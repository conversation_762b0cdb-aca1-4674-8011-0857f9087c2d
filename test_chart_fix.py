#!/usr/bin/env python3
"""
测试图表修复
"""

import requests
import json
import time

def test_chart_generation():
    """测试图表生成"""
    base_url = "http://127.0.0.1:8083"
    
    print("🧪 测试图表生成修复")
    print("=" * 50)
    
    # 测试所有图表类型
    chart_tests = [
        {
            "name": "柱状图",
            "data": {
                "chart_type": "bar",
                "table": "film",
                "x_column": "rating",
                "y_column": "rental_rate",
                "title": "电影评级与租赁费用分析"
            }
        },
        {
            "name": "饼状图", 
            "data": {
                "chart_type": "pie",
                "table": "film",
                "label_column": "rating",
                "value_column": "rental_rate",
                "title": "电影评级分布"
            }
        },
        {
            "name": "趋势图",
            "data": {
                "chart_type": "line",
                "table": "payment",
                "x_column": "payment_date",
                "y_column": "amount",
                "title": "支付金额趋势"
            }
        },
        {
            "name": "散点图",
            "data": {
                "chart_type": "scatter",
                "table": "film",
                "x_column": "rental_rate",
                "y_column": "replacement_cost",
                "title": "租赁费用与替换成本关系"
            }
        },
        {
            "name": "热力图",
            "data": {
                "chart_type": "heatmap",
                "table": "payment",
                "x_column": "HOUR(payment_date)",
                "y_column": "DAYOFWEEK(payment_date)",
                "title": "支付时间热力图"
            }
        }
    ]
    
    for test in chart_tests:
        print(f"\n📊 测试{test['name']}...")
        try:
            response = requests.post(f"{base_url}/api/generate-chart", 
                                   json=test['data'], 
                                   timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    chart_data = result.get('chart_data', {})
                    data_count = len(chart_data.get('data', []))
                    has_image = 'image_base64' in chart_data
                    
                    print(f"   ✅ {test['name']}生成成功")
                    print(f"   📈 数据点数: {data_count}")
                    print(f"   🖼️ 包含图片: {'是' if has_image else '否'}")
                else:
                    print(f"   ❌ {test['name']}生成失败: {result.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 无法连接到服务器 {base_url}")
            break
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    test_chart_generation()
