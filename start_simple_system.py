#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动简化版MySQL数据分析系统
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def start_simple_bridge():
    """启动简化HTTP桥接器"""
    print("1. 启动简化HTTP桥接器...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, "simple_bridge_test.py"],
            cwd=Path.cwd(),
            creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == "win32" else 0
        )
        
        print(f"   进程ID: {process.pid}")
        print("   ✓ 简化HTTP桥接器已启动")
        return process
        
    except Exception as e:
        print(f"   ✗ 启动失败: {e}")
        return None

def start_web_server():
    """启动Web服务器"""
    print("\n2. 启动Web服务器...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, "web_server.py", "8081"],
            cwd=Path.cwd(),
            creationflags=subprocess.CREATE_NEW_CONSOLE if sys.platform == "win32" else 0
        )
        
        print(f"   进程ID: {process.pid}")
        print("   ✓ Web服务器已启动")
        return process
        
    except Exception as e:
        print(f"   ✗ 启动失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("\n3. 打开浏览器...")
    
    try:
        webbrowser.open("http://127.0.0.1:8081")
        print("   ✓ 浏览器已打开")
    except Exception as e:
        print(f"   ✗ 打开浏览器失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("启动简化版MySQL数据分析系统")
    print("=" * 60)
    
    # 启动简化HTTP桥接器
    bridge_process = start_simple_bridge()
    if not bridge_process:
        print("HTTP桥接器启动失败，退出")
        return
    
    # 等待一下
    time.sleep(3)
    
    # 启动Web服务器
    web_process = start_web_server()
    if not web_process:
        print("Web服务器启动失败")
    
    # 等待一下
    time.sleep(3)
    
    # 打开浏览器
    open_browser()
    
    print("\n" + "=" * 60)
    print("系统启动完成!")
    print("=" * 60)
    print("📊 简化HTTP桥接器: http://127.0.0.1:8083")
    print("🌐 Web界面: http://127.0.0.1:8081")
    print("=" * 60)
    print("\n使用说明:")
    print("1. Web界面已在浏览器中打开")
    print("2. 点击'连接服务器'连接到HTTP桥接器")
    print("3. 开始使用数据分析功能（使用模拟数据）")
    print("\n注意: 这是简化版本，使用模拟数据进行演示")
    print("如需连接真实数据库，请使用完整版本")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
