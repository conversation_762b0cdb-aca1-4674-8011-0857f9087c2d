#!/usr/bin/env python3
"""
测试服务器连接
"""

import requests
import json

def test_http_connection():
    """测试HTTP连接"""
    print("🔍 测试服务器连接...")
    
    # 测试基本连接
    urls_to_test = [
        "http://127.0.0.1:9000",
        "http://127.0.0.1:9000/mcp/",
        "http://127.0.0.1:9000/health",
        "http://127.0.0.1:9000/mcp/health"
    ]
    
    for url in urls_to_test:
        try:
            print(f"\n🔗 测试: {url}")
            response = requests.get(url, timeout=5)
            print(f"  状态码: {response.status_code}")
            print(f"  响应: {response.text[:200]}...")
        except Exception as e:
            print(f"  ❌ 失败: {e}")

def test_mcp_protocol():
    """测试MCP协议"""
    print("\n🔍 测试MCP协议...")
    
    try:
        url = "http://127.0.0.1:9000/mcp/"
        
        # 测试MCP初始化
        init_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print(f"📤 发送初始化请求到: {url}")
        response = requests.post(
            url,
            json=init_payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📥 状态码: {response.status_code}")
        print(f"📥 响应: {response.text}")
        
    except Exception as e:
        print(f"❌ MCP协议测试失败: {e}")

if __name__ == "__main__":
    test_http_connection()
    test_mcp_protocol()
