#!/usr/bin/env python3
"""
正确的MCP客户端实现
基于FastMCP文档的正确用法
"""

import asyncio
import json
from typing import Dict, Any
from fastmcp import Client

class CorrectMCPClient:
    """正确的MCP客户端"""
    
    def __init__(self, server_url: str = "http://127.0.0.1:9000/mcp/"):
        self.server_url = server_url
    
    async def test_connection(self):
        """测试连接"""
        print(f"🔗 连接到: {self.server_url}")
        
        try:
            # 使用正确的async with语法
            async with Client(self.server_url) as client:
                print("✅ 连接成功")
                
                # 测试ping
                await client.ping()
                print("✅ Ping成功")
                
                # 列出工具
                tools = await client.list_tools()
                print(f"📋 可用工具: {[tool.name for tool in tools]}")
                
                return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None):
        """调用工具"""
        if arguments is None:
            arguments = {}
        
        print(f"🔄 调用工具: {tool_name}")
        print(f"📝 参数: {arguments}")
        
        try:
            async with Client(self.server_url) as client:
                result = await client.call_tool(tool_name, arguments)
                
                print("✅ 调用成功")
                print(f"📊 结果: {json.dumps(result.data, indent=2, ensure_ascii=False)}")
                return result.data
                
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    async def demo_analysis(self):
        """演示分析"""
        print("🎯 工业数据分析演示")
        print("=" * 40)
        
        # 测试连接
        if not await self.test_connection():
            return
        
        print("\n📊 开始演示分析...")
        
        # 1. 测试hello工具
        print("\n1️⃣ 测试Hello工具")
        await self.call_tool("hello")
        
        # 2. 测试数据库
        print("\n2️⃣ 测试数据库连接")
        await self.call_tool("test_database")
        
        print("\n🎉 演示完成！")
    
    async def interactive_mode(self):
        """交互模式"""
        print("🎯 正确的MCP客户端")
        print("=" * 40)
        
        # 测试连接
        if not await self.test_connection():
            return
        
        print("\n💡 可用命令:")
        print("  1. hello - 测试hello工具")
        print("  2. db - 测试数据库")
        print("  3. tools - 列出工具")
        print("  4. quit - 退出")
        
        while True:
            try:
                command = input("\n📝 请输入命令: ").strip().lower()
                
                if command in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                elif command == 'hello':
                    await self.call_tool("hello")
                
                elif command == 'db':
                    await self.call_tool("test_database")
                
                elif command == 'tools':
                    await self.list_tools()
                
                else:
                    print("❌ 未知命令，请输入有效命令")
            
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    async def list_tools(self):
        """列出工具"""
        try:
            async with Client(self.server_url) as client:
                tools = await client.list_tools()
                print("\n📋 可用工具:")
                for tool in tools:
                    print(f"  🔧 {tool.name}: {tool.description}")
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")

async def main():
    """主函数"""
    import sys
    
    client = CorrectMCPClient()
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        await client.demo_analysis()
    else:
        await client.interactive_mode()

if __name__ == "__main__":
    asyncio.run(main())
