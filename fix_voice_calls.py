#!/usr/bin/env python3
"""
修复语音调用的脚本
"""

import re

def fix_voice_calls():
    """修复mysql_analysis_mcp.py中的语音调用"""
    
    # 读取文件
    with open('mysql_analysis_mcp.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有直接的voice_manager.speak调用
    pattern = r'(\s+)voice_manager\.speak\('
    
    # 替换为带检查的调用
    def replace_voice_call(match):
        indent = match.group(1)
        return f'{indent}if voice_manager:\n{indent}    voice_manager.speak('
    
    # 执行替换
    new_content = re.sub(pattern, replace_voice_call, content)
    
    # 保存文件
    with open('mysql_analysis_mcp.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 语音调用修复完成")

if __name__ == "__main__":
    fix_voice_calls()
