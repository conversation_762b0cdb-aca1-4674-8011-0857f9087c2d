#!/usr/bin/env python3
"""
MySQL数据库分析MCP服务器
提供数据统计、异常分析、提醒、图表生成和趋势分析功能
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import warnings
warnings.filterwarnings("ignore")

# 第三方库导入
try:
    import mysql.connector
    from mysql.connector import pooling
    import pandas as pd
    import numpy as np
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import seaborn as sns
    from scipy import stats
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.utils import PlotlyJSONEncoder
    import openai
    from openai import AsyncOpenAI
    import base64
    from io import BytesIO
    from fastmcp import FastMCP, Context
    import pyttsx3
    import speech_recognition as sr
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请运行: pip install mysql-connector-python pandas numpy matplotlib seaborn scipy plotly fastmcp pyttsx3 speechrecognition")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# OpenAI配置
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_MODEL = "gpt-4o-mini"

class AIAssistant:
    """OpenAI GPT-4o-mini AI助手"""

    def __init__(self):
        self.client = AsyncOpenAI(api_key=OPENAI_API_KEY)
        self.model = OPENAI_MODEL

    async def analyze(self, prompt: str, temperature: float = 0.3, max_tokens: int = 800) -> str:
        """使用GPT-4o-mini进行分析"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的数据分析专家，擅长MySQL数据库分析、异常检测、趋势分析和业务洞察。请用简洁专业的中文回答，重点关注业务价值和可操作性。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            return f"AI分析暂时不可用: {str(e)}"

# 创建AI助手实例
ai_assistant = AIAssistant()

# 创建MCP服务器实例
mcp = FastMCP(
    name="MySQL数据库分析服务器",
    dependencies=[
        "mysql-connector-python>=8.0.0",
        "pandas>=1.5.0", 
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "scipy>=1.7.0",
        "plotly>=5.0.0",
        "pyttsx3>=2.90",
        "speechrecognition>=3.8.0"
    ]
)

class DatabaseConfig:
    """数据库配置类"""
    def __init__(self, config_file: str = "db_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载数据库配置"""
        default_config = {
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "",
            "database": "test",
            "pool_name": "mysql_pool",
            "pool_size": 10,
            "pool_reset_session": True,
            "charset": "utf8mb4",
            "use_unicode": True,
            "autocommit": True
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    default_config.update(config)
            except Exception as e:
                logger.warning(f"加载配置文件失败: {e}, 使用默认配置")
        else:
            # 创建默认配置文件
            self.save_config(default_config)
        
        return default_config
    
    def save_config(self, config: Dict[str, Any]):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")

class DatabaseManager:
    """数据库连接管理器"""
    def __init__(self, config: DatabaseConfig):
        self.config = config.config
        self.pool = None
        self.init_pool()
    
    def init_pool(self):
        """初始化连接池"""
        try:
            pool_config = {
                'pool_name': self.config['pool_name'],
                'pool_size': self.config['pool_size'],
                'pool_reset_session': self.config['pool_reset_session'],
                'host': self.config['host'],
                'port': self.config['port'],
                'user': self.config['user'],
                'password': self.config['password'],
                'database': self.config['database'],
                'charset': self.config['charset'],
                'use_unicode': self.config['use_unicode'],
                'autocommit': self.config['autocommit']
            }
            
            self.pool = pooling.MySQLConnectionPool(**pool_config)
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            return self.pool.get_connection()
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
    
    def execute_query(self, query: str, params: tuple = None) -> pd.DataFrame:
        """执行查询并返回DataFrame"""
        conn = None
        try:
            conn = self.get_connection()
            df = pd.read_sql(query, conn, params=params)
            return df
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            logger.error(f"执行更新失败: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

class VoiceManager:
    """语音管理器"""
    def __init__(self):
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None
        self.init_voice()
    
    def init_voice(self):
        """初始化语音功能"""
        try:
            # 初始化TTS引擎（快速初始化）
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 150)  # 语速
            self.tts_engine.setProperty('volume', 0.8)  # 音量

            # 延迟初始化语音识别（避免阻塞启动）
            self.recognizer = None
            self.microphone = None

            logger.info("语音功能初始化成功（TTS已启用，语音识别延迟初始化）")
        except Exception as e:
            logger.warning(f"语音功能初始化失败: {e}")
            self.tts_engine = None
    
    def speak(self, text: str):
        """文本转语音"""
        try:
            if self.tts_engine:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
        except Exception as e:
            logger.error(f"语音播放失败: {e}")
    
    def _init_speech_recognition(self):
        """延迟初始化语音识别"""
        if self.recognizer is None:
            try:
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()

                # 调整环境噪音
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)

                logger.info("语音识别初始化成功")
            except Exception as e:
                logger.error(f"语音识别初始化失败: {e}")
                self.recognizer = None
                self.microphone = None

    def listen(self, timeout: int = 5) -> str:
        """语音转文本"""
        try:
            # 延迟初始化语音识别
            self._init_speech_recognition()

            if not self.recognizer or not self.microphone:
                logger.warning("语音识别未初始化")
                return ""

            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=timeout)

            # 使用Google语音识别
            text = self.recognizer.recognize_google(audio, language='zh-CN')
            return text
        except sr.WaitTimeoutError:
            return ""
        except Exception as e:
            logger.error(f"语音识别失败: {e}")
            return ""

# 全局实例 - 延迟初始化
db_config = None
db_manager = None
voice_manager = None

def init_services():
    """初始化服务"""
    global db_config, db_manager, voice_manager
    if db_config is None:
        db_config = DatabaseConfig()
        db_manager = DatabaseManager(db_config)
        voice_manager = VoiceManager()

class DataAnalyzer:
    """数据分析器"""
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def calculate_statistics(self, table: str, column: str, 
                           start_time: str = None, end_time: str = None,
                           time_column: str = 'created_at') -> Dict[str, Any]:
        """计算统计数据"""
        try:
            # 构建查询条件
            where_clause = ""
            params = []
            
            if start_time and end_time:
                where_clause = f"WHERE {time_column} BETWEEN %s AND %s"
                params = [start_time, end_time]
            elif start_time:
                where_clause = f"WHERE {time_column} >= %s"
                params = [start_time]
            elif end_time:
                where_clause = f"WHERE {time_column} <= %s"
                params = [end_time]
            
            # 执行统计查询
            query = f"""
            SELECT 
                COUNT({column}) as count,
                SUM({column}) as sum,
                AVG({column}) as average,
                MIN({column}) as minimum,
                MAX({column}) as maximum,
                STDDEV({column}) as std_dev,
                VARIANCE({column}) as variance
            FROM {table} {where_clause}
            """
            
            result = self.db.execute_query(query, tuple(params))
            
            if not result.empty:
                stats = result.iloc[0].to_dict()
                # 处理None值
                for key, value in stats.items():
                    if pd.isna(value):
                        stats[key] = 0
                return stats
            else:
                return {}
                
        except Exception as e:
            logger.error(f"统计计算失败: {e}")
            raise

    def detect_anomalies(self, table: str, column: str,
                        threshold_multiplier: float = 2.0,
                        method: str = 'zscore',
                        start_time: str = None,
                        end_time: str = None,
                        time_column: str = 'created_at',
                        business_rule: str = None) -> Dict[str, Any]:
        """检测异常数据"""
        try:
            # 获取数据 - 尝试不同的主键列名
            primary_key_candidates = ['id', f'{table}_id', 'payment_id', 'rental_id', 'customer_id']
            primary_key = None

            # 查找合适的主键列
            for pk in primary_key_candidates:
                try:
                    test_query = f"SELECT {pk} FROM {table} LIMIT 1"
                    self.db.execute_query(test_query)
                    primary_key = pk
                    break
                except:
                    continue

            if not primary_key:
                primary_key = '1 as row_id'  # 使用常量作为行标识

            # 构建查询语句
            select_columns = f"{column}, {primary_key} as id"

            # 如果有时间列，也选择它
            if time_column and time_column != primary_key and time_column != column:
                try:
                    # 测试时间列是否存在
                    test_query = f"SELECT {time_column} FROM {table} LIMIT 1"
                    self.db.execute_query(test_query)
                    select_columns += f", {time_column}"
                except:
                    time_column = None

            query = f"SELECT {select_columns} FROM {table} WHERE {column} IS NOT NULL"

            # 添加时间范围条件
            if start_time and time_column:
                query += f" AND {time_column} >= '{start_time}'"
            if end_time and time_column:
                query += f" AND {time_column} <= '{end_time}'"

            df = self.db.execute_query(query)

            if df.empty:
                return {"anomalies": [], "total_count": 0, "anomaly_count": 0, "anomaly_rate": 0}

            values = df[column].values
            anomalies = []

            if method == 'zscore':
                # Z-score方法
                z_scores = np.abs(stats.zscore(values))
                anomaly_indices = np.where(z_scores > threshold_multiplier)[0]

                for idx in anomaly_indices:
                    anomaly_data = {
                        'id': int(df.iloc[idx]['id']),
                        'value': float(values[idx]),
                        'z_score': float(z_scores[idx]),
                        'reason': f'Z-score ({z_scores[idx]:.2f}) 超过阈值 {threshold_multiplier}'
                    }

                    # 添加时间戳
                    if time_column and time_column in df.columns:
                        anomaly_data['timestamp'] = str(df.iloc[idx][time_column])

                    anomalies.append(anomaly_data)

            elif method == 'iqr':
                # IQR方法
                Q1 = np.percentile(values, 25)
                Q3 = np.percentile(values, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold_multiplier * IQR
                upper_bound = Q3 + threshold_multiplier * IQR

                anomaly_indices = np.where((values < lower_bound) | (values > upper_bound))[0]

                for idx in anomaly_indices:
                    anomaly_data = {
                        'id': int(df.iloc[idx]['id']),
                        'value': float(values[idx]),
                        'reason': f'IQR异常: 值 {values[idx]:.2f} 超出范围 [{lower_bound:.2f}, {upper_bound:.2f}]'
                    }

                    if time_column and time_column in df.columns:
                        anomaly_data['timestamp'] = str(df.iloc[idx][time_column])

                    anomalies.append(anomaly_data)

            elif method == 'business_rule' and business_rule:
                # 业务规则检测
                try:
                    # 简单的业务规则解析（支持基本的比较操作）
                    for idx, value in enumerate(values):
                        rule_result = self._evaluate_business_rule(business_rule, value)
                        if rule_result:
                            anomaly_data = {
                                'id': int(df.iloc[idx]['id']),
                                'value': float(value),
                                'reason': f'违反业务规则: {business_rule}'
                            }

                            if time_column and time_column in df.columns:
                                anomaly_data['timestamp'] = str(df.iloc[idx][time_column])

                            anomalies.append(anomaly_data)
                except Exception as e:
                    return {"error": f"业务规则解析失败: {str(e)}"}

            elif method == 'threshold':
                # 阈值范围检测
                anomaly_indices = np.where(values > threshold_multiplier)[0]

                for idx in anomaly_indices:
                    anomaly_data = {
                        'id': int(df.iloc[idx]['id']),
                        'value': float(values[idx]),
                        'reason': f'超过阈值: {values[idx]:.2f} > {threshold_multiplier}'
                    }

                    if time_column and time_column in df.columns:
                        anomaly_data['timestamp'] = str(df.iloc[idx][time_column])

                    anomalies.append(anomaly_data)

            elif method == 'percentile':
                # 百分位数检测
                percentile = 100 - threshold_multiplier  # 将阈值转换为百分位数
                threshold_value = np.percentile(values, percentile)
                anomaly_indices = np.where(values > threshold_value)[0]

                for idx in anomaly_indices:
                    anomaly_data = {
                        'id': int(df.iloc[idx]['id']),
                        'value': float(values[idx]),
                        'reason': f'超过{percentile:.1f}百分位数: {values[idx]:.2f} > {threshold_value:.2f}'
                    }

                    if time_column and time_column in df.columns:
                        anomaly_data['timestamp'] = str(df.iloc[idx][time_column])

                    anomalies.append(anomaly_data)

            elif method == 'iqr':
                # IQR方法
                Q1 = np.percentile(values, 25)
                Q3 = np.percentile(values, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold_multiplier * IQR
                upper_bound = Q3 + threshold_multiplier * IQR

                anomaly_indices = np.where((values < lower_bound) | (values > upper_bound))[0]

                for idx in anomaly_indices:
                    value = values[idx]
                    if value < lower_bound:
                        reason = f'值 ({value:.2f}) 低于下界 ({lower_bound:.2f})'
                    else:
                        reason = f'值 ({value:.2f}) 高于上界 ({upper_bound:.2f})'

                    anomalies.append({
                        'id': int(df.iloc[idx]['id']),
                        'value': float(value),
                        'bound_info': {'lower': lower_bound, 'upper': upper_bound},
                        'reason': reason
                    })

            return {
                "anomalies": anomalies,
                "total_count": len(values),
                "anomaly_count": len(anomalies),
                "anomaly_rate": len(anomalies) / len(values) * 100
            }

        except Exception as e:
            logger.error(f"异常检测失败: {e}")
            raise

    def _evaluate_business_rule(self, rule: str, value: float) -> bool:
        """评估业务规则"""
        try:
            # 简单的业务规则解析
            # 支持格式: "amount > 1000", "amount < 0.01", "amount > 1000 OR amount < 0.01"
            rule = rule.replace('amount', str(value))
            rule = rule.replace('OR', 'or').replace('AND', 'and')
            rule = rule.replace('>', ' > ').replace('<', ' < ').replace('=', ' == ')

            # 安全评估表达式
            allowed_chars = set('0123456789.+-*/() <>==!andor ')
            if all(c in allowed_chars or c.isspace() for c in rule):
                return eval(rule)
            else:
                return False
        except:
            return False

class AlertManager:
    """提醒管理器"""
    def __init__(self, db_manager: DatabaseManager, voice_manager: VoiceManager):
        self.db = db_manager
        self.voice = voice_manager
        self.alerts = []

    def add_alert(self, alert_config: Dict[str, Any]) -> str:
        """添加提醒配置"""
        alert_id = f"alert_{len(self.alerts) + 1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        alert_config['id'] = alert_id
        alert_config['created_at'] = datetime.now()
        alert_config['active'] = True
        self.alerts.append(alert_config)
        return alert_id

    def check_alerts(self) -> List[Dict[str, Any]]:
        """检查所有提醒条件"""
        triggered_alerts = []

        for alert in self.alerts:
            if not alert['active']:
                continue

            try:
                if alert['type'] == 'value_threshold':
                    if self._check_value_threshold(alert):
                        triggered_alerts.append(alert)
                elif alert['type'] == 'time_based':
                    if self._check_time_based(alert):
                        triggered_alerts.append(alert)
            except Exception as e:
                logger.error(f"检查提醒失败: {e}")

        return triggered_alerts

    def _check_value_threshold(self, alert: Dict[str, Any]) -> bool:
        """检查数值阈值提醒"""
        query = f"SELECT {alert['column']} FROM {alert['table']} ORDER BY id DESC LIMIT 1"
        result = self.db.execute_query(query)

        if not result.empty:
            current_value = result.iloc[0][alert['column']]
            threshold = alert['threshold']
            operator = alert['operator']

            if operator == '>':
                return current_value > threshold
            elif operator == '<':
                return current_value < threshold
            elif operator == '>=':
                return current_value >= threshold
            elif operator == '<=':
                return current_value <= threshold
            elif operator == '==':
                return current_value == threshold

        return False

    def _check_time_based(self, alert: Dict[str, Any]) -> bool:
        """检查时间基础提醒"""
        now = datetime.now()
        alert_time = alert.get('alert_time')

        if alert_time:
            alert_datetime = datetime.strptime(alert_time, '%Y-%m-%d %H:%M:%S')
            return now >= alert_datetime

        return False

class ChartGenerator:
    """图表生成器 - 使用matplotlib"""
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

    def _save_chart_to_base64(self, fig) -> str:
        """将matplotlib图表保存为base64字符串"""
        buffer = BytesIO()
        fig.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        buffer.close()
        return image_base64

    def generate_bar_chart(self, table: str, x_column: str, y_column: str,
                          title: str = "柱状图", limit: int = 20) -> str:
        """生成柱状图"""
        try:
            query = f"SELECT {x_column}, {y_column} FROM {table} LIMIT {limit}"
            df = self.db.execute_query(query)

            if df.empty:
                return json.dumps({
                    "chart_type": "bar",
                    "title": title,
                    "error": "没有找到数据",
                    "data": []
                }, ensure_ascii=False)

            # 使用matplotlib生成图表
            fig, ax = plt.subplots(figsize=(10, 6))

            # 如果数据太多，进行聚合
            if len(df) > limit:
                df = df.groupby(x_column)[y_column].mean().reset_index()

            bars = ax.bar(df[x_column], df[y_column], color=sns.color_palette("husl", len(df)))
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel(x_column, fontsize=12)
            ax.set_ylabel(y_column, fontsize=12)

            # 旋转x轴标签以避免重叠
            plt.xticks(rotation=45, ha='right')

            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.2f}', ha='center', va='bottom')

            plt.tight_layout()

            # 转换为base64
            chart_base64 = self._save_chart_to_base64(fig)
            plt.close(fig)

            # 返回包含图表数据的JSON
            result = {
                "chart_type": "bar",
                "title": title,
                "image_base64": chart_base64,
                "data": df.to_dict('records')
            }

            return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"生成柱状图失败: {e}")
            raise

    def generate_pie_chart(self, table: str, label_column: str, value_column: str,
                          title: str = "饼状图") -> str:
        """生成饼状图"""
        try:
            query = f"SELECT {label_column}, SUM({value_column}) as total FROM {table} GROUP BY {label_column}"
            df = self.db.execute_query(query)

            if df.empty:
                return json.dumps({
                    "chart_type": "pie",
                    "title": title,
                    "error": "没有找到数据",
                    "data": []
                }, ensure_ascii=False)

            # 使用matplotlib生成饼状图
            fig, ax = plt.subplots(figsize=(10, 8))

            colors = sns.color_palette("husl", len(df))
            wedges, texts, autotexts = ax.pie(df['total'], labels=df[label_column],
                                            autopct='%1.1f%%', colors=colors,
                                            startangle=90)

            ax.set_title(title, fontsize=16, fontweight='bold')

            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

            plt.tight_layout()

            # 转换为base64
            chart_base64 = self._save_chart_to_base64(fig)
            plt.close(fig)

            # 返回包含图表数据的JSON
            result = {
                "chart_type": "pie",
                "title": title,
                "image_base64": chart_base64,
                "data": df.to_dict('records')
            }

            return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"生成饼状图失败: {e}")
            raise

    def generate_line_chart(self, table: str, x_column: str, y_column: str,
                           title: str = "趋势图", time_range: str = None) -> str:
        """生成趋势线图"""
        try:
            where_clause = ""
            if time_range:
                where_clause = f"WHERE {x_column} >= DATE_SUB(NOW(), INTERVAL {time_range})"

            query = f"SELECT DATE({x_column}) as date_group, AVG({y_column}) as avg_value FROM {table} {where_clause} GROUP BY DATE({x_column}) ORDER BY DATE({x_column}) LIMIT 100"
            df = self.db.execute_query(query)

            # 如果指定时间范围没有数据，尝试使用全部数据
            if df.empty and time_range:
                logger.info(f"指定时间范围 {time_range} 没有数据，使用全部数据")
                query = f"SELECT DATE({x_column}) as date_group, AVG({y_column}) as avg_value FROM {table} GROUP BY DATE({x_column}) ORDER BY DATE({x_column}) LIMIT 100"
                df = self.db.execute_query(query)
                title = f"{title} (全部数据)"

            if df.empty:
                return json.dumps({
                    "chart_type": "line",
                    "title": title,
                    "error": "没有找到数据",
                    "data": []
                }, ensure_ascii=False)

            # 使用matplotlib生成趋势图
            fig, ax = plt.subplots(figsize=(12, 6))

            ax.plot(df['date_group'], df['avg_value'], marker='o', linewidth=2, markersize=4)
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel(x_column, fontsize=12)
            ax.set_ylabel(y_column, fontsize=12)

            # 格式化x轴日期
            if 'date' in x_column.lower() or 'time' in x_column.lower():
                plt.xticks(rotation=45)

            ax.grid(True, alpha=0.3)
            plt.tight_layout()

            # 转换为base64
            chart_base64 = self._save_chart_to_base64(fig)
            plt.close(fig)

            # 转换日期为字符串以便JSON序列化
            df_dict = df.to_dict('records')
            for record in df_dict:
                if 'date_group' in record and record['date_group']:
                    record['date_group'] = str(record['date_group'])

            # 返回包含图表数据的JSON
            result = {
                "chart_type": "line",
                "title": title,
                "image_base64": chart_base64,
                "data": df_dict,
                "time_range": time_range
            }

            return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"生成趋势图失败: {e}")
            raise

    def generate_scatter_chart(self, table: str, x_column: str, y_column: str,
                              title: str = "散点图", limit: int = 100) -> str:
        """生成散点图"""
        try:
            query = f"SELECT {x_column}, {y_column} FROM {table} WHERE {x_column} IS NOT NULL AND {y_column} IS NOT NULL LIMIT {limit}"
            df = self.db.execute_query(query)

            if df.empty:
                return json.dumps({
                    "chart_type": "scatter",
                    "title": title,
                    "error": "没有找到数据",
                    "data": []
                }, ensure_ascii=False)

            # 使用matplotlib生成散点图
            fig, ax = plt.subplots(figsize=(10, 8))

            scatter = ax.scatter(df[x_column], df[y_column], alpha=0.6, s=50,
                               c=range(len(df)), cmap='viridis')
            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel(x_column, fontsize=12)
            ax.set_ylabel(y_column, fontsize=12)

            # 添加颜色条
            plt.colorbar(scatter, ax=ax, label='数据点序号')

            # 添加网格
            ax.grid(True, alpha=0.3)
            plt.tight_layout()

            # 转换为base64
            chart_base64 = self._save_chart_to_base64(fig)
            plt.close(fig)

            # 准备数据用于前端ECharts
            chart_data = []
            for _, row in df.iterrows():
                chart_data.append({
                    "x": float(row[x_column]) if pd.notnull(row[x_column]) else 0,
                    "y": float(row[y_column]) if pd.notnull(row[y_column]) else 0,
                    "value": [float(row[x_column]) if pd.notnull(row[x_column]) else 0,
                             float(row[y_column]) if pd.notnull(row[y_column]) else 0]
                })

            # 返回包含图表数据的JSON
            result = {
                "chart_type": "scatter",
                "title": title,
                "image_base64": chart_base64,
                "data": chart_data
            }

            return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"生成散点图失败: {e}")
            raise

    def generate_heatmap_chart(self, table: str, x_column: str, y_column: str,
                              title: str = "热力图", value_column: str = "COUNT(*)") -> str:
        """生成热力图"""
        try:
            # 构建查询，聚合数据
            if value_column == "COUNT(*)":
                query = f"""
                SELECT {x_column} as x_val, {y_column} as y_val, COUNT(*) as value
                FROM {table}
                WHERE {x_column} IS NOT NULL AND {y_column} IS NOT NULL
                GROUP BY {x_column}, {y_column}
                ORDER BY {x_column}, {y_column}
                """
            else:
                query = f"""
                SELECT {x_column} as x_val, {y_column} as y_val, AVG({value_column}) as value
                FROM {table}
                WHERE {x_column} IS NOT NULL AND {y_column} IS NOT NULL AND {value_column} IS NOT NULL
                GROUP BY {x_column}, {y_column}
                ORDER BY {x_column}, {y_column}
                """

            df = self.db.execute_query(query)

            if df.empty:
                return json.dumps({
                    "chart_type": "heatmap",
                    "title": title,
                    "error": "没有找到数据",
                    "data": []
                }, ensure_ascii=False)

            # 创建数据透视表
            pivot_df = df.pivot(index='y_val', columns='x_val', values='value').fillna(0)

            # 使用matplotlib生成热力图
            fig, ax = plt.subplots(figsize=(12, 8))

            im = ax.imshow(pivot_df.values, cmap='YlOrRd', aspect='auto')

            # 设置坐标轴标签
            ax.set_xticks(range(len(pivot_df.columns)))
            ax.set_yticks(range(len(pivot_df.index)))
            ax.set_xticklabels(pivot_df.columns)
            ax.set_yticklabels(pivot_df.index)

            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

            # 添加数值标注
            for i in range(len(pivot_df.index)):
                for j in range(len(pivot_df.columns)):
                    text = ax.text(j, i, f'{pivot_df.values[i, j]:.1f}',
                                 ha="center", va="center", color="black", fontsize=8)

            ax.set_title(title, fontsize=16, fontweight='bold')
            ax.set_xlabel(x_column, fontsize=12)
            ax.set_ylabel(y_column, fontsize=12)

            # 添加颜色条
            plt.colorbar(im, ax=ax, label='数值')
            plt.tight_layout()

            # 转换为base64
            chart_base64 = self._save_chart_to_base64(fig)
            plt.close(fig)

            # 准备数据用于前端ECharts
            chart_data = []
            for _, row in df.iterrows():
                chart_data.append({
                    "x": str(row['x_val']),
                    "y": str(row['y_val']),
                    "value": float(row['value']) if pd.notnull(row['value']) else 0
                })

            # 返回包含图表数据的JSON
            result = {
                "chart_type": "heatmap",
                "title": title,
                "image_base64": chart_base64,
                "data": chart_data
            }

            return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"生成热力图失败: {e}")
            raise

# 创建分析器和管理器实例 - 延迟初始化
data_analyzer = None
alert_manager = None
chart_generator = None

def init_analyzers():
    """初始化分析器"""
    global data_analyzer, alert_manager, chart_generator
    if data_analyzer is None:
        init_services()
        data_analyzer = DataAnalyzer(db_manager)
        alert_manager = AlertManager(db_manager, voice_manager)
        chart_generator = ChartGenerator(db_manager)

# ==================== MCP工具定义 ====================

@mcp.tool
async def get_database_statistics(
    table: str,
    column: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    time_column: str = "created_at",
    ctx: Context = None
) -> Dict[str, Any]:
    """
    获取数据库表的统计信息

    Args:
        table: 表名
        column: 要统计的列名
        start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
        end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
        time_column: 时间列名，默认为created_at

    Returns:
        包含统计信息的字典
    """
    try:
        init_analyzers()  # 确保服务已初始化

        if ctx:
            await ctx.info(f"正在计算表 {table} 列 {column} 的统计信息...")

        stats = data_analyzer.calculate_statistics(
            table=table,
            column=column,
            start_time=start_time,
            end_time=end_time,
            time_column=time_column
        )

        # 语音播报结果
        if stats:
            summary = f"表{table}的{column}列统计完成，共{stats.get('count', 0)}条记录，平均值为{stats.get('average', 0):.2f}"
            if voice_manager:

                voice_manager.speak(summary)

        if ctx:
            await ctx.info(f"统计计算完成，共 {stats.get('count', 0)} 条记录")

        return {
            "success": True,
            "data": stats,
            "table": table,
            "column": column,
            "time_range": {
                "start": start_time,
                "end": end_time
            }
        }

    except Exception as e:
        error_msg = f"统计计算失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:

            voice_manager.speak("统计计算失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def detect_data_anomalies(
    table: str,
    column: str,
    method: str = "zscore",
    threshold: float = 2.0,
    start_time: str = None,
    end_time: str = None,
    time_column: str = "created_at",
    business_rule: str = None,
    enable_ai_analysis: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    检测数据异常并提供AI分析

    Args:
        table: 表名
        column: 要检测的列名
        method: 检测方法 ('zscore', 'iqr', 'business_rule', 'threshold', 'percentile')
        threshold: 阈值倍数
        start_time: 开始时间 (可选)
        end_time: 结束时间 (可选)
        time_column: 时间列名 (默认 'created_at')
        business_rule: 业务规则 (当method为'business_rule'时使用)
        enable_ai_analysis: 是否启用AI分析 (默认 True)

    Returns:
        异常检测结果，包含AI分析
    """
    try:
        if ctx:
            await ctx.info(f"正在检测表 {table} 列 {column} 的异常数据...")

        result = data_analyzer.detect_anomalies(
            table=table,
            column=column,
            threshold_multiplier=threshold,
            method=method,
            start_time=start_time,
            end_time=end_time,
            time_column=time_column,
            business_rule=business_rule
        )

        # AI异常原因分析
        ai_analysis = None
        if enable_ai_analysis and ctx and result.get('anomalies'):
            try:
                if ctx:
                    await ctx.info("正在进行AI异常原因分析...")

                # 准备异常数据摘要（取前5个异常）
                anomalies_sample = result['anomalies'][:5]
                anomaly_summary = {
                    "table": table,
                    "column": column,
                    "method": method,
                    "threshold": threshold,
                    "total_anomalies": len(result['anomalies']),
                    "anomaly_rate": result.get('anomaly_rate', 0),
                    "sample_anomalies": anomalies_sample
                }

                # 使用OpenAI分析异常原因
                ai_prompt = f"""作为数据分析专家，请分析以下数据库异常检测结果的可能业务原因：

数据表: {table}
字段: {column}
检测方法: {method}
阈值: {threshold}
异常总数: {anomaly_summary['total_anomalies']}
异常率: {anomaly_summary['anomaly_rate']:.2f}%

异常样本数据:
{json.dumps(anomalies_sample, indent=2, ensure_ascii=False)}

请从以下角度分析：
1. 可能的业务原因（如促销活动、系统故障、数据录入错误等）
2. 异常模式特征
3. 建议的后续行动
4. 风险评估

请用简洁专业的中文回答，重点关注实际业务场景。"""

                ai_analysis = await ai_assistant.analyze(ai_prompt, temperature=0.3, max_tokens=800)

                if ctx:
                    await ctx.info("AI异常分析完成")

            except Exception as ai_error:
                logger.warning(f"AI分析失败: {ai_error}")
                ai_analysis = f"AI分析暂时不可用: {str(ai_error)}"

        # 语音播报结果
        anomaly_count = result.get('anomaly_count', 0)
        total_count = result.get('total_count', 0)
        if voice_manager:
            if anomaly_count > 0:
                summary = f"检测到{anomaly_count}个异常数据，异常率为{result.get('anomaly_rate', 0):.2f}%"
                if ai_analysis:
                    summary += "，已完成AI原因分析"
                voice_manager.speak(summary)
            else:
                voice_manager.speak("未检测到异常数据")

        if ctx:
            await ctx.info(f"异常检测完成，发现 {anomaly_count} 个异常数据")

        # 构建返回结果
        final_result = {
            "success": True,
            "data": result,
            "method": method,
            "threshold": threshold
        }

        if ai_analysis:
            final_result["ai_analysis"] = ai_analysis

        return final_result

    except Exception as e:
        error_msg = f"异常检测失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:
            voice_manager.speak("异常检测失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def create_alert(
    alert_type: str,
    table: str,
    column: str = None,
    threshold: float = None,
    operator: str = ">",
    alert_time: str = None,
    description: str = "",
    enable_ai_priority: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    创建智能数据提醒

    Args:
        alert_type: 提醒类型 ('value_threshold' 或 'time_based')
        table: 表名
        column: 列名 (value_threshold类型必需)
        threshold: 阈值 (value_threshold类型必需)
        operator: 比较操作符 ('>', '<', '>=', '<=', '==')
        alert_time: 提醒时间 (time_based类型必需, YYYY-MM-DD HH:MM:SS)
        description: 提醒描述
        enable_ai_priority: 是否启用AI优先级评估 (默认 True)

    Returns:
        创建结果，包含AI优先级评估
    """
    try:
        if ctx:
            await ctx.info(f"正在创建 {alert_type} 类型的提醒...")

        alert_config = {
            "type": alert_type,
            "table": table,
            "description": description
        }

        if alert_type == "value_threshold":
            if not column or threshold is None:
                raise ValueError("value_threshold类型需要指定column和threshold")
            alert_config.update({
                "column": column,
                "threshold": threshold,
                "operator": operator
            })
        elif alert_type == "time_based":
            if not alert_time:
                raise ValueError("time_based类型需要指定alert_time")
            alert_config["alert_time"] = alert_time

        # AI优先级评估
        ai_priority_assessment = None
        if enable_ai_priority and ctx:
            try:
                if ctx:
                    await ctx.info("正在进行AI优先级评估...")

                # 获取相关数据统计信息
                context_data = {}
                if alert_type == "value_threshold" and column:
                    try:
                        stats_query = f"""
                        SELECT
                            AVG({column}) as avg_value,
                            STDDEV({column}) as std_dev,
                            MIN({column}) as min_value,
                            MAX({column}) as max_value,
                            COUNT(*) as total_records
                        FROM {table}
                        WHERE {column} IS NOT NULL
                        """
                        stats_df = db_manager.execute_query(stats_query)
                        if not stats_df.empty:
                            context_data = stats_df.iloc[0].to_dict()
                    except Exception as stats_error:
                        logger.warning(f"获取统计信息失败: {stats_error}")

                # 使用LLM评估优先级
                ai_prompt = f"""作为数据监控专家，请评估以下数据提醒的优先级和重要性：

提醒配置:
- 类型: {alert_type}
- 数据表: {table}
- 字段: {column or '无'}
- 阈值: {threshold or '无'}
- 操作符: {operator}
- 描述: {description or '无描述'}

数据背景信息:
{json.dumps(context_data, indent=2, ensure_ascii=False) if context_data else '暂无统计信息'}

请从以下角度评估：
1. 紧急程度 (高/中/低)
2. 业务影响程度 (高/中/低)
3. 误报风险评估
4. 建议的响应时间
5. 监控频率建议
6. 潜在的业务风险

请提供简洁的评估结果，包含具体的优先级建议和理由。"""

                ai_priority_assessment = await ai_assistant.analyze(ai_prompt, temperature=0.2, max_tokens=600)

                if ctx:
                    await ctx.info("AI优先级评估完成")

            except Exception as ai_error:
                logger.warning(f"AI优先级评估失败: {ai_error}")
                ai_priority_assessment = f"AI优先级评估暂时不可用: {str(ai_error)}"

        # 添加AI评估结果到配置中
        if ai_priority_assessment:
            alert_config["ai_priority_assessment"] = ai_priority_assessment

        alert_id = alert_manager.add_alert(alert_config)

        # 语音确认
        voice_message = f"提醒创建成功，编号为{alert_id}"
        if ai_priority_assessment:
            voice_message += "，已完成AI优先级评估"
        if voice_manager:
            voice_manager.speak(voice_message)

        if ctx:
            await ctx.info(f"提醒创建成功，ID: {alert_id}")

        # 构建返回结果
        result = {
            "success": True,
            "alert_id": alert_id,
            "config": alert_config
        }

        if ai_priority_assessment:
            result["ai_priority_assessment"] = ai_priority_assessment

        return result

    except Exception as e:
        error_msg = f"创建提醒失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:
            voice_manager.speak("创建提醒失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def check_alerts(
    enable_ai_analysis: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    智能检查所有提醒条件

    Args:
        enable_ai_analysis: 是否启用AI分析 (默认 True)

    Returns:
        触发的提醒列表，包含AI分析
    """
    try:
        if ctx:
            await ctx.info("正在检查所有提醒条件...")

        triggered_alerts = alert_manager.check_alerts()

        # AI智能分析触发的提醒
        ai_alert_analysis = None
        if enable_ai_analysis and ctx and triggered_alerts:
            try:
                if ctx:
                    await ctx.info("正在进行AI提醒分析...")

                # 准备提醒摘要
                alert_summary = {
                    "total_triggered": len(triggered_alerts),
                    "total_monitored": len(alert_manager.alerts),
                    "triggered_alerts": triggered_alerts[:5]  # 取前5个触发的提醒
                }

                # 使用LLM分析提醒情况
                ai_prompt = f"""作为数据监控专家，请分析当前触发的数据提醒情况：

监控概况:
- 总监控项目: {alert_summary['total_monitored']}
- 触发提醒数量: {alert_summary['total_triggered']}

触发的提醒详情:
{json.dumps(alert_summary['triggered_alerts'], indent=2, ensure_ascii=False)}

请从以下角度分析：
1. 提醒严重程度评估
2. 是否存在关联性问题（多个提醒可能指向同一根本原因）
3. 建议的处理优先级排序
4. 可能的系统性问题识别
5. immediate action recommendations（立即行动建议）

请提供简洁专业的分析，重点关注可操作的建议。"""

                ai_alert_analysis = await ai_assistant.analyze(ai_prompt, temperature=0.3, max_tokens=700)

                if ctx:
                    await ctx.info("AI提醒分析完成")

            except Exception as ai_error:
                logger.warning(f"AI提醒分析失败: {ai_error}")
                ai_alert_analysis = f"AI提醒分析暂时不可用: {str(ai_error)}"

        # 语音播报触发的提醒
        if triggered_alerts:
            for alert in triggered_alerts:
                message = f"提醒触发: {alert.get('description', '未命名提醒')}"
                if voice_manager:
                    voice_manager.speak(message)

            # 播报AI分析摘要
            if ai_alert_analysis and voice_manager:
                voice_manager.speak(f"已触发{len(triggered_alerts)}个提醒，AI分析已完成")
        else:
            if voice_manager:
                voice_manager.speak("所有监控项目正常，无提醒触发")

        if ctx:
            await ctx.info(f"检查完成，触发了 {len(triggered_alerts)} 个提醒")

        # 构建返回结果
        result = {
            "success": True,
            "triggered_alerts": triggered_alerts,
            "total_checked": len(alert_manager.alerts),
            "alert_summary": {
                "total_triggered": len(triggered_alerts),
                "total_monitored": len(alert_manager.alerts),
                "trigger_rate": len(triggered_alerts) / len(alert_manager.alerts) * 100 if alert_manager.alerts else 0
            }
        }

        if ai_alert_analysis:
            result["ai_alert_analysis"] = ai_alert_analysis

        return result

    except Exception as e:
        error_msg = f"检查提醒失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@mcp.tool
async def comprehensive_ai_analysis(
    table: str,
    columns: list = None,
    analysis_type: str = "full",
    time_range_days: int = 30,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    综合AI数据分析

    Args:
        table: 表名
        columns: 要分析的列名列表 (可选，默认分析所有数值列)
        analysis_type: 分析类型 ('full', 'anomaly', 'trend', 'summary')
        time_range_days: 分析时间范围（天数）

    Returns:
        综合AI分析结果
    """
    try:
        if ctx:
            await ctx.info(f"正在进行表 {table} 的综合AI分析...")

        # 获取表结构信息
        table_info_query = f"DESCRIBE {table}"
        table_structure = db_manager.execute_query(table_info_query)

        # 获取数据概览
        overview_query = f"""
        SELECT
            COUNT(*) as total_records,
            COUNT(DISTINCT DATE(created_at)) as date_range_days
        FROM {table}
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL {time_range_days} DAY)
        """
        overview_data = db_manager.execute_query(overview_query)

        # 如果没有指定列，自动识别数值列
        if not columns:
            numeric_columns = []
            for _, row in table_structure.iterrows():
                column_type = row['Type'].lower()
                if any(t in column_type for t in ['int', 'decimal', 'float', 'double', 'numeric']):
                    numeric_columns.append(row['Field'])
            columns = numeric_columns[:5]  # 限制最多5列

        # 收集各列的统计信息
        analysis_data = {
            "table": table,
            "time_range_days": time_range_days,
            "total_records": int(overview_data.iloc[0]['total_records']) if not overview_data.empty else 0,
            "columns_analyzed": columns,
            "column_statistics": {}
        }

        for column in columns:
            try:
                stats_query = f"""
                SELECT
                    AVG({column}) as avg_value,
                    STDDEV({column}) as std_dev,
                    MIN({column}) as min_value,
                    MAX({column}) as max_value,
                    COUNT(DISTINCT {column}) as unique_values,
                    COUNT(CASE WHEN {column} IS NULL THEN 1 END) as null_count
                FROM {table}
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL {time_range_days} DAY)
                """
                stats_df = db_manager.execute_query(stats_query)
                if not stats_df.empty:
                    analysis_data["column_statistics"][column] = stats_df.iloc[0].to_dict()
            except Exception as col_error:
                logger.warning(f"分析列 {column} 失败: {col_error}")

        # 使用LLM进行综合分析
        ai_prompt = f"""作为高级数据分析师，请对以下数据库表进行综合分析：

数据概览:
{json.dumps(analysis_data, indent=2, ensure_ascii=False, default=str)}

请根据分析类型 "{analysis_type}" 提供以下内容：

1. 数据质量评估
   - 数据完整性分析
   - 异常值识别
   - 数据分布特征

2. 业务洞察
   - 关键指标表现
   - 趋势模式识别
   - 潜在业务机会

3. 风险识别
   - 数据异常风险
   - 业务风险点
   - 监控建议

4. 行动建议
   - 优化建议
   - 监控重点
   - 进一步分析方向

请提供专业、可操作的分析报告，重点关注业务价值和实际应用。"""

        if ctx:
            await ctx.info("正在生成AI综合分析报告...")

        ai_analysis_report = await ai_assistant.analyze(ai_prompt, temperature=0.4, max_tokens=1500)

        # 语音播报
        if voice_manager:
            voice_manager.speak(f"表{table}的综合AI分析已完成，共分析{len(columns)}个字段")

        if ctx:
            await ctx.info("综合AI分析完成")

        return {
            "success": True,
            "analysis_data": analysis_data,
            "ai_analysis_report": ai_analysis_report,
            "analysis_type": analysis_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        error_msg = f"综合AI分析失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:
            voice_manager.speak("综合AI分析失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def generate_bar_chart(
    table: str,
    x_column: str,
    y_column: str,
    title: str = "柱状图",
    limit: int = 20,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    生成柱状图

    Args:
        table: 表名
        x_column: X轴列名
        y_column: Y轴列名
        title: 图表标题
        limit: 数据限制条数

    Returns:
        图表JSON数据
    """
    try:
        init_analyzers()  # 确保分析器已初始化

        if ctx:
            await ctx.info(f"正在生成表 {table} 的柱状图...")

        chart_result = chart_generator.generate_bar_chart(
            table=table,
            x_column=x_column,
            y_column=y_column,
            title=title,
            limit=limit
        )

        if voice_manager:
            voice_manager.speak("柱状图生成完成")

        if ctx:
            await ctx.info("柱状图生成完成")

        # 解析返回的JSON字符串
        chart_data = json.loads(chart_result)

        return {
            "success": True,
            "chart_type": chart_data.get("chart_type", "bar"),
            "chart_data": chart_data,
            "title": title,
            "has_image": "image_base64" in chart_data
        }

    except Exception as e:
        error_msg = f"生成柱状图失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:

            voice_manager.speak("柱状图生成失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def generate_pie_chart(
    table: str,
    label_column: str,
    value_column: str,
    title: str = "饼状图",
    ctx: Context = None
) -> Dict[str, Any]:
    """
    生成饼状图

    Args:
        table: 表名
        label_column: 标签列名
        value_column: 数值列名
        title: 图表标题

    Returns:
        图表JSON数据
    """
    try:
        init_analyzers()  # 确保分析器已初始化

        if ctx:
            await ctx.info(f"正在生成表 {table} 的饼状图...")

        chart_result = chart_generator.generate_pie_chart(
            table=table,
            label_column=label_column,
            value_column=value_column,
            title=title
        )

        if voice_manager:
            voice_manager.speak("饼状图生成完成")

        if ctx:
            await ctx.info("饼状图生成完成")

        # 解析返回的JSON字符串
        chart_data = json.loads(chart_result)

        return {
            "success": True,
            "chart_type": chart_data.get("chart_type", "pie"),
            "chart_data": chart_data,
            "title": title,
            "has_image": "image_base64" in chart_data
        }

    except Exception as e:
        error_msg = f"生成饼状图失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:

            voice_manager.speak("饼状图生成失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def generate_trend_chart(
    table: str,
    x_column: str,
    y_column: str,
    title: str = "趋势图",
    time_range: str = "7 DAY",
    ctx: Context = None
) -> Dict[str, Any]:
    """
    生成趋势图

    Args:
        table: 表名
        x_column: X轴列名（通常是时间列）
        y_column: Y轴列名
        title: 图表标题
        time_range: 时间范围 (如 "7 DAY", "1 MONTH", "1 YEAR")

    Returns:
        图表JSON数据
    """
    try:
        init_analyzers()  # 确保分析器已初始化

        if ctx:
            await ctx.info(f"正在生成表 {table} 的趋势图...")

        chart_result = chart_generator.generate_line_chart(
            table=table,
            x_column=x_column,
            y_column=y_column,
            title=title,
            time_range=time_range
        )

        if voice_manager:
            voice_manager.speak("趋势图生成完成")

        if ctx:
            await ctx.info("趋势图生成完成")

        # 解析返回的JSON字符串
        chart_data = json.loads(chart_result)

        return {
            "success": True,
            "chart_type": chart_data.get("chart_type", "line"),
            "chart_data": chart_data,
            "title": title,
            "time_range": time_range,
            "has_image": "image_base64" in chart_data
        }

    except Exception as e:
        error_msg = f"生成趋势图失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:

            voice_manager.speak("趋势图生成失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def generate_scatter_chart(
    table: str,
    x_column: str,
    y_column: str,
    title: str = "散点图",
    limit: int = 100,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    生成散点图

    Args:
        table: 表名
        x_column: X轴列名
        y_column: Y轴列名
        title: 图表标题
        limit: 数据限制条数

    Returns:
        图表JSON数据
    """
    try:
        init_analyzers()  # 确保分析器已初始化

        if ctx:
            await ctx.info(f"正在生成表 {table} 的散点图...")

        chart_result = chart_generator.generate_scatter_chart(
            table=table,
            x_column=x_column,
            y_column=y_column,
            title=title,
            limit=limit
        )

        if voice_manager:
            voice_manager.speak("散点图生成完成")

        if ctx:
            await ctx.info("散点图生成完成")

        # 解析返回的JSON字符串
        chart_data = json.loads(chart_result)

        return {
            "success": True,
            "chart_type": chart_data.get("chart_type", "scatter"),
            "chart_data": chart_data,
            "title": title,
            "has_image": "image_base64" in chart_data
        }

    except Exception as e:
        error_msg = f"生成散点图失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:
            voice_manager.speak("散点图生成失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def generate_heatmap_chart(
    table: str,
    x_column: str,
    y_column: str,
    title: str = "热力图",
    value_column: str = "COUNT(*)",
    ctx: Context = None
) -> Dict[str, Any]:
    """
    生成热力图

    Args:
        table: 表名
        x_column: X轴列名
        y_column: Y轴列名
        title: 图表标题
        value_column: 数值列名（默认为COUNT(*)）

    Returns:
        图表JSON数据
    """
    try:
        init_analyzers()  # 确保分析器已初始化

        if ctx:
            await ctx.info(f"正在生成表 {table} 的热力图...")

        chart_result = chart_generator.generate_heatmap_chart(
            table=table,
            x_column=x_column,
            y_column=y_column,
            title=title,
            value_column=value_column
        )

        if voice_manager:
            voice_manager.speak("热力图生成完成")

        if ctx:
            await ctx.info("热力图生成完成")

        # 解析返回的JSON字符串
        chart_data = json.loads(chart_result)

        return {
            "success": True,
            "chart_type": chart_data.get("chart_type", "heatmap"),
            "chart_data": chart_data,
            "title": title,
            "has_image": "image_base64" in chart_data
        }

    except Exception as e:
        error_msg = f"生成热力图失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:
            voice_manager.speak("热力图生成失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def analyze_data_trend(
    table: str,
    time_column: str,
    value_column: str,
    period: str = "day",
    forecast_days: int = 7,
    enable_ai_insights: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    分析数据趋势并提供AI洞察

    Args:
        table: 表名
        time_column: 时间列名
        value_column: 数值列名
        period: 聚合周期 ('hour', 'day', 'week', 'month')
        forecast_days: 预测天数
        enable_ai_insights: 是否启用AI洞察分析 (默认 True)

    Returns:
        趋势分析结果，包含AI洞察
    """
    try:
        if ctx:
            await ctx.info(f"正在分析表 {table} 的数据趋势...")

        # 根据周期构建聚合查询
        date_format = {
            'hour': '%Y-%m-%d %H:00:00',
            'day': '%Y-%m-%d',
            'week': '%Y-%u',
            'month': '%Y-%m'
        }

        query = f"""
        SELECT
            DATE_FORMAT({time_column}, '{date_format[period]}') as period,
            AVG({value_column}) as avg_value,
            COUNT(*) as count,
            MIN({value_column}) as min_value,
            MAX({value_column}) as max_value,
            STDDEV({value_column}) as std_dev
        FROM {table}
        WHERE {time_column} >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY period
        ORDER BY period
        """

        df = db_manager.execute_query(query)

        if df.empty:
            return {"success": False, "error": "没有找到数据"}

        # 计算趋势指标
        values = df['avg_value'].values
        trend_slope = np.polyfit(range(len(values)), values, 1)[0]

        # 计算更多统计指标
        avg_value = float(np.mean(values))
        volatility = float(np.std(values))
        growth_rate = ((values[-1] - values[0]) / values[0] * 100) if values[0] != 0 else 0

        # 简单的线性预测
        last_value = values[-1]
        predictions = []
        for i in range(1, forecast_days + 1):
            predicted_value = last_value + (trend_slope * i)
            predictions.append({
                'day': i,
                'predicted_value': float(predicted_value)
            })

        # 趋势分析
        if trend_slope > 0:
            trend_direction = "上升"
            trend_description = f"数据呈上升趋势，斜率为 {trend_slope:.4f}"
        elif trend_slope < 0:
            trend_direction = "下降"
            trend_description = f"数据呈下降趋势，斜率为 {trend_slope:.4f}"
        else:
            trend_direction = "平稳"
            trend_description = "数据趋势相对平稳"

        # AI洞察分析
        ai_insights = None
        if enable_ai_insights and ctx:
            try:
                if ctx:
                    await ctx.info("正在进行AI趋势洞察分析...")

                # 准备趋势数据摘要
                trend_summary = {
                    "table": table,
                    "column": value_column,
                    "period": period,
                    "data_points": len(values),
                    "trend_direction": trend_direction,
                    "slope": float(trend_slope),
                    "growth_rate": float(growth_rate),
                    "avg_value": avg_value,
                    "volatility": volatility,
                    "recent_data": df.tail(7).to_dict('records')  # 最近7个数据点
                }

                # 使用LLM进行洞察分析
                ai_prompt = f"""作为数据分析专家，请对以下数据趋势进行深度洞察分析：

数据表: {table}
字段: {value_column}
分析周期: {period}
数据点数量: {trend_summary['data_points']}
趋势方向: {trend_direction}
增长率: {growth_rate:.2f}%
平均值: {avg_value:.2f}
波动性: {volatility:.2f}

最近数据趋势:
{json.dumps(trend_summary['recent_data'], indent=2, ensure_ascii=False)}

请从以下角度提供洞察：
1. 趋势模式分析（季节性、周期性、异常波动）
2. 业务含义解读（可能的业务驱动因素）
3. 风险与机会识别
4. 预测可信度评估
5. 建议的监控重点和行动建议

请用专业但易懂的中文回答，重点关注业务价值和可操作性。"""

                ai_insights = await ai_assistant.analyze(ai_prompt, temperature=0.4, max_tokens=1000)

                if ctx:
                    await ctx.info("AI趋势洞察分析完成")

            except Exception as ai_error:
                logger.warning(f"AI洞察分析失败: {ai_error}")
                ai_insights = f"AI洞察分析暂时不可用: {str(ai_error)}"

        # 语音播报
        voice_summary = f"数据趋势分析完成，{trend_direction}趋势，增长率{growth_rate:.1f}%"
        if ai_insights:
            voice_summary += "，已完成AI洞察分析"
        if voice_manager:
            voice_manager.speak(voice_summary)

        if ctx:
            await ctx.info(f"趋势分析完成，趋势方向: {trend_direction}")

        # 构建返回结果
        result = {
            "success": True,
            "trend_analysis": {
                "direction": trend_direction,
                "slope": float(trend_slope),
                "description": trend_description,
                "data_points": len(values),
                "growth_rate": float(growth_rate),
                "avg_value": avg_value,
                "volatility": volatility
            },
            "historical_data": df.to_dict('records'),
            "predictions": predictions,
            "period": period
        }

        if ai_insights:
            result["ai_insights"] = ai_insights

        return result

    except Exception as e:
        error_msg = f"趋势分析失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:
            voice_manager.speak("趋势分析失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def voice_command(
    command: str = "",
    listen_for_input: bool = False,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    语音交互功能

    Args:
        command: 要播报的文本命令
        listen_for_input: 是否监听语音输入

    Returns:
        语音交互结果
    """
    try:
        result = {"success": True}

        if command:
            if ctx:
                await ctx.info(f"正在播报: {command}")
            if voice_manager:

                voice_manager.speak(command)
            result["spoken"] = command

        if listen_for_input:
            if ctx:
                await ctx.info("正在监听语音输入...")

            if voice_manager:


                voice_manager.speak("请说话")
            recognized_text = voice_manager.listen(timeout=10)

            if recognized_text:
                result["recognized"] = recognized_text
                if ctx:
                    await ctx.info(f"识别到: {recognized_text}")
            else:
                result["recognized"] = ""
                if ctx:
                    await ctx.warning("未识别到语音输入")

        return result

    except Exception as e:
        error_msg = f"语音交互失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@mcp.tool
async def get_database_info(ctx: Context = None) -> Dict[str, Any]:
    """
    获取数据库基本信息

    Returns:
        数据库信息
    """
    try:
        init_services()  # 确保服务已初始化

        if ctx:
            await ctx.info("正在获取数据库信息...")

        # 获取所有表信息
        tables_query = "SHOW TABLES"
        tables_df = db_manager.execute_query(tables_query)
        tables = tables_df.iloc[:, 0].tolist() if not tables_df.empty else []

        # 获取数据库状态
        status_query = "SHOW STATUS LIKE 'Threads_connected'"
        status_df = db_manager.execute_query(status_query)

        db_info = {
            "database": db_config.config['database'],
            "host": db_config.config['host'],
            "port": db_config.config['port'],
            "tables": tables,
            "table_count": len(tables),
            "connection_status": "connected" if not status_df.empty else "disconnected"
        }

        # 语音播报
        if voice_manager:

            voice_manager.speak(f"数据库{db_info['database']}连接正常，共有{len(tables)}个表")

        if ctx:
            await ctx.info(f"数据库信息获取完成，共 {len(tables)} 个表")

        return {
            "success": True,
            "database_info": db_info
        }

    except Exception as e:
        error_msg = f"获取数据库信息失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:

            voice_manager.speak("获取数据库信息失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def execute_custom_sql(
    sql: str,
    params: Optional[List[Any]] = None,
    datasource: str = "default",
    ctx: Context = None
) -> Dict[str, Any]:
    """
    执行自定义SQL查询（类似开源mcp-mysql-server的功能）

    Args:
        sql: SQL查询语句
        params: 查询参数列表
        datasource: 数据源名称（预留，当前使用默认数据源）

    Returns:
        查询结果
    """
    try:
        if ctx:
            await ctx.info(f"正在执行自定义SQL查询...")
            await ctx.debug(f"SQL: {sql}")

        # 安全检查 - 防止危险操作
        dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE']
        sql_upper = sql.upper().strip()

        for keyword in dangerous_keywords:
            if sql_upper.startswith(keyword):
                error_msg = f"出于安全考虑，不允许执行 {keyword} 操作"
                if ctx:
                    await ctx.warning(error_msg)
                if voice_manager:

                    voice_manager.speak("SQL执行被拒绝，存在安全风险")
                return {"success": False, "error": error_msg}

        # 执行查询
        if params:
            df = db_manager.execute_query(sql, tuple(params))
        else:
            df = db_manager.execute_query(sql)

        # 转换结果
        if df.empty:
            result = {"rows": [], "columns": [], "row_count": 0}
        else:
            result = {
                "rows": df.to_dict('records'),
                "columns": df.columns.tolist(),
                "row_count": len(df)
            }

        # 语音反馈
        if voice_manager:

            voice_manager.speak(f"SQL查询完成，返回{result['row_count']}条记录")

        if ctx:
            await ctx.info(f"SQL查询完成，返回 {result['row_count']} 条记录")

        return {
            "success": True,
            "data": result,
            "sql": sql,
            "datasource": datasource
        }

    except Exception as e:
        error_msg = f"SQL执行失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager:

            voice_manager.speak("SQL执行失败")
        return {"success": False, "error": error_msg}

# ==================== MCP资源定义 ====================

@mcp.resource("resource://database/config")
async def get_database_config() -> Dict[str, Any]:
    """获取数据库配置信息"""
    return {
        "host": db_config.config['host'],
        "port": db_config.config['port'],
        "database": db_config.config['database'],
        "pool_size": db_config.config['pool_size'],
        "charset": db_config.config['charset']
    }

@mcp.resource("resource://alerts/active")
async def get_active_alerts() -> List[Dict[str, Any]]:
    """获取活跃的提醒列表"""
    return [alert for alert in alert_manager.alerts if alert.get('active', False)]

@mcp.resource("resource://system/status")
async def get_system_status() -> Dict[str, Any]:
    """获取系统状态"""
    return {
        "timestamp": datetime.now().isoformat(),
        "database_connected": True,
        "voice_enabled": voice_manager.tts_engine is not None,
        "speech_recognition_enabled": voice_manager.recognizer is not None,
        "active_alerts": len([a for a in alert_manager.alerts if a.get('active', False)]),
        "version": "1.0.0"
    }

# ==================== 主函数 ====================

async def main():
    """主函数"""
    try:
        logger.info("启动MySQL数据库分析MCP服务器...")
        logger.info("MCP服务器启动中...")

    except Exception as e:
        logger.error(f"启动失败: {e}")
        raise

if __name__ == "__main__":
    try:
        print("=" * 60)
        print("MySQL数据库分析MCP服务器")
        print("=" * 60)
        print("正在启动服务器...")
        print(f"监听地址: http://127.0.0.1:9000/mcp/")
        print("按 Ctrl+C 停止服务器")
        print("=" * 60)

        # 运行MCP服务器
        mcp.run(
            transport="http",
            host="127.0.0.1",
            port=9000,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n服务器已停止")
        logger.info("服务器已停止")
    except Exception as e:
        print(f"\n服务器运行错误: {e}")
        logger.error(f"服务器运行错误: {e}")
