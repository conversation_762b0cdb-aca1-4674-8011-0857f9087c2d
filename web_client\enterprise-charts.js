// 企业级图表生成器 - 基于ECharts
class EnterpriseChartGenerator {
    constructor() {
        this.themes = {
            light: 'macarons',
            dark: 'dark',
            corporate: 'corporate'
        };
        this.currentTheme = 'light';
        this.charts = new Map(); // 存储图表实例

        // 设置ECharts全局字体配置，避免影响页面字体
        this.initializeEChartsConfig();
    }

    // 初始化ECharts配置，隔离字体影响
    initializeEChartsConfig() {
        if (typeof echarts !== 'undefined') {
            // 确保ECharts不影响全局字体
            echarts.registerTheme('isolated', {
                textStyle: {
                    fontFamily: 'Microsoft YaHei, Arial, sans-serif'
                }
            });
        }
    }

    // 设置主题
    setTheme(theme) {
        this.currentTheme = theme;
        // 重新渲染所有图表
        this.charts.forEach((chart, id) => {
            this.refreshChart(id);
        });
    }

    // 创建柱状图
    createBarChart(containerId, data, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`容器 ${containerId} 不存在`);
            return null;
        }

        // 销毁已存在的图表
        if (this.charts.has(containerId)) {
            this.charts.get(containerId).dispose();
        }

        const chart = echarts.init(container, this.currentTheme);
        
        // 紧凑模式配置
        const isCompact = options.compact || false;

        const defaultOptions = {
            title: {
                text: options.title || '柱状图',
                left: 'center',
                textStyle: {
                    fontSize: isCompact ? 14 : 18,
                    fontWeight: 'bold',
                    fontFamily: 'Microsoft YaHei, Arial, sans-serif'
                },
                top: isCompact ? 10 : 20
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function(params) {
                    const item = params[0];
                    return `${item.name}<br/>${item.seriesName}: ${item.value}`;
                }
            },
            legend: isCompact ? null : {
                data: [options.seriesName || '数值'],
                top: 40
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: isCompact ? '8%' : '3%',
                top: isCompact ? '20%' : '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.map(item => item.name || item.x),
                axisLabel: {
                    rotate: data.length > 8 ? 45 : 0
                }
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '数值'
            },
            series: [{
                name: options.seriesName || '数值',
                type: 'bar',
                data: data.map(item => ({
                    value: item.value || item.y,
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: options.startColor || '#83bff6'},
                            {offset: 0.5, color: options.midColor || '#188df0'},
                            {offset: 1, color: options.endColor || '#188df0'}
                        ])
                    }
                })),
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                animationDelay: function (idx) {
                    return idx * 100;
                }
            }],
            animationEasing: 'elasticOut',
            animationDelayUpdate: function (idx) {
                return idx * 5;
            }
        };

        chart.setOption(defaultOptions);
        this.charts.set(containerId, chart);

        // 添加点击事件
        chart.on('click', (params) => {
            if (options.onClick) {
                options.onClick(params);
            }
        });

        // 响应式
        window.addEventListener('resize', () => {
            chart.resize();
        });

        // 图表创建完成后，触发字体保护
        setTimeout(() => {
            if (window.app && typeof window.app.ensurePageFontIntegrity === 'function') {
                window.app.ensurePageFontIntegrity();
            }
        }, 100);

        return chart;
    }

    // 创建饼状图
    createPieChart(containerId, data, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`容器 ${containerId} 不存在`);
            return null;
        }

        if (this.charts.has(containerId)) {
            this.charts.get(containerId).dispose();
        }

        const chart = echarts.init(container, this.currentTheme);
        
        const defaultOptions = {
            title: {
                text: options.title || '饼状图',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    fontFamily: 'Microsoft YaHei, Arial, sans-serif'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                top: 'middle'
            },
            series: [{
                name: options.seriesName || '数据',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['60%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '30',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: data.map(item => ({
                    value: item.value || item.y,
                    name: item.name || item.label
                })),
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: function (idx) {
                    return Math.random() * 200;
                }
            }]
        };

        chart.setOption(defaultOptions);
        this.charts.set(containerId, chart);

        // 添加点击事件
        chart.on('click', (params) => {
            if (options.onClick) {
                options.onClick(params);
            }
        });

        window.addEventListener('resize', () => {
            chart.resize();
        });

        // 图表创建完成后，触发字体保护
        setTimeout(() => {
            if (window.app && typeof window.app.ensurePageFontIntegrity === 'function') {
                window.app.ensurePageFontIntegrity();
            }
        }, 100);

        return chart;
    }

    // 创建趋势图
    createLineChart(containerId, data, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`容器 ${containerId} 不存在`);
            return null;
        }

        if (this.charts.has(containerId)) {
            this.charts.get(containerId).dispose();
        }

        const chart = echarts.init(container, this.currentTheme);
        
        const defaultOptions = {
            title: {
                text: options.title || '趋势图',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    fontFamily: 'Microsoft YaHei, Arial, sans-serif'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            },
            legend: {
                data: [options.seriesName || '趋势'],
                top: 40
            },
            toolbox: {
                feature: {
                    saveAsImage: {
                        title: '保存为图片'
                    },
                    dataZoom: {
                        title: {
                            zoom: '区域缩放',
                            back: '区域缩放还原'
                        }
                    },
                    restore: {
                        title: '还原'
                    }
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: data.map(item => item.name || item.x)
            },
            yAxis: {
                type: 'value',
                name: options.yAxisName || '数值'
            },
            series: [{
                name: options.seriesName || '趋势',
                type: 'line',
                stack: options.area ? 'Total' : null,
                smooth: options.smooth !== false,  // 默认开启平滑
                lineStyle: {
                    width: 3
                },
                showSymbol: false,
                areaStyle: options.area ? {
                    opacity: 0.8,
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {offset: 0, color: options.startColor || 'rgba(128, 255, 165, 0.8)'},
                        {offset: 1, color: options.endColor || 'rgba(1, 191, 236, 0.1)'}
                    ])
                } : null,
                emphasis: {
                    focus: 'series'
                },
                data: data.map(item => item.value || item.y)
            }]
        };

        chart.setOption(defaultOptions);
        this.charts.set(containerId, chart);

        // 添加点击事件
        chart.on('click', (params) => {
            if (options.onClick) {
                options.onClick(params);
            }
        });

        window.addEventListener('resize', () => {
            chart.resize();
        });

        // 图表创建完成后，触发字体保护
        setTimeout(() => {
            if (window.app && typeof window.app.ensurePageFontIntegrity === 'function') {
                window.app.ensurePageFontIntegrity();
            }
        }, 100);

        return chart;
    }

    // 刷新图表
    refreshChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.resize();
        }
    }

    // 销毁图表
    destroyChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.dispose();
            this.charts.delete(containerId);
        }
    }

    // 销毁所有图表
    destroyAllCharts() {
        this.charts.forEach((chart, id) => {
            chart.dispose();
        });
        this.charts.clear();
    }

    // 导出图表
    exportChart(containerId, type = 'png') {
        const chart = this.charts.get(containerId);
        if (chart) {
            const url = chart.getDataURL({
                type: type,
                pixelRatio: 2,
                backgroundColor: '#fff'
            });
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = `chart_${containerId}_${Date.now()}.${type}`;
            link.href = url;
            link.click();
        }
    }
}

// 企业级图表生成器将在页面加载时初始化
