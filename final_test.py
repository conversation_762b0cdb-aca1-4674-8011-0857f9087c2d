#!/usr/bin/env python3
"""
最终测试脚本 - 测试修复后的功能
"""

import asyncio
import sys

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    sys.exit(1)

async def final_test():
    """最终测试"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("🔧 测试修复后的功能")
    print("=" * 50)
    
    try:
        async with Client(server_url) as client:
            
            # 测试异常检测（修复后）
            print("🔍 测试异常检测（修复后）...")
            result = await client.call_tool("detect_data_anomalies", {
                "table": "payment",
                "column": "amount",
                "method": "zscore",
                "threshold": 2.0
            })
            if result.data.get('success'):
                anomaly_data = result.data.get('data', {})
                print(f"   ✅ 成功！")
                print(f"   总记录数: {anomaly_data.get('total_count')}")
                print(f"   异常记录数: {anomaly_data.get('anomaly_count')}")
                print(f"   异常率: {anomaly_data.get('anomaly_rate', 0):.2f}%")
                
                # 显示前几个异常
                anomalies = anomaly_data.get('anomalies', [])[:3]
                if anomalies:
                    print("   前3个异常数据:")
                    for i, anomaly in enumerate(anomalies, 1):
                        print(f"     {i}. 金额: ${anomaly.get('value', 0):.2f}, 原因: {anomaly.get('reason', 'N/A')}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 测试图表生成（安装kaleido后）
            print("\n📊 测试图表生成（安装kaleido后）...")
            result = await client.call_tool("generate_bar_chart", {
                "table": "film",
                "x_column": "rating",
                "y_column": "rental_rate",
                "title": "电影评级与租赁费用分析",
                "limit": 10
            })
            if result.data.get('success'):
                print(f"   ✅ 成功！")
                print(f"   图表类型: {result.data.get('chart_type')}")
                print(f"   图表标题: {result.data.get('title')}")
                print("   图表已生成并保存为PNG文件")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 测试饼状图
            print("\n🥧 测试饼状图生成...")
            result = await client.call_tool("generate_pie_chart", {
                "table": "film",
                "label_column": "rating",
                "value_column": "rental_rate",
                "title": "电影评级分布"
            })
            if result.data.get('success'):
                print(f"   ✅ 成功！")
                print(f"   图表类型: {result.data.get('chart_type')}")
                print("   饼状图已生成")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 测试提醒检查
            print("\n⏰ 测试提醒检查...")
            result = await client.call_tool("check_alerts")
            if result.data.get('success'):
                triggered = result.data.get('triggered_alerts', [])
                total_checked = result.data.get('total_checked', 0)
                print(f"   ✅ 成功！")
                print(f"   检查了 {total_checked} 个提醒")
                print(f"   触发了 {len(triggered)} 个提醒")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 测试更复杂的统计查询
            print("\n📈 测试复杂统计查询...")
            result = await client.call_tool("get_database_statistics", {
                "table": "rental",
                "column": "rental_id",
                "start_time": "2005-05-01 00:00:00",
                "end_time": "2005-06-01 00:00:00",
                "time_column": "rental_date"
            })
            if result.data.get('success'):
                stats = result.data.get('data', {})
                print(f"   ✅ 成功！")
                print(f"   2005年5月租赁统计:")
                print(f"   租赁次数: {stats.get('count')}")
                print(f"   平均租赁ID: {stats.get('average', 0):.2f}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            print("\n" + "=" * 50)
            print("🎉 最终测试完成！")
            print("\n📋 您的MySQL数据分析MCP系统现在具备以下完整功能:")
            print("✅ 实时数据库连接和查询")
            print("✅ 统计分析（按时间段求和、求平均）")
            print("✅ 智能异常检测（Z-score和IQR方法）")
            print("✅ 数据可视化（柱状图、饼状图、趋势图）")
            print("✅ 智能提醒系统（时间和数值阈值）")
            print("✅ 语音交互功能")
            print("✅ 自定义SQL查询")
            print("✅ 趋势分析和预测")
            
            print("\n🌟 系统特点:")
            print("• 本地部署，无需互联网连接")
            print("• 支持大数据量实时处理")
            print("• 连接池优化，高性能")
            print("• 语音对话交互")
            print("• 多种异常检测算法")
            print("• 丰富的数据可视化")
            
            print(f"\n🔗 MCP服务器地址: {server_url}")
            print("状态: 🟢 运行中")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(final_test())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}")
