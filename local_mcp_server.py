#!/usr/bin/env python3
"""
完整的本地MCP服务器
支持所有客户要求的功能：统计、分析、提醒、图表、趋势
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import warnings
warnings.filterwarnings("ignore")

# 导入配置和LLM管理器
from llm_providers import init_llm_manager, get_llm_manager

# FastMCP导入
try:
    from fastmcp import FastMCP
    from fastmcp.server.context import Context
except ImportError:
    print("❌ FastMCP未安装，请运行: pip install fastmcp")
    sys.exit(1)

# 第三方库导入
try:
    import mysql.connector
    from mysql.connector import pooling
    import pandas as pd
    import numpy as np
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import seaborn as sns
    from scipy import stats
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.utils import PlotlyJSONEncoder
    import base64
    from io import BytesIO
    import pyttsx3
    import speech_recognition as sr
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请运行: python install_local_system.py")
    sys.exit(1)

# 加载配置
def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 配置文件 config.json 不存在")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        sys.exit(1)

# 全局配置
CONFIG = load_config()

# 配置日志
logging.basicConfig(
    level=getattr(logging, CONFIG['logging']['level']),
    format=CONFIG['logging']['format'],
    handlers=[
        logging.FileHandler(CONFIG['logging']['file']),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 初始化LLM管理器
llm_manager = init_llm_manager(CONFIG['llm'])

# 创建MCP服务器实例
mcp = FastMCP(
    name=CONFIG['system']['name'],
    dependencies=[
        "mysql-connector-python>=8.0.0",
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "scipy>=1.7.0",
        "plotly>=5.0.0",
        "pyttsx3>=2.90",
        "speechrecognition>=3.8.0",
        "openai>=1.0.0",
        "aiohttp>=3.8.0"
    ]
)

class DatabaseManager:
    """优化的数据库管理器，支持大数据量"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.pool = None
        self.cache = {}
        self.cache_ttl = config.get('cache_ttl', 300)
        self.connected = False
        self.init_pool()

    def init_pool(self):
        """初始化连接池"""
        try:
            # 首先测试基本连接
            logger.info("正在测试数据库连接...")
            test_connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                charset=self.config['charset'],
                connect_timeout=5
            )
            test_connection.close()
            logger.info("数据库连接测试成功")

            # 检查数据库是否存在
            try:
                db_connection = mysql.connector.connect(
                    host=self.config['host'],
                    port=self.config['port'],
                    user=self.config['user'],
                    password=self.config['password'],
                    database=self.config['database'],
                    charset=self.config['charset'],
                    connect_timeout=5
                )
                db_connection.close()
                logger.info(f"数据库 '{self.config['database']}' 连接成功")
            except mysql.connector.Error as db_error:
                if db_error.errno == 1049:  # Unknown database
                    logger.warning(f"数据库 '{self.config['database']}' 不存在，将创建...")
                    self.create_database()
                else:
                    raise db_error

            # 创建连接池
            pool_config = {
                'pool_name': 'local_mcp_pool',
                'pool_size': self.config['pool_size'],
                'pool_reset_session': True,
                'host': self.config['host'],
                'port': self.config['port'],
                'user': self.config['user'],
                'password': self.config['password'],
                'database': self.config['database'],
                'charset': self.config['charset'],
                'autocommit': True,
                'use_unicode': True
            }

            self.pool = pooling.MySQLConnectionPool(**pool_config)
            self.connected = True
            logger.info("数据库连接池初始化成功")

        except mysql.connector.Error as e:
            logger.error(f"数据库连接失败: {e}")
            self.connected = False
            self.show_database_help()
            # 不抛出异常，允许系统继续运行
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            self.connected = False
            self.show_database_help()

    def create_database(self):
        """创建数据库"""
        try:
            connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                charset=self.config['charset']
            )
            cursor = connection.cursor()
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            connection.commit()
            cursor.close()
            connection.close()
            logger.info(f"数据库 '{self.config['database']}' 创建成功")
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            raise

    def show_database_help(self):
        """显示数据库配置帮助"""
        print("\n" + "="*60)
        print("❌ 数据库连接失败！")
        print("="*60)
        print("🔧 请检查以下配置：")
        print(f"  📍 主机: {self.config['host']}")
        print(f"  🔌 端口: {self.config['port']}")
        print(f"  👤 用户: {self.config['user']}")
        print(f"  🗄️ 数据库: {self.config['database']}")
        print("\n💡 解决方案：")
        print("1. 确保MySQL服务器正在运行")
        print("   Windows: 服务管理器中启动MySQL服务")
        print("   Linux: sudo systemctl start mysql")
        print("   macOS: brew services start mysql")
        print("\n2. 检查MySQL连接")
        print(f"   mysql -h {self.config['host']} -P {self.config['port']} -u {self.config['user']} -p")
        print("\n3. 修改配置文件 config.json 中的数据库设置")
        print("\n4. 或者使用演示模式（无需数据库）")
        print("   python local_mcp_server.py --demo-mode")
        print("="*60)
    
    def execute_query(self, query: str, params: tuple = None, use_cache: bool = True) -> pd.DataFrame:
        """执行查询，支持缓存"""
        # 检查数据库连接
        if not self.connected or not self.pool:
            raise Exception("数据库未连接，请检查数据库配置")

        cache_key = f"{query}_{params}" if params else query

        # 检查缓存
        if use_cache and cache_key in self.cache:
            cache_time, data = self.cache[cache_key]
            if datetime.now().timestamp() - cache_time < self.cache_ttl:
                logger.debug("使用缓存数据")
                return data

        # 执行查询
        try:
            connection = self.pool.get_connection()
            df = pd.read_sql(query, connection, params=params)
            connection.close()

            # 缓存结果
            if use_cache:
                self.cache[cache_key] = (datetime.now().timestamp(), df)

            logger.debug(f"查询执行成功，返回 {len(df)} 行数据")
            return df

        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            raise

    def is_connected(self) -> bool:
        """检查数据库是否连接"""
        return self.connected and self.pool is not None

class VoiceManager:
    """语音管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get('enabled', True)
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None
        
        if self.enabled:
            self.init_tts()
            self.init_asr()
    
    def init_tts(self):
        """初始化TTS"""
        try:
            self.tts_engine = pyttsx3.init()
            tts_config = self.config['tts']
            
            # 设置语音参数
            voices = self.tts_engine.getProperty('voices')
            if voices and tts_config.get('voice_id') is not None:
                self.tts_engine.setProperty('voice', voices[tts_config['voice_id']].id)
            
            self.tts_engine.setProperty('rate', tts_config.get('rate', 150))
            self.tts_engine.setProperty('volume', tts_config.get('volume', 0.8))
            
            logger.info("TTS引擎初始化成功")
        except Exception as e:
            logger.warning(f"TTS初始化失败: {e}")
            self.tts_engine = None
    
    def init_asr(self):
        """初始化ASR"""
        try:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # 调整环境噪音
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
            
            logger.info("ASR引擎初始化成功")
        except Exception as e:
            logger.warning(f"ASR初始化失败: {e}")
            self.recognizer = None
    
    def speak(self, text: str):
        """语音播报"""
        if self.enabled and self.tts_engine:
            try:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.warning(f"语音播报失败: {e}")
    
    def listen(self, timeout: int = 5) -> Optional[str]:
        """语音识别"""
        if not self.enabled or not self.recognizer or not self.microphone:
            return None
        
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=timeout)
            
            text = self.recognizer.recognize_google(audio, language='zh-CN')
            logger.info(f"识别到语音: {text}")
            return text
            
        except sr.WaitTimeoutError:
            logger.debug("语音识别超时")
            return None
        except Exception as e:
            logger.warning(f"语音识别失败: {e}")
            return None

# 初始化全局组件
print("🔧 正在初始化系统组件...")

# 检查是否为演示模式
DEMO_MODE = "--demo-mode" in sys.argv

if DEMO_MODE:
    print("🎭 演示模式：跳过数据库连接")
    db_manager = None
else:
    try:
        db_manager = DatabaseManager(CONFIG['database'])
        if db_manager.is_connected():
            print("✅ 数据库连接成功")
        else:
            print("❌ 数据库连接失败，部分功能将不可用")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        db_manager = None

voice_manager = VoiceManager(CONFIG['voice'])
if voice_manager.enabled:
    print("✅ 语音功能初始化成功")
else:
    print("⚠️ 语音功能未启用")

# 数据库连接检查函数
def check_database_connection():
    """检查数据库连接"""
    if db_manager is None or not db_manager.is_connected():
        return {
            "success": False,
            "error": "数据库未连接，请检查数据库配置或使用演示模式"
        }
    return None

@mcp.tool
async def get_system_status(ctx: Context = None) -> Dict[str, Any]:
    """获取系统状态"""
    try:
        if ctx:
            await ctx.info("正在检查系统状态...")

        # 检查LLM提供者状态
        llm_status = await llm_manager.check_providers()

        # 检查数据库连接
        if db_manager and db_manager.is_connected():
            try:
                test_query = "SELECT 1 as test"
                db_manager.execute_query(test_query)
                db_status = "正常"
            except Exception as e:
                db_status = f"查询异常: {str(e)}"
        else:
            db_status = "未连接"
        
        # 检查语音功能
        voice_status = {
            "enabled": voice_manager.enabled,
            "tts": voice_manager.tts_engine is not None,
            "asr": voice_manager.recognizer is not None
        }
        
        status = {
            "success": True,
            "system": {
                "name": CONFIG['system']['name'],
                "version": CONFIG['system']['version'],
                "offline_mode": CONFIG['system']['offline_mode']
            },
            "llm": {
                "current_provider": llm_manager.get_current_provider(),
                "providers_status": llm_status
            },
            "database": {
                "status": db_status,
                "pool_size": CONFIG['database']['pool_size']
            },
            "voice": voice_status,
            "timestamp": datetime.now().isoformat()
        }
        
        if ctx:
            await ctx.info("系统状态检查完成")
        
        return status
        
    except Exception as e:
        error_msg = f"系统状态检查失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@mcp.tool
async def switch_llm_provider(
    provider_name: str,
    ctx: Context = None
) -> Dict[str, Any]:
    """切换LLM提供者"""
    try:
        if ctx:
            await ctx.info(f"正在切换到LLM提供者: {provider_name}")
        
        success = llm_manager.switch_provider(provider_name)
        
        if success:
            # 检查新提供者是否可用
            is_available = await llm_manager.providers[provider_name].is_available()
            
            result = {
                "success": True,
                "current_provider": provider_name,
                "available": is_available,
                "message": f"已切换到 {provider_name}"
            }
            
            if voice_manager.enabled:
                voice_manager.speak(f"已切换到{provider_name}提供者")
            
            if ctx:
                await ctx.info(f"LLM提供者切换成功: {provider_name}")
        else:
            result = {
                "success": False,
                "error": f"未知的LLM提供者: {provider_name}",
                "available_providers": llm_manager.list_providers()
            }
        
        return result

    except Exception as e:
        error_msg = f"切换LLM提供者失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@mcp.tool
async def calculate_statistics(
    table: str,
    column: str,
    operation: str = "sum",
    start_time: str = None,
    end_time: str = None,
    time_column: str = "created_at",
    group_by: str = None,
    ctx: Context = None
) -> Dict[str, Any]:
    """统计分析：按条件求和、求平均等"""
    # 检查数据库连接
    db_check = check_database_connection()
    if db_check:
        return db_check

    try:
        if ctx:
            await ctx.info(f"正在计算表 {table} 列 {column} 的{operation}统计...")

        # 构建查询
        operations = {
            "sum": f"SUM({column})",
            "avg": f"AVG({column})",
            "count": f"COUNT({column})",
            "max": f"MAX({column})",
            "min": f"MIN({column})",
            "stddev": f"STDDEV({column})"
        }

        if operation not in operations:
            raise ValueError(f"不支持的操作: {operation}")

        select_clause = operations[operation]
        if group_by:
            select_clause = f"{group_by}, {select_clause}"

        query = f"SELECT {select_clause} FROM {table}"
        conditions = []

        # 添加时间条件
        if start_time:
            conditions.append(f"{time_column} >= '{start_time}'")
        if end_time:
            conditions.append(f"{time_column} <= '{end_time}'")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        if group_by:
            query += f" GROUP BY {group_by}"

        # 执行查询
        df = db_manager.execute_query(query)

        if df.empty:
            return {"success": False, "error": "没有找到数据"}

        # 处理结果
        if group_by:
            result_data = df.to_dict('records')
        else:
            result_data = {
                operation: float(df.iloc[0, 0]) if pd.notna(df.iloc[0, 0]) else 0
            }

        # 语音播报
        if voice_manager.enabled:
            if isinstance(result_data, dict):
                value = result_data[operation]
                voice_manager.speak(f"表{table}的{column}列{operation}结果为{value:.2f}")
            else:
                voice_manager.speak(f"统计计算完成，共{len(result_data)}组结果")

        if ctx:
            await ctx.info(f"统计计算完成")

        return {
            "success": True,
            "operation": operation,
            "table": table,
            "column": column,
            "data": result_data,
            "query": query,
            "time_range": {
                "start": start_time,
                "end": end_time
            }
        }

    except Exception as e:
        error_msg = f"统计计算失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager.enabled:
            voice_manager.speak("统计计算失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def detect_anomalies_with_ai(
    table: str,
    column: str,
    method: str = "zscore",
    threshold: float = 2.0,
    start_time: str = None,
    end_time: str = None,
    time_column: str = "created_at",
    enable_ai_analysis: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """异常检测并提供AI分析"""
    # 检查数据库连接
    db_check = check_database_connection()
    if db_check:
        return db_check

    try:
        if ctx:
            await ctx.info(f"正在检测表 {table} 列 {column} 的异常数据...")

        # 构建查询
        query = f"SELECT {time_column}, {column} FROM {table}"
        conditions = []

        if start_time:
            conditions.append(f"{time_column} >= '{start_time}'")
        if end_time:
            conditions.append(f"{time_column} <= '{end_time}'")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        query += f" ORDER BY {time_column}"

        # 执行查询
        df = db_manager.execute_query(query)

        if df.empty:
            return {"success": False, "error": "没有找到数据"}

        # 异常检测
        values = df[column].values
        anomalies = []

        if method == "zscore":
            z_scores = np.abs(stats.zscore(values))
            anomaly_indices = np.where(z_scores > threshold)[0]

            for idx in anomaly_indices:
                anomalies.append({
                    "index": int(idx),
                    "time": str(df.iloc[idx][time_column]),
                    "value": float(values[idx]),
                    "z_score": float(z_scores[idx]),
                    "reason": f"Z-score ({z_scores[idx]:.2f}) 超过阈值 {threshold}"
                })

        elif method == "iqr":
            Q1 = np.percentile(values, 25)
            Q3 = np.percentile(values, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR

            anomaly_indices = np.where((values < lower_bound) | (values > upper_bound))[0]

            for idx in anomaly_indices:
                reason = f"值 ({values[idx]:.2f}) 超出IQR范围 [{lower_bound:.2f}, {upper_bound:.2f}]"
                anomalies.append({
                    "index": int(idx),
                    "time": str(df.iloc[idx][time_column]),
                    "value": float(values[idx]),
                    "reason": reason
                })

        # AI异常原因分析
        ai_analysis = None
        if enable_ai_analysis and anomalies and len(anomalies) > 0:
            try:
                if ctx:
                    await ctx.info("正在进行AI异常原因分析...")

                anomalies_sample = anomalies[:5]  # 取前5个异常
                ai_prompt = f"""作为数据分析专家，请分析以下数据库异常检测结果：

数据表: {table}
字段: {column}
检测方法: {method}
阈值: {threshold}
异常总数: {len(anomalies)}
异常率: {len(anomalies)/len(values)*100:.2f}%

异常样本:
{json.dumps(anomalies_sample, indent=2, ensure_ascii=False)}

请从以下角度分析：
1. 可能的业务原因（如促销活动、系统故障、数据录入错误等）
2. 异常模式特征
3. 建议的后续行动
4. 风险评估

请用简洁专业的中文回答，重点关注实际业务场景。"""

                ai_analysis = await llm_manager.analyze(ai_prompt, temperature=0.3, max_tokens=800)

                if ctx:
                    await ctx.info("AI异常分析完成")

            except Exception as ai_error:
                logger.warning(f"AI分析失败: {ai_error}")
                ai_analysis = f"AI分析暂时不可用: {str(ai_error)}"

        # 语音播报
        if voice_manager.enabled:
            if len(anomalies) > 0:
                voice_manager.speak(f"检测到{len(anomalies)}个异常数据，异常率为{len(anomalies)/len(values)*100:.1f}%")
            else:
                voice_manager.speak("未检测到异常数据")

        if ctx:
            await ctx.info(f"异常检测完成，发现 {len(anomalies)} 个异常数据")

        result = {
            "success": True,
            "method": method,
            "threshold": threshold,
            "total_records": len(values),
            "anomaly_count": len(anomalies),
            "anomaly_rate": len(anomalies) / len(values) * 100,
            "anomalies": anomalies,
            "query": query
        }

        if ai_analysis:
            result["ai_analysis"] = ai_analysis

        return result

    except Exception as e:
        error_msg = f"异常检测失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager.enabled:
            voice_manager.speak("异常检测失败")
        return {"success": False, "error": error_msg}

# 全局提醒管理器
class AlertManager:
    """提醒管理器"""

    def __init__(self):
        self.alerts = {}
        self.alert_counter = 0

    def add_alert(self, config: Dict[str, Any]) -> str:
        """添加提醒"""
        self.alert_counter += 1
        alert_id = f"alert_{self.alert_counter}"
        config['id'] = alert_id
        config['created_at'] = datetime.now().isoformat()
        config['triggered_count'] = 0
        self.alerts[alert_id] = config
        return alert_id

    def check_alerts(self) -> List[Dict[str, Any]]:
        """检查所有提醒"""
        triggered = []

        for alert_id, alert in self.alerts.items():
            try:
                if self._check_single_alert(alert):
                    alert['triggered_count'] += 1
                    alert['last_triggered'] = datetime.now().isoformat()
                    triggered.append(alert.copy())
            except Exception as e:
                logger.error(f"检查提醒 {alert_id} 失败: {e}")

        return triggered

    def _check_single_alert(self, alert: Dict[str, Any]) -> bool:
        """检查单个提醒"""
        alert_type = alert.get('type')

        if alert_type == "value_threshold":
            return self._check_value_threshold(alert)
        elif alert_type == "time_based":
            return self._check_time_based(alert)

        return False

    def _check_value_threshold(self, alert: Dict[str, Any]) -> bool:
        """检查数值阈值提醒"""
        try:
            table = alert['table']
            column = alert['column']
            threshold = alert['threshold']
            operator = alert.get('operator', '>')

            query = f"SELECT {column} FROM {table} ORDER BY created_at DESC LIMIT 1"
            df = db_manager.execute_query(query)

            if df.empty:
                return False

            current_value = float(df.iloc[0, 0])

            if operator == '>':
                return current_value > threshold
            elif operator == '<':
                return current_value < threshold
            elif operator == '>=':
                return current_value >= threshold
            elif operator == '<=':
                return current_value <= threshold
            elif operator == '==':
                return current_value == threshold

            return False

        except Exception as e:
            logger.error(f"检查数值阈值提醒失败: {e}")
            return False

    def _check_time_based(self, alert: Dict[str, Any]) -> bool:
        """检查时间基础提醒"""
        try:
            alert_time = datetime.fromisoformat(alert['alert_time'])
            now = datetime.now()

            # 检查是否到达提醒时间（允许1分钟误差）
            time_diff = abs((now - alert_time).total_seconds())
            return time_diff <= 60

        except Exception as e:
            logger.error(f"检查时间提醒失败: {e}")
            return False

alert_manager = AlertManager()

@mcp.tool
async def create_smart_alert(
    alert_type: str,
    table: str,
    column: str = None,
    threshold: float = None,
    operator: str = ">",
    alert_time: str = None,
    description: str = "",
    enable_ai_priority: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """创建智能提醒"""
    # 检查数据库连接
    db_check = check_database_connection()
    if db_check:
        return db_check

    try:
        if ctx:
            await ctx.info(f"正在创建 {alert_type} 类型的智能提醒...")

        alert_config = {
            "type": alert_type,
            "table": table,
            "description": description
        }

        if alert_type == "value_threshold":
            if not column or threshold is None:
                raise ValueError("value_threshold类型需要指定column和threshold")
            alert_config.update({
                "column": column,
                "threshold": threshold,
                "operator": operator
            })
        elif alert_type == "time_based":
            if not alert_time:
                raise ValueError("time_based类型需要指定alert_time")
            alert_config["alert_time"] = alert_time

        # AI优先级评估
        ai_priority = None
        if enable_ai_priority:
            try:
                if ctx:
                    await ctx.info("正在进行AI优先级评估...")

                # 获取相关数据统计
                context_data = {}
                if alert_type == "value_threshold" and column:
                    try:
                        stats_query = f"""
                        SELECT
                            AVG({column}) as avg_value,
                            STDDEV({column}) as std_dev,
                            MIN({column}) as min_value,
                            MAX({column}) as max_value,
                            COUNT(*) as total_records
                        FROM {table}
                        WHERE {column} IS NOT NULL
                        """
                        stats_df = db_manager.execute_query(stats_query)
                        if not stats_df.empty:
                            context_data = stats_df.iloc[0].to_dict()
                    except Exception:
                        pass

                ai_prompt = f"""作为数据监控专家，请评估以下数据提醒的优先级和重要性：

提醒配置:
- 类型: {alert_type}
- 数据表: {table}
- 字段: {column or '无'}
- 阈值: {threshold or '无'}
- 操作符: {operator}
- 描述: {description or '无描述'}

数据背景信息:
{json.dumps(context_data, indent=2, ensure_ascii=False) if context_data else '暂无统计信息'}

请从以下角度评估：
1. 紧急程度 (高/中/低)
2. 业务影响程度 (高/中/低)
3. 误报风险评估
4. 建议的响应时间
5. 监控频率建议
6. 潜在的业务风险

请提供简洁的评估结果，包含具体的优先级建议和理由。"""

                ai_priority = await llm_manager.analyze(ai_prompt, temperature=0.2, max_tokens=600)

                if ctx:
                    await ctx.info("AI优先级评估完成")

            except Exception as ai_error:
                logger.warning(f"AI优先级评估失败: {ai_error}")
                ai_priority = f"AI优先级评估暂时不可用: {str(ai_error)}"

        # 添加AI评估到配置
        if ai_priority:
            alert_config["ai_priority_assessment"] = ai_priority

        # 创建提醒
        alert_id = alert_manager.add_alert(alert_config)

        # 语音确认
        if voice_manager.enabled:
            voice_manager.speak(f"智能提醒创建成功，编号为{alert_id}")

        if ctx:
            await ctx.info(f"智能提醒创建成功，ID: {alert_id}")

        result = {
            "success": True,
            "alert_id": alert_id,
            "config": alert_config
        }

        if ai_priority:
            result["ai_priority_assessment"] = ai_priority

        return result

    except Exception as e:
        error_msg = f"创建智能提醒失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager.enabled:
            voice_manager.speak("创建智能提醒失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def generate_chart(
    table: str,
    chart_type: str,
    x_column: str,
    y_column: str = None,
    title: str = "数据图表",
    start_time: str = None,
    end_time: str = None,
    time_column: str = "created_at",
    limit: int = 1000,
    ctx: Context = None
) -> Dict[str, Any]:
    """生成统计图表"""
    # 检查数据库连接
    db_check = check_database_connection()
    if db_check:
        return db_check

    try:
        if ctx:
            await ctx.info(f"正在生成{chart_type}图表...")

        # 构建查询
        if y_column:
            select_clause = f"{x_column}, {y_column}"
        else:
            select_clause = f"{x_column}, COUNT(*) as count"

        query = f"SELECT {select_clause} FROM {table}"
        conditions = []

        if start_time:
            conditions.append(f"{time_column} >= '{start_time}'")
        if end_time:
            conditions.append(f"{time_column} <= '{end_time}'")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        if not y_column:
            query += f" GROUP BY {x_column}"

        query += f" ORDER BY {x_column} LIMIT {limit}"

        # 执行查询
        df = db_manager.execute_query(query)

        if df.empty:
            return {"success": False, "error": "没有找到数据"}

        # 生成图表
        chart_data = None
        image_base64 = None

        if chart_type == "bar":
            # 柱状图
            fig = px.bar(df, x=x_column, y=y_column or 'count', title=title)
            chart_data = fig.to_json()

        elif chart_type == "pie":
            # 饼状图
            fig = px.pie(df, names=x_column, values=y_column or 'count', title=title)
            chart_data = fig.to_json()

        elif chart_type == "line":
            # 折线图
            fig = px.line(df, x=x_column, y=y_column or 'count', title=title)
            chart_data = fig.to_json()

        elif chart_type == "scatter":
            # 散点图
            if not y_column:
                return {"success": False, "error": "散点图需要指定y_column"}
            fig = px.scatter(df, x=x_column, y=y_column, title=title)
            chart_data = fig.to_json()

        # 生成静态图片（可选）
        if chart_data:
            try:
                # 使用matplotlib生成静态图片
                plt.figure(figsize=(10, 6))

                if chart_type == "bar":
                    plt.bar(df[x_column], df[y_column or 'count'])
                elif chart_type == "line":
                    plt.plot(df[x_column], df[y_column or 'count'])
                elif chart_type == "pie":
                    plt.pie(df[y_column or 'count'], labels=df[x_column], autopct='%1.1f%%')

                plt.title(title)
                plt.xticks(rotation=45)
                plt.tight_layout()

                # 转换为base64
                buffer = BytesIO()
                plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
                buffer.seek(0)
                image_base64 = base64.b64encode(buffer.getvalue()).decode()
                plt.close()

            except Exception as e:
                logger.warning(f"生成静态图片失败: {e}")

        # 语音播报
        if voice_manager.enabled:
            voice_manager.speak(f"{chart_type}图表生成完成，包含{len(df)}个数据点")

        if ctx:
            await ctx.info("图表生成完成")

        result = {
            "success": True,
            "chart_type": chart_type,
            "title": title,
            "data_points": len(df),
            "chart_data": chart_data,
            "query": query
        }

        if image_base64:
            result["image_base64"] = image_base64

        return result

    except Exception as e:
        error_msg = f"图表生成失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager.enabled:
            voice_manager.speak("图表生成失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def analyze_trend_with_ai(
    table: str,
    time_column: str,
    value_column: str,
    period: str = "day",
    forecast_days: int = 7,
    enable_ai_insights: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """趋势分析并提供AI洞察"""
    # 检查数据库连接
    db_check = check_database_connection()
    if db_check:
        return db_check

    try:
        if ctx:
            await ctx.info(f"正在分析表 {table} 的数据趋势...")

        # 根据周期构建聚合查询
        date_formats = {
            'hour': '%Y-%m-%d %H:00:00',
            'day': '%Y-%m-%d',
            'week': '%Y-%u',
            'month': '%Y-%m'
        }

        if period not in date_formats:
            raise ValueError(f"不支持的周期: {period}")

        query = f"""
        SELECT
            DATE_FORMAT({time_column}, '{date_formats[period]}') as period,
            AVG({value_column}) as avg_value,
            COUNT(*) as count,
            MIN({value_column}) as min_value,
            MAX({value_column}) as max_value,
            STDDEV({value_column}) as std_dev
        FROM {table}
        WHERE {time_column} >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY period
        ORDER BY period
        """

        df = db_manager.execute_query(query)

        if df.empty:
            return {"success": False, "error": "没有找到数据"}

        # 计算趋势指标
        values = df['avg_value'].values
        trend_slope = np.polyfit(range(len(values)), values, 1)[0]

        # 计算更多统计指标
        avg_value = float(np.mean(values))
        volatility = float(np.std(values))
        growth_rate = ((values[-1] - values[0]) / values[0] * 100) if values[0] != 0 else 0

        # 简单的线性预测
        last_value = values[-1]
        predictions = []
        for i in range(1, forecast_days + 1):
            predicted_value = last_value + (trend_slope * i)
            predictions.append({
                'day': i,
                'predicted_value': float(predicted_value)
            })

        # 趋势分析
        if trend_slope > 0:
            trend_direction = "上升"
            trend_description = f"数据呈上升趋势，斜率为 {trend_slope:.4f}"
        elif trend_slope < 0:
            trend_direction = "下降"
            trend_description = f"数据呈下降趋势，斜率为 {trend_slope:.4f}"
        else:
            trend_direction = "平稳"
            trend_description = "数据趋势相对平稳"

        # AI洞察分析
        ai_insights = None
        if enable_ai_insights:
            try:
                if ctx:
                    await ctx.info("正在进行AI趋势洞察分析...")

                ai_prompt = f"""对以下数据趋势进行深度洞察分析：

数据表: {table}
字段: {value_column}
分析周期: {period}
数据点数量: {len(values)}
趋势方向: {trend_direction}
增长率: {growth_rate:.2f}%
平均值: {avg_value:.2f}
波动性: {volatility:.2f}

最近数据趋势:
{json.dumps(df.tail(7).to_dict('records'), indent=2, ensure_ascii=False)}

请从以下角度提供洞察：
1. 趋势模式分析（季节性、周期性、异常波动）
2. 业务含义解读（可能的业务驱动因素）
3. 风险与机会识别
4. 预测可信度评估
5. 建议的监控重点和行动建议

请用专业但易懂的中文回答，重点关注业务价值和可操作性。"""

                ai_insights = await llm_manager.analyze(ai_prompt, temperature=0.4, max_tokens=1000)

                if ctx:
                    await ctx.info("AI趋势洞察分析完成")

            except Exception as ai_error:
                logger.warning(f"AI洞察分析失败: {ai_error}")
                ai_insights = f"AI洞察分析暂时不可用: {str(ai_error)}"

        # 语音播报
        if voice_manager.enabled:
            voice_summary = f"数据趋势分析完成，{trend_direction}趋势，增长率{growth_rate:.1f}%"
            if ai_insights:
                voice_summary += "，已完成AI洞察分析"
            voice_manager.speak(voice_summary)

        if ctx:
            await ctx.info(f"趋势分析完成，趋势方向: {trend_direction}")

        result = {
            "success": True,
            "trend_analysis": {
                "direction": trend_direction,
                "slope": float(trend_slope),
                "description": trend_description,
                "data_points": len(values),
                "growth_rate": float(growth_rate),
                "avg_value": avg_value,
                "volatility": volatility
            },
            "historical_data": df.to_dict('records'),
            "predictions": predictions,
            "period": period,
            "query": query
        }

        if ai_insights:
            result["ai_insights"] = ai_insights

        return result

    except Exception as e:
        error_msg = f"趋势分析失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        if voice_manager.enabled:
            voice_manager.speak("趋势分析失败")
        return {"success": False, "error": error_msg}

@mcp.tool
async def check_alerts_with_ai(
    enable_ai_analysis: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """智能检查所有提醒"""
    # 检查数据库连接
    db_check = check_database_connection()
    if db_check:
        return db_check

    try:
        if ctx:
            await ctx.info("正在检查所有提醒条件...")

        triggered_alerts = alert_manager.check_alerts()

        # AI智能分析触发的提醒
        ai_alert_analysis = None
        if enable_ai_analysis and triggered_alerts:
            try:
                if ctx:
                    await ctx.info("正在进行AI提醒分析...")

                alert_summary = {
                    "total_triggered": len(triggered_alerts),
                    "total_monitored": len(alert_manager.alerts),
                    "triggered_alerts": triggered_alerts[:5]  # 取前5个
                }

                ai_prompt = f"""作为数据监控专家，请分析当前触发的数据提醒情况：

监控概况:
- 总监控项目: {alert_summary['total_monitored']}
- 触发提醒数量: {alert_summary['total_triggered']}

触发的提醒详情:
{json.dumps(alert_summary['triggered_alerts'], indent=2, ensure_ascii=False)}

请从以下角度分析：
1. 提醒严重程度评估
2. 是否存在关联性问题（多个提醒可能指向同一根本原因）
3. 建议的处理优先级排序
4. 可能的系统性问题识别
5. 立即行动建议

请提供简洁专业的分析，重点关注可操作的建议。"""

                ai_alert_analysis = await llm_manager.analyze(ai_prompt, temperature=0.3, max_tokens=700)

                if ctx:
                    await ctx.info("AI提醒分析完成")

            except Exception as ai_error:
                logger.warning(f"AI提醒分析失败: {ai_error}")
                ai_alert_analysis = f"AI提醒分析暂时不可用: {str(ai_error)}"

        # 语音播报
        if voice_manager.enabled:
            if triggered_alerts:
                for alert in triggered_alerts:
                    message = f"提醒触发: {alert.get('description', '未命名提醒')}"
                    voice_manager.speak(message)

                if ai_alert_analysis:
                    voice_manager.speak(f"已触发{len(triggered_alerts)}个提醒，AI分析已完成")
            else:
                voice_manager.speak("所有监控项目正常，无提醒触发")

        if ctx:
            await ctx.info(f"检查完成，触发了 {len(triggered_alerts)} 个提醒")

        result = {
            "success": True,
            "triggered_alerts": triggered_alerts,
            "total_checked": len(alert_manager.alerts),
            "alert_summary": {
                "total_triggered": len(triggered_alerts),
                "total_monitored": len(alert_manager.alerts),
                "trigger_rate": len(triggered_alerts) / len(alert_manager.alerts) * 100 if alert_manager.alerts else 0
            }
        }

        if ai_alert_analysis:
            result["ai_alert_analysis"] = ai_alert_analysis

        return result

    except Exception as e:
        error_msg = f"检查提醒失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@mcp.tool
async def voice_command(
    command: str = None,
    listen_for_command: bool = False,
    ctx: Context = None
) -> Dict[str, Any]:
    """语音命令处理"""
    try:
        if ctx:
            await ctx.info("处理语音命令...")

        # 如果需要监听语音
        if listen_for_command:
            if voice_manager.enabled:
                voice_manager.speak("请说出您的命令")
                command = voice_manager.listen(timeout=10)
                if not command:
                    return {"success": False, "error": "未识别到语音命令"}
            else:
                return {"success": False, "error": "语音功能未启用"}

        if not command:
            return {"success": False, "error": "未提供命令"}

        # 简单的命令解析
        command_lower = command.lower()
        response = ""

        if "状态" in command_lower or "系统" in command_lower:
            status = await get_system_status(ctx)
            if status["success"]:
                response = f"系统运行正常，当前LLM提供者：{status['llm']['current_provider']}"
            else:
                response = "系统状态检查失败"

        elif "统计" in command_lower:
            response = "请指定要统计的表和字段，例如：统计payment表amount字段的总和"

        elif "异常" in command_lower:
            response = "请指定要检测异常的表和字段，例如：检测payment表amount字段的异常"

        elif "图表" in command_lower:
            response = "请指定要生成的图表类型和数据，例如：生成payment表的柱状图"

        elif "趋势" in command_lower:
            response = "请指定要分析趋势的表和字段，例如：分析payment表amount字段的趋势"

        else:
            # 使用AI理解命令
            try:
                ai_prompt = f"""用户说了语音命令："{command}"

这是一个MySQL数据分析系统，支持以下功能：
1. 统计分析（求和、平均值等）
2. 异常检测
3. 生成图表
4. 趋势分析
5. 智能提醒

请分析用户的意图，并给出简洁的回应建议。如果命令不清楚，请询问更多细节。"""

                response = await llm_manager.analyze(ai_prompt, temperature=0.3, max_tokens=200)

            except Exception:
                response = "抱歉，我没有理解您的命令。请说得更具体一些。"

        # 语音回应
        if voice_manager.enabled:
            voice_manager.speak(response)

        if ctx:
            await ctx.info("语音命令处理完成")

        return {
            "success": True,
            "command": command,
            "response": response,
            "voice_enabled": voice_manager.enabled
        }

    except Exception as e:
        error_msg = f"语音命令处理失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

if __name__ == "__main__":
    print("🚀 启动完整的本地MCP MySQL数据分析系统")
    print(f"📋 系统名称: {CONFIG['system']['name']}")
    print(f"🔧 版本: {CONFIG['system']['version']}")
    print(f"🌐 离线模式: {CONFIG['system']['offline_mode']}")
    print(f"🤖 当前LLM提供者: {CONFIG['llm']['provider']}")
    print(f"🎤 语音功能: {'启用' if CONFIG['voice']['enabled'] else '禁用'}")
    print("=" * 60)

    # 配置日志
    logging.basicConfig(
        level=getattr(logging, CONFIG['logging']['level']),
        format=CONFIG['logging']['format']
    )

    # 检查LLM提供者状态
    async def check_llm_status():
        try:
            status = await llm_manager.check_providers()
            print("🤖 LLM提供者状态:")
            for provider, available in status.items():
                status_icon = "✅" if available else "❌"
                print(f"  {status_icon} {provider}: {'可用' if available else '不可用'}")

            current = llm_manager.get_current_provider()
            if current and status.get(current, False):
                print(f"✅ 当前使用: {current}")
            else:
                print("⚠️ 当前提供者不可用，请检查配置")
        except Exception as e:
            print(f"❌ LLM状态检查失败: {e}")

    # 运行状态检查
    asyncio.run(check_llm_status())

    print("\n🎯 支持的功能:")
    print("  📊 统计分析 - calculate_statistics")
    print("  🔍 异常检测 - detect_anomalies_with_ai")
    print("  ⚠️ 智能提醒 - create_smart_alert")
    print("  📈 图表生成 - generate_chart")
    print("  📉 趋势分析 - analyze_trend_with_ai")
    print("  🎤 语音交互 - voice_command")
    print("  🔧 系统管理 - get_system_status")

    print(f"\n🌐 MCP服务器配置:")
    print(f"  地址: {CONFIG['mcp_server']['host']}:{CONFIG['mcp_server']['port']}")
    print(f"  传输: {CONFIG['mcp_server']['transport']}")

    print("\n💡 使用说明:")
    print("  1. 连接MCP客户端到上述地址")
    print("  2. 使用工具进行数据分析")
    print("  3. 支持语音交互（如果启用）")
    print("  4. 按 Ctrl+C 停止服务器")
    print("=" * 60)

    # 启动MCP服务器
    try:
        mcp.run(
            transport=CONFIG['mcp_server']['transport'],
            host=CONFIG['mcp_server']['host'],
            port=CONFIG['mcp_server']['port']
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)
