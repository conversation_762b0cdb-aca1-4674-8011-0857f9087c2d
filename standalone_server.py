#!/usr/bin/env python3
"""
独立的MCP服务器
用于分离式架构
"""

import mysql.connector
import pandas as pd
from typing import Dict, Any
from fastmcp import FastMCP

print("🚀 启动独立MCP服务器")
print("=" * 40)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'realtime_data',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return mysql.connector.connect(**DB_CONFIG)

# 创建MCP服务器
mcp = FastMCP("工业数据分析系统")

@mcp.tool()
def hello() -> str:
    """简单的hello工具"""
    return "Hello from Industrial Data Analysis Server!"

@mcp.tool()
def get_system_status() -> Dict[str, Any]:
    """获取系统状态"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM industrial_monitoring")
        record_count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        return {
            "status": "运行正常",
            "database": "已连接",
            "records": record_count,
            "server": "独立工业数据分析MCP服务器",
            "port": 9001
        }
    except Exception as e:
        return {
            "status": "错误",
            "error": str(e)
        }

@mcp.tool()
def get_data_summary() -> Dict[str, Any]:
    """获取数据摘要"""
    try:
        connection = get_db_connection()
        
        query = """
        SELECT 
            COUNT(*) as total_records,
            MIN(record_time) as earliest_time,
            MAX(record_time) as latest_time,
            AVG(pressure_1) as avg_pressure_1,
            AVG(temperature_1) as avg_temperature_1
        FROM industrial_monitoring
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        result = df.iloc[0].to_dict()
        
        # 转换时间格式
        if result['earliest_time']:
            result['earliest_time'] = str(result['earliest_time'])
        if result['latest_time']:
            result['latest_time'] = str(result['latest_time'])
        
        return {
            "success": True,
            "summary": result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def calculate_average(column: str) -> Dict[str, Any]:
    """计算指定列的平均值"""
    try:
        allowed_columns = [
            'pressure_1', 'temperature_1', 'pressure_2', 'temperature_2',
            'oxygen_content', 'flow_rate_1'
        ]
        
        if column not in allowed_columns:
            return {
                "success": False,
                "error": f"列名 '{column}' 不允许。允许的列: {allowed_columns}"
            }
        
        connection = get_db_connection()
        
        query = f"""
        SELECT 
            AVG({column}) as average_value,
            MIN({column}) as min_value,
            MAX({column}) as max_value,
            COUNT({column}) as count_value
        FROM industrial_monitoring 
        WHERE {column} IS NOT NULL
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        result = df.iloc[0].to_dict()
        
        return {
            "success": True,
            "column": column,
            "statistics": result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def get_recent_data(limit: int = 5) -> Dict[str, Any]:
    """获取最近的数据记录"""
    try:
        if limit > 20:
            limit = 20
        
        connection = get_db_connection()
        
        query = f"""
        SELECT id, record_time, pressure_1, temperature_1, oxygen_content
        FROM industrial_monitoring 
        ORDER BY record_time DESC 
        LIMIT {limit}
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        records = []
        for _, row in df.iterrows():
            record = row.to_dict()
            if 'record_time' in record and record['record_time']:
                record['record_time'] = str(record['record_time'])
            records.append(record)
        
        return {
            "success": True,
            "count": len(records),
            "records": records
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    print("🚀 启动独立MCP服务器")
    print("=" * 40)
    print("📍 服务器地址: http://127.0.0.1:9002/mcp/")
    print("🔧 可用工具:")
    print("  - hello")
    print("  - get_system_status")
    print("  - get_data_summary")
    print("  - calculate_average")
    print("  - get_recent_data")
    print("=" * 40)
    print("🎯 等待客户端连接...")

    # 添加请求日志记录
    import logging
    logging.basicConfig(level=logging.INFO)  # 改为INFO级别，避免过多日志
    try:
        # 使用端口9002避免冲突，并指定路径
        print("🔧 [DEBUG] 启动参数:")
        print(f"  transport: http")
        print(f"  host: 127.0.0.1")
        print(f"  port: 9002")
        print(f"  path: /mcp")

        mcp.run(transport="http", host="127.0.0.1", port=9002, path="/mcp")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        print("💡 请检查端口9002是否被占用")
        import traceback
        traceback.print_exc()
