-- 创建数据库
CREATE DATABASE IF NOT EXISTS realtime_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE realtime_data;

-- 创建支付表
CREATE TABLE IF NOT EXISTS payment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_payment_date (payment_date),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- 插入支付示例数据（包含一些异常值用于测试）
INSERT INTO payment (user_id, amount, payment_date) VALUES
-- 正常数据
(1, 100.50, '2024-07-01 10:00:00'),
(2, 250.00, '2024-07-01 11:30:00'),
(3, 75.25, '2024-07-01 14:15:00'),
(1, 500.00, '2024-07-02 09:20:00'),
(4, 125.75, '2024-07-02 16:45:00'),
(2, 300.00, '2024-07-03 12:10:00'),
(5, 50.00, '2024-07-03 18:30:00'),
(3, 200.00, '2024-07-04 08:00:00'),
(6, 175.50, '2024-07-04 13:25:00'),
(4, 225.25, '2024-07-05 15:40:00'),

-- 最近数据（用于实时分析）
(1, 89.99, '2024-08-01 08:00:00'),
(2, 156.78, '2024-08-01 09:15:00'),
(3, 234.56, '2024-08-01 10:30:00'),
(4, 67.89, '2024-08-01 11:45:00'),
(5, 345.67, '2024-08-01 13:00:00'),
(6, 123.45, '2024-08-01 14:15:00'),
(1, 456.78, '2024-08-01 15:30:00'),
(2, 78.90, '2024-08-01 16:45:00'),

-- 异常数据（用于异常检测测试）
(7, 10000.00, '2024-08-01 17:00:00'),  -- 异常高值
(8, 0.01, '2024-08-01 17:15:00'),      -- 异常低值
(9, 50000.00, '2024-08-01 17:30:00'),  -- 异常高值
(10, -100.00, '2024-08-01 17:45:00'),  -- 负值异常

-- 更多正常数据
(1, 199.99, '2024-08-01 18:00:00'),
(2, 299.99, '2024-08-01 18:15:00'),
(3, 149.99, '2024-08-01 18:30:00'),
(4, 399.99, '2024-08-01 18:45:00'),
(5, 99.99, '2024-08-01 19:00:00');

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    age INT,
    city VARCHAR(50),
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_city (city)
);

-- 插入用户数据
INSERT INTO users (username, email, age, city) VALUES
('user1', '<EMAIL>', 25, '北京'),
('user2', '<EMAIL>', 30, '上海'),
('user3', '<EMAIL>', 28, '广州'),
('user4', '<EMAIL>', 35, '深圳'),
('user5', '<EMAIL>', 22, '杭州'),
('user6', '<EMAIL>', 40, '成都'),
('user7', '<EMAIL>', 33, '武汉'),
('user8', '<EMAIL>', 27, '西安'),
('user9', '<EMAIL>', 31, '南京'),
('user10', '<EMAIL>', 29, '重庆');

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    order_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_order_date (order_date),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- 插入订单数据
INSERT INTO orders (user_id, product_name, quantity, unit_price, total_amount, order_date, status) VALUES
(1, '笔记本电脑', 1, 5999.00, 5999.00, '2024-07-01 10:00:00', 'completed'),
(2, '手机', 2, 3999.00, 7998.00, '2024-07-01 11:30:00', 'completed'),
(3, '耳机', 1, 299.00, 299.00, '2024-07-01 14:15:00', 'completed'),
(1, '键盘', 1, 199.00, 199.00, '2024-07-02 09:20:00', 'completed'),
(4, '鼠标', 3, 89.00, 267.00, '2024-07-02 16:45:00', 'completed'),
(2, '显示器', 1, 1999.00, 1999.00, '2024-07-03 12:10:00', 'completed'),
(5, '音箱', 2, 399.00, 798.00, '2024-07-03 18:30:00', 'pending'),
(3, '平板电脑', 1, 2999.00, 2999.00, '2024-07-04 08:00:00', 'completed'),
(6, '充电器', 5, 49.00, 245.00, '2024-07-04 13:25:00', 'completed'),
(4, '数据线', 10, 19.00, 190.00, '2024-07-05 15:40:00', 'pending'),

-- 最近订单
(1, '无线充电器', 1, 129.00, 129.00, '2024-08-01 08:30:00', 'completed'),
(2, '蓝牙音箱', 1, 299.00, 299.00, '2024-08-01 10:15:00', 'pending'),
(3, '智能手表', 1, 1299.00, 1299.00, '2024-08-01 12:00:00', 'completed'),
(4, '移动电源', 2, 99.00, 198.00, '2024-08-01 14:30:00', 'completed'),
(5, '车载充电器', 1, 39.00, 39.00, '2024-08-01 16:15:00', 'pending');

-- 创建销售数据表（用于趋势分析）
CREATE TABLE IF NOT EXISTS sales_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sale_date DATE NOT NULL,
    product_category VARCHAR(50) NOT NULL,
    sales_amount DECIMAL(12,2) NOT NULL,
    quantity_sold INT NOT NULL,
    region VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sale_date (sale_date),
    INDEX idx_category (product_category),
    INDEX idx_region (region)
);

-- 插入销售趋势数据（最近30天）
INSERT INTO sales_data (sale_date, product_category, sales_amount, quantity_sold, region) VALUES
-- 7月数据
('2024-07-01', '电子产品', 15000.00, 25, '华北'),
('2024-07-02', '电子产品', 18000.00, 30, '华东'),
('2024-07-03', '电子产品', 12000.00, 20, '华南'),
('2024-07-04', '电子产品', 22000.00, 35, '华北'),
('2024-07-05', '电子产品', 16000.00, 28, '华东'),
('2024-07-06', '电子产品', 14000.00, 23, '华南'),
('2024-07-07', '电子产品', 19000.00, 32, '华北'),

('2024-07-08', '服装', 8000.00, 40, '华北'),
('2024-07-09', '服装', 9500.00, 45, '华东'),
('2024-07-10', '服装', 7200.00, 35, '华南'),
('2024-07-11', '服装', 11000.00, 50, '华北'),
('2024-07-12', '服装', 8800.00, 42, '华东'),

('2024-07-15', '家居用品', 5000.00, 15, '华北'),
('2024-07-16', '家居用品', 6200.00, 18, '华东'),
('2024-07-17', '家居用品', 4800.00, 12, '华南'),
('2024-07-18', '家居用品', 7500.00, 22, '华北'),

-- 8月数据（显示增长趋势）
('2024-08-01', '电子产品', 25000.00, 40, '华北'),
('2024-08-01', '服装', 12000.00, 55, '华东'),
('2024-08-01', '家居用品', 8000.00, 25, '华南');

-- 显示创建结果
SELECT '数据库和示例数据创建完成！' as message;
SELECT '包含以下表:' as info;
SELECT 'payment - 支付记录表' as table_info
UNION ALL SELECT 'users - 用户表'
UNION ALL SELECT 'orders - 订单表'
UNION ALL SELECT 'sales_data - 销售数据表';

-- 显示数据统计
SELECT 
    '支付记录' as table_name,
    COUNT(*) as record_count,
    MIN(payment_date) as earliest_date,
    MAX(payment_date) as latest_date
FROM payment
UNION ALL
SELECT 
    '用户数据',
    COUNT(*),
    MIN(created_at),
    MAX(created_at)
FROM users
UNION ALL
SELECT 
    '订单数据',
    COUNT(*),
    MIN(order_date),
    MAX(order_date)
FROM orders
UNION ALL
SELECT 
    '销售数据',
    COUNT(*),
    MIN(sale_date),
    MAX(sale_date)
FROM sales_data;
