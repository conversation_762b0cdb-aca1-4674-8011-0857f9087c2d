# FastMCP 文档 - 第 6 部分
# 主要内容: Dependencies
# 包含段落: 94 个
# 总行数: 1031

================================================================================

## Dependencies
类型: docs, 行数: 25

#### Dependencies

If your server has dependencies, you can use `uv` or another package manager to set up the environment.

```json
{
  "mcpServers": {
    "dice-roller": {
      "command": "uv",
      "args": [
        "run",
        "--with", "pandas",
        "--with", "requests", 
        "python",
        "path/to/your/server.py"
      ]
    }
  }
}
```

<Warning>
  **`uv` must be installed and available in your system PATH**. Cursor runs in its own isolated environment and needs `uv` to manage dependencies.
</Warning>


------------------------------------------------------------

## Environment Variables
类型: docs, 行数: 23

#### Environment Variables

You can also specify environment variables in the configuration:

```json
{
  "mcpServers": {
    "weather-server": {
      "command": "python",
      "args": ["path/to/weather_server.py"],
      "env": {
        "API_KEY": "your-api-key",
        "DEBUG": "true"
      }
    }
  }
}
```

<Warning>
  Cursor runs servers in a completely isolated environment with no access to your shell environment or locally installed applications. You must explicitly pass any environment variables your server needs.
</Warning>


------------------------------------------------------------

## Using the Server
类型: docs, 行数: 17

## Using the Server

Once your server is installed, you can start using your FastMCP server with Cursor's AI assistant.

Try asking Cursor something like:

> "Roll some dice for me"

Cursor will automatically detect your `roll_dice` tool and use it to fulfill your request, returning something like:

> 🎲 Here are your dice rolls: 4, 6, 4
>
> You rolled 3 dice with a total of 14! The 6 was a nice high roll there!

The AI assistant can now access all the tools, resources, and prompts you've defined in your FastMCP server.



------------------------------------------------------------

## Eunomia Authorization 🤝 FastMCP
类型: docs, 行数: 9

# Eunomia Authorization 🤝 FastMCP
Source: https://gofastmcp.com/integrations/eunomia-authorization

Add policy-based authorization to your FastMCP servers

Add **policy-based authorization** to your FastMCP servers with one-line code addition with the **[Eunomia][eunomia-github] authorization middleware**.

Control which tools, resources and prompts MCP clients can view and execute on your server. Define dynamic JSON-based policies and obtain a comprehensive audit log of all access attempts and violations.


------------------------------------------------------------

## How it Works
类型: docs, 行数: 4

## How it Works

Exploiting FastMCP's [Middleware][fastmcp-middleare], the Eunomia middleware intercepts all MCP requests to your server and, then, automatically maps MCP methods to authorization checks.


------------------------------------------------------------

## Listing Operations
类型: docs, 行数: 19

### Listing Operations

The middleware behaves as a filter for listing operations (`tools/list`, `resources/list`, `prompts/list`), hiding to the client components that are not authorized by the defined policies.

```mermaid
sequenceDiagram
    participant MCPClient as MCP Client
    participant EunomiaMiddleware as Eunomia Middleware
    participant MCPServer as FastMCP Server
    participant EunomiaServer as Eunomia Server

    MCPClient->>EunomiaMiddleware: MCP Listing Request (e.g., tools/list)
    EunomiaMiddleware->>MCPServer: MCP Listing Request
    MCPServer-->>EunomiaMiddleware: MCP Listing Response
    EunomiaMiddleware->>EunomiaServer: Authorization Checks
    EunomiaServer->>EunomiaMiddleware: Authorization Decisions
    EunomiaMiddleware-->>MCPClient: Filtered MCP Listing Response
```


------------------------------------------------------------

## Execution Operations
类型: docs, 行数: 20

### Execution Operations

The middleware behaves as a firewall for execution operations (`tools/call`, `resources/read`, `prompts/get`), blocking operations that are not authorized by the defined policies.

```mermaid
sequenceDiagram
    participant MCPClient as MCP Client
    participant EunomiaMiddleware as Eunomia Middleware
    participant MCPServer as FastMCP Server
    participant EunomiaServer as Eunomia Server

    MCPClient->>EunomiaMiddleware: MCP Execution Request (e.g., tools/call)
    EunomiaMiddleware->>EunomiaServer: Authorization Check
    EunomiaServer->>EunomiaMiddleware: Authorization Decision
    EunomiaMiddleware-->>MCPClient: MCP Unauthorized Error (if denied)
    EunomiaMiddleware->>MCPServer: MCP Execution Request (if allowed)
    MCPServer-->>EunomiaMiddleware: MCP Execution Response (if allowed)
    EunomiaMiddleware-->>MCPClient: MCP Execution Response (if allowed)
```


------------------------------------------------------------

## Add Authorization to Your Server
类型: docs, 行数: 12

## Add Authorization to Your Server

<Note>
  Eunomia is an AI-specific standalone authorization server that handles policy decisions. You must have an Eunomia server running alongside your FastMCP server for the middleware to function.

  Run it in the background with Docker:

  ```bash
  docker run -d -p 8000:8000 ttommitt/eunomia-server:latest
  ```
</Note>


------------------------------------------------------------

## Create a Server with Authorization
类型: docs, 行数: 28

### Create a Server with Authorization

First, install the `eunomia-mcp` package:

```bash
pip install eunomia-mcp
```

Then create a FastMCP server and add the Eunomia middleware in one line:

```python server.py
from fastmcp import FastMCP
from eunomia_mcp import EunomiaMcpMiddleware

mcp = FastMCP("Secure FastMCP Server 🔒")

@mcp.tool()
def add(a: int, b: int) -> int:
    """Add two numbers"""
    return a + b

middleware = EunomiaMcpMiddleware()
app = mcp.add_middleware(middleware)

if __name__ == "__main__":
    mcp.run()
```


------------------------------------------------------------

## Configure Access Policies
类型: setup, 行数: 5

### Configure Access Policies

Use the `eunomia-mcp` CLI in your terminal to manage your authorization policies:

```bash

------------------------------------------------------------

## Create a default policy configuration file
类型: setup, 行数: 7

# Create a default policy configuration file
eunomia-mcp init
```

This creates a policy file you can customize to control access to your MCP tools and resources.

```bash

------------------------------------------------------------

## Once ready, validate your policy
类型: docs, 行数: 3

# Once ready, validate your policy
eunomia-mcp validate mcp_policies.json


------------------------------------------------------------

## And push it to the Eunomia server
类型: docs, 行数: 4

# And push it to the Eunomia server
eunomia-mcp push mcp_policies.json
```


------------------------------------------------------------

## Run the Server
类型: docs, 行数: 23

### Run the Server

Start your FastMCP server normally:

```bash
python server.py
```

The middleware will now intercept all MCP requests and check them against your policies. Requests include agent identification through headers like `X-Agent-ID`, `X-User-ID`, `User-Agent`, or `Authorization` and an automatic mapping of MCP methods to authorization resources and actions.

<Tip>
  For detailed policy configuration, custom authentication, and advanced
  deployment patterns, visit the [Eunomia MCP Middleware
  repository][eunomia-mcp-github].
</Tip>

[eunomia-github]: https://github.com/whataboutyou-ai/eunomia

[eunomia-mcp-github]: https://github.com/whataboutyou-ai/eunomia/tree/main/pkgs/extensions/mcp

[fastmcp-middleare]: /servers/middleware



------------------------------------------------------------

## FastAPI 🤝 FastMCP
类型: api, 行数: 29

# FastAPI 🤝 FastMCP
Source: https://gofastmcp.com/integrations/fastapi

Integrate FastMCP with FastAPI applications

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

FastMCP provides two powerful ways to integrate with FastAPI applications, both of which are documented below.

1. You can [generate an MCP server FROM your FastAPI app](#generating-an-mcp-server) by converting existing API endpoints into MCP tools. This is useful for bootstrapping and quickly attaching LLMs to your API.
2. You can [mount an MCP server INTO your FastAPI app](#mounting-an-mcp-server) by adding MCP functionality to your web application. This is useful for exposing your MCP tools alongside regular API endpoints.

You can even combine both approaches to create a single FastAPI app that serves both regular API endpoints and MCP tools!

<Tip>
  Generating MCP servers from FastAPI apps is a great way to get started with FastMCP, but in practice LLMs achieve **significantly better performance** with well-designed and curated MCP servers than with auto-converted FastAPI servers. This is especially true for complex APIs with many endpoints and parameters.
</Tip>

<Note>
  FastMCP does *not* include FastAPI as a dependency; you must install it separately to use this integration.
</Note>


------------------------------------------------------------

## Generating an MCP Server
类型: docs, 行数: 10

## Generating an MCP Server

<VersionBadge version="2.0.0" />

FastMCP can directly convert your existing FastAPI applications into MCP servers, allowing AI models to interact with your API endpoints through the MCP protocol.

<Tip>
  Under the hood, the FastAPI integration is built on top of FastMCP's OpenAPI integration. See the [OpenAPI docs](/integrations/openapi) for more details.
</Tip>


------------------------------------------------------------

## Create a Server
类型: docs, 行数: 8

### Create a Server

The simplest way to convert a FastAPI app is using the `FastMCP.from_fastapi()` method:

```python server.py
from fastapi import FastAPI
from fastmcp import FastMCP


------------------------------------------------------------

## Your existing FastAPI app
类型: api, 行数: 15

# Your existing FastAPI app
app = FastAPI(title="My API", version="1.0.0")

@app.get("/items", tags=["items"], operation_id="list_items")
def list_items():
    return [{"id": 1, "name": "Item 1"}, {"id": 2, "name": "Item 2"}]

@app.get("/items/{item_id}", tags=["items", "detail"], operation_id="get_item")
def get_item(item_id: int):
    return {"id": item_id, "name": f"Item {item_id}"}

@app.post("/items", tags=["items", "create"], operation_id="create_item")
def create_item(name: str):
    return {"id": 3, "name": name}


------------------------------------------------------------

## Convert FastAPI app to MCP server
类型: api, 行数: 7

# Convert FastAPI app to MCP server
mcp = FastMCP.from_fastapi(app=app)

if __name__ == "__main__":
    mcp.run()  # Run as MCP server
```


------------------------------------------------------------

## Component Mapping
类型: docs, 行数: 9

### Component Mapping

By default, FastMCP converts **every endpoint** in your FastAPI app into an MCP **Tool**. This provides maximum compatibility with LLM clients that primarily support MCP tools.

You can customize this behavior using route maps to control which endpoints become tools, resources, or resource templates:

```python
from fastmcp.server.openapi import RouteMap, MCPType


------------------------------------------------------------

## Custom route mapping
类型: docs, 行数: 4

# Custom route mapping
mcp = FastMCP.from_fastapi(
    app=app,
    route_maps=[

------------------------------------------------------------

## GET requests with path parameters become ResourceTemplates
类型: docs, 行数: 2

        # GET requests with path parameters become ResourceTemplates
        RouteMap(methods=["GET"], pattern=r".*\{.*\}.*", mcp_type=MCPType.RESOURCE_TEMPLATE),

------------------------------------------------------------

## All other GET requests become Resources
类型: docs, 行数: 2

        # All other GET requests become Resources
        RouteMap(methods=["GET"], pattern=r".*", mcp_type=MCPType.RESOURCE),

------------------------------------------------------------

## POST/PUT/DELETE become Tools (handled by default rule)
类型: docs, 行数: 7

        # POST/PUT/DELETE become Tools (handled by default rule)
    ],
)
```

The `FastMCP.from_fastapi()` method accepts all the same configuration options as `FastMCP.from_openapi()`, including route maps, custom tags, component naming, timeouts, and component customization functions. For comprehensive configuration details, see the [OpenAPI Integration guide](/integrations/openapi).


------------------------------------------------------------

## Key Considerations
类型: docs, 行数: 2

### Key Considerations


------------------------------------------------------------

## Operation IDs
类型: docs, 行数: 9

#### Operation IDs

FastMCP uses your FastAPI operation IDs to name MCP components. Ensure your endpoints have meaningful operation IDs:

```python
@app.get("/users/{user_id}", operation_id="get_user_detail")  # ✅ Good
@app.get("/users/{user_id}")  # ❌ Auto-generated name might be unclear
```


------------------------------------------------------------

## Pydantic Models
类型: docs, 行数: 19

#### Pydantic Models

Your Pydantic models are automatically converted to JSON schema for MCP tool parameters:

```python
from pydantic import BaseModel

class CreateItemRequest(BaseModel):
    name: str
    description: str | None = None
    price: float

@app.post("/items")
def create_item(item: CreateItemRequest):
    return {"id": 123, **item.dict()}
```

The MCP tool will have properly typed parameters matching your Pydantic model.


------------------------------------------------------------

## Error Handling
类型: docs, 行数: 6

#### Error Handling

FastAPI error handling carries over to the MCP server. HTTPExceptions are automatically converted to appropriate MCP errors.

Since FastAPI integration is built on OpenAPI, all the same configuration options are available including authentication setup, timeout configuration, and request parameter handling. For detailed information on these features, see the [OpenAPI Integration guide](/integrations/openapi).


------------------------------------------------------------

## Mounting an MCP Server
类型: docs, 行数: 6

## Mounting an MCP Server

<VersionBadge version="2.3.1" />

You can also mount an existing FastMCP server into your FastAPI application, adding MCP functionality to your web application. This is useful for exposing your MCP tools alongside regular API endpoints.


------------------------------------------------------------

## Basic Integration
类型: docs, 行数: 7

### Basic Integration

```python
from fastmcp import FastMCP
from fastapi import FastAPI
from starlette.routing import Mount


------------------------------------------------------------

## Create your FastMCP server
类型: docs, 行数: 8

# Create your FastMCP server
mcp = FastMCP("MyServer")

@mcp.tool
def analyze_data(query: str) -> dict:
    """Analyze data based on the query."""
    return {"result": f"Analysis for: {query}"}


------------------------------------------------------------

## Create the ASGI app from your MCP server
类型: docs, 行数: 3

# Create the ASGI app from your MCP server
mcp_app = mcp.http_app(path='/mcp')


------------------------------------------------------------

## Create a FastAPI app and mount the MCP server
类型: api, 行数: 4

# Create a FastAPI app and mount the MCP server
app = FastAPI(lifespan=mcp_app.lifespan)
app.mount("/mcp-server", mcp_app)


------------------------------------------------------------

## Add regular FastAPI routes
类型: api, 行数: 12

# Add regular FastAPI routes
@app.get("/health")
def health_check():
    return {"status": "healthy"}
```

The MCP endpoint will be available at `/mcp-server/mcp/` of your FastAPI application.

<Warning>
  For Streamable HTTP transport, you **must** pass the lifespan context from the FastMCP app to the FastAPI app. Otherwise, the FastMCP server's session manager will not be properly initialized.
</Warning>


------------------------------------------------------------

## Advanced Integration
类型: docs, 行数: 8

### Advanced Integration

You can combine both approaches - generate an MCP server from your FastAPI app AND mount additional MCP servers:

```python
from fastmcp import FastMCP
from fastapi import FastAPI


------------------------------------------------------------

## Your existing FastAPI app
类型: api, 行数: 7

# Your existing FastAPI app
app = FastAPI()

@app.get("/items")
def list_items():
    return [{"id": 1, "name": "Item 1"}]


------------------------------------------------------------

## Generate MCP server from FastAPI app
类型: api, 行数: 3

# Generate MCP server from FastAPI app
api_mcp = FastMCP.from_fastapi(app=app, name="API Server")


------------------------------------------------------------

## Create additional purpose-built MCP server
类型: docs, 行数: 8

# Create additional purpose-built MCP server
tools_mcp = FastMCP("Tools Server")

@tools_mcp.tool
def advanced_analysis(data: dict) -> dict:
    """Perform advanced analysis not available via API."""
    return {"analysis": "complex results"}


------------------------------------------------------------

## Mount the tools server into the same FastAPI app
类型: api, 行数: 11

# Mount the tools server into the same FastAPI app
tools_app = tools_mcp.http_app(path='/mcp')
app.mount("/tools", tools_app, lifespan=tools_app.lifespan)
```

Now you have:

* API endpoints converted to MCP tools (via `api_mcp`)
* Additional MCP tools available at `/tools/mcp/`
* Regular FastAPI endpoints at their original paths


------------------------------------------------------------

## Authentication and Middleware
类型: docs, 行数: 17

### Authentication and Middleware

When mounting MCP servers into FastAPI, you can leverage FastAPI's authentication and middleware:

```python
from fastapi import FastAPI, Depends, HTTPException
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if credentials.credentials != "secret-token":
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials

app = FastAPI()


------------------------------------------------------------

## Mount MCP server with authentication
类型: docs, 行数: 5

# Mount MCP server with authentication
@app.get("/secure")
def secure_endpoint(auth=Depends(verify_token)):
    return {"message": "Authenticated"}


------------------------------------------------------------

## The mounted MCP server inherits the app's security
类型: docs, 行数: 8

# The mounted MCP server inherits the app's security
mcp_app = mcp.http_app()
app.mount("/mcp", mcp_app, lifespan=mcp_app.lifespan)
```

For more advanced ASGI integration patterns, see the [ASGI Integration guide](/integrations/asgi).



------------------------------------------------------------

## Gemini SDK 🤝 FastMCP
类型: docs, 行数: 16

# Gemini SDK 🤝 FastMCP
Source: https://gofastmcp.com/integrations/gemini

Call FastMCP servers from the Google Gemini SDK

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

Google's Gemini API includes built-in support for MCP servers in their Python and JavaScript SDKs, allowing you to connect directly to MCP servers and use their tools seamlessly with Gemini models.


------------------------------------------------------------

## Gemini Python SDK
类型: docs, 行数: 12

## Gemini Python SDK

Google's [Gemini Python SDK](https://ai.google.dev/gemini-api/docs) can use FastMCP clients directly.

<Note>
  Google's MCP integration is currently experimental and available in the Python and JavaScript SDKs. The API automatically calls MCP tools when needed and can connect to both local and remote MCP servers.
</Note>

<Tip>
  Currently, Gemini's MCP support only accesses **tools** from MCP servers—it queries the `list_tools` endpoint and exposes those functions to the AI. Other MCP features like resources and prompts are not currently supported.
</Tip>


------------------------------------------------------------

## Create a Server
类型: docs, 行数: 19

### Create a Server

First, create a FastMCP server with the tools you want to expose. For this example, we'll create a server with a single tool that rolls dice.

```python server.py
import random
from fastmcp import FastMCP

mcp = FastMCP(name="Dice Roller")

@mcp.tool
def roll_dice(n_dice: int) -> list[int]:
    """Roll `n_dice` 6-sided dice and return the results."""
    return [random.randint(1, 6) for _ in range(n_dice)]

if __name__ == "__main__":
    mcp.run()
```


------------------------------------------------------------

## Call the Server
类型: docs, 行数: 46

### Call the Server

To use the Gemini API with MCP, you'll need to install the Google Generative AI SDK:

```bash
pip install google-genai
```

You'll also need to authenticate with Google. You can do this by setting the `GEMINI_API_KEY` environment variable. Consult the Gemini SDK documentation for more information.

```bash
export GEMINI_API_KEY="your-api-key"
```

Gemini's SDK interacts directly with the MCP client session. To call the server, you'll need to instantiate a FastMCP client, enter its connection context, and pass the client session to the Gemini SDK.

```python {5, 9, 15}
from fastmcp import Client
from google import genai
import asyncio

mcp_client = Client("server.py")
gemini_client = genai.Client()

async def main():    
    async with mcp_client:
        response = await gemini_client.aio.models.generate_content(
            model="gemini-2.0-flash",
            contents="Roll 3 dice!",
            config=genai.types.GenerateContentConfig(
                temperature=0,
                tools=[mcp_client.session],  # Pass the FastMCP client session
            ),
        )
        print(response.text)

if __name__ == "__main__":
    asyncio.run(main())
```

If you run this code, you'll see output like:

```text
Okay, I rolled 3 dice and got a 5, 4, and 1.
```


------------------------------------------------------------

## Remote & Authenticated Servers
类型: docs, 行数: 19

### Remote & Authenticated Servers

In the above example, we connected to our local server using `stdio` transport. Because we're using a FastMCP client, you can also connect to any local or remote MCP server, using any [transport](/clients/transports) or [auth](/clients/auth) method supported by FastMCP, simply by changing the client configuration.

For example, to connect to a remote, authenticated server, you can use the following client:

```python
from fastmcp import Client
from fastmcp.client.auth import BearerAuth

mcp_client = Client(
    "https://my-server.com/mcp/",
    auth=BearerAuth("<your-token>"),
)
```

The rest of the code remains the same.



------------------------------------------------------------

## MCP JSON Configuration 🤝 FastMCP
类型: setup, 行数: 18

# MCP JSON Configuration 🤝 FastMCP
Source: https://gofastmcp.com/integrations/mcp-json-configuration

Generate standard MCP configuration files for any compatible client

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.10.3" />

FastMCP can generate standard MCP JSON configuration files that work with any MCP-compatible client including Claude Desktop, VS Code, Cursor, and other applications that support the Model Context Protocol.


------------------------------------------------------------

## MCP JSON Configuration Standard
类型: setup, 行数: 4

## MCP JSON Configuration Standard

The MCP JSON configuration format is an **emergent standard** that has developed across the MCP ecosystem. This format defines how MCP clients should configure and launch MCP servers, providing a consistent way to specify server commands, arguments, and environment variables.


------------------------------------------------------------

## Configuration Structure
类型: setup, 行数: 18

### Configuration Structure

The standard uses a `mcpServers` object where each key represents a server name and the value contains the server's configuration:

```json
{
  "mcpServers": {
    "server-name": {
      "command": "executable",
      "args": ["arg1", "arg2"],
      "env": {
        "VAR": "value"
      }
    }
  }
}
```


------------------------------------------------------------

## Server Configuration Fields
类型: setup, 行数: 2

### Server Configuration Fields


------------------------------------------------------------

## `command` (required)
类型: docs, 行数: 10

#### `command` (required)

The executable command to run the MCP server. This should be an absolute path or a command available in the system PATH.

```json
{
  "command": "python"
}
```


------------------------------------------------------------

## `args` (optional)
类型: docs, 行数: 10

#### `args` (optional)

An array of command-line arguments passed to the server executable. Arguments are passed in order.

```json
{
  "args": ["server.py", "--verbose", "--port", "8080"]
}
```


------------------------------------------------------------

## `env` (optional)
类型: docs, 行数: 14

#### `env` (optional)

An object containing environment variables to set when launching the server. All values must be strings.

```json
{
  "env": {
    "API_KEY": "secret-key",
    "DEBUG": "true",
    "PORT": "8080"
  }
}
```


------------------------------------------------------------

## Client Adoption
类型: docs, 行数: 9

### Client Adoption

This format is widely adopted across the MCP ecosystem:

* **Claude Desktop**: Uses `~/.claude/claude_desktop_config.json`
* **Cursor**: Uses `~/.cursor/mcp.json`
* **VS Code**: Uses workspace `.vscode/mcp.json`
* **Other clients**: Many MCP-compatible applications follow this standard


------------------------------------------------------------

## Overview
类型: docs, 行数: 14

## Overview

<Note>
  **For the best experience, use FastMCP's first-class integrations:** [`fastmcp install claude-code`](/integrations/claude-code), [`fastmcp install claude-desktop`](/integrations/claude-desktop), or [`fastmcp install cursor`](/integrations/cursor). Use MCP JSON generation for advanced use cases and unsupported clients.
</Note>

The `fastmcp install mcp-json` command generates configuration in the standard `mcpServers` format used across the MCP ecosystem. This is useful when:

* **Working with unsupported clients** - Any MCP client not directly integrated with FastMCP
* **CI/CD environments** - Automated configuration generation for deployments
* **Configuration sharing** - Easy distribution of server setups to team members
* **Custom tooling** - Integration with your own MCP management tools
* **Manual setup** - When you prefer to manually configure your MCP client


------------------------------------------------------------

## Basic Usage
类型: docs, 行数: 44

## Basic Usage

Generate configuration and output to stdout (useful for piping):

```bash
fastmcp install mcp-json server.py
```

This outputs the server configuration JSON that you add to the `mcpServers` object:

```json
{
  "command": "uv",
  "args": [
    "run",
    "--with",
    "fastmcp", 
    "fastmcp",
    "run",
    "/absolute/path/to/server.py"
  ]
}
```

To use this in a client configuration file, add it under a server name in the `mcpServers` object:

```json
{
  "mcpServers": {
    "My Server": {
      "command": "uv",
      "args": [
        "run",
        "--with",
        "fastmcp", 
        "fastmcp",
        "run",
        "/absolute/path/to/server.py"
      ]
    }
  }
}
```


------------------------------------------------------------

## Configuration Options
类型: setup, 行数: 2

## Configuration Options


------------------------------------------------------------

## Server Naming
类型: docs, 行数: 3

### Server Naming

```bash

------------------------------------------------------------

## Use server's built-in name (from FastMCP constructor)
类型: docs, 行数: 3

# Use server's built-in name (from FastMCP constructor)
fastmcp install mcp-json server.py


------------------------------------------------------------

## Override with custom name
类型: docs, 行数: 4

# Override with custom name
fastmcp install mcp-json server.py --name "Custom Server Name"
```


------------------------------------------------------------

## Dependencies
类型: docs, 行数: 5

### Dependencies

Add Python packages your server needs:

```bash

------------------------------------------------------------

## Single package
类型: docs, 行数: 3

# Single package
fastmcp install mcp-json server.py --with pandas


------------------------------------------------------------

## Multiple packages
类型: docs, 行数: 3

# Multiple packages  
fastmcp install mcp-json server.py --with pandas --with requests --with httpx


------------------------------------------------------------

## Editable local package
类型: docs, 行数: 15

# Editable local package
fastmcp install mcp-json server.py --with-editable ./my-package
```

You can also specify dependencies directly in your server code:

```python server.py
from fastmcp import FastMCP

mcp = FastMCP(
    name="Data Analysis Server",
    dependencies=["pandas", "matplotlib", "seaborn"]
)
```


------------------------------------------------------------

## Environment Variables
类型: docs, 行数: 3

### Environment Variables

```bash

------------------------------------------------------------

## Individual environment variables
类型: docs, 行数: 5

# Individual environment variables
fastmcp install mcp-json server.py \
  --env-var API_KEY=your-secret-key \
  --env-var DEBUG=true


------------------------------------------------------------

## Load from .env file
类型: docs, 行数: 4

# Load from .env file
fastmcp install mcp-json server.py --env-file .env
```


------------------------------------------------------------

## Server Object Selection
类型: docs, 行数: 5

### Server Object Selection

Use the same `file.py:object` notation as other FastMCP commands:

```bash

------------------------------------------------------------

## Auto-detects server object (looks for 'mcp', 'server', or 'app')
类型: docs, 行数: 3

# Auto-detects server object (looks for 'mcp', 'server', or 'app')
fastmcp install mcp-json server.py


------------------------------------------------------------

## Explicit server object
类型: docs, 行数: 4

# Explicit server object
fastmcp install mcp-json server.py:my_custom_server
```


------------------------------------------------------------

## Clipboard Integration
类型: docs, 行数: 12

## Clipboard Integration

Copy configuration directly to your clipboard for easy pasting:

```bash
fastmcp install mcp-json server.py --copy
```

<Note>
  The `--copy` flag requires the `pyperclip` Python package. If not installed, you'll see an error message with installation instructions.
</Note>


------------------------------------------------------------

## Usage Examples
类型: tutorial, 行数: 2

## Usage Examples


------------------------------------------------------------

## Basic Server
类型: docs, 行数: 22

### Basic Server

```bash
fastmcp install mcp-json dice_server.py
```

Output:

```json
{
  "command": "uv",
  "args": [
    "run",
    "--with",
    "fastmcp",
    "fastmcp", 
    "run",
    "/home/<USER>/dice_server.py"
  ]
}
```


------------------------------------------------------------

## Production Server with Dependencies
类型: docs, 行数: 35

### Production Server with Dependencies

```bash
fastmcp install mcp-json api_server.py \
  --name "Production API Server" \
  --with requests \
  --with python-dotenv \
  --env-var API_BASE_URL=https://api.example.com \
  --env-var TIMEOUT=30
```

Output:

```json
{
  "command": "uv",
  "args": [
    "run",
    "--with",
    "fastmcp",
    "--with",
    "python-dotenv", 
    "--with",
    "requests",
    "fastmcp",
    "run", 
    "/home/<USER>/api_server.py"
  ],
  "env": {
    "API_BASE_URL": "https://api.example.com",
    "TIMEOUT": "30"
  }
}
```


------------------------------------------------------------

## Pipeline Usage
类型: docs, 行数: 11

### Pipeline Usage

Save configuration to file:

```bash
fastmcp install mcp-json server.py > mcp-config.json
```

Use in shell scripts:

```bash

------------------------------------------------------------

## !/bin/bash
类型: docs, 行数: 3

#!/bin/bash
CONFIG=$(fastmcp install mcp-json server.py --name "CI Server")
echo "$CONFIG" | jq '.command'

------------------------------------------------------------

## Output: "uv"
类型: docs, 行数: 3

# Output: "uv"
```


------------------------------------------------------------

## Integration with MCP Clients
类型: docs, 行数: 4

## Integration with MCP Clients

The generated configuration works with any MCP-compatible application:


------------------------------------------------------------

## Claude Desktop
类型: docs, 行数: 8

### Claude Desktop

<Note>
  **Prefer [`fastmcp install claude-desktop`](/integrations/claude-desktop)** for automatic installation. Use MCP JSON for advanced configuration needs.
</Note>

Copy the `mcpServers` object into `~/.claude/claude_desktop_config.json`


------------------------------------------------------------

## Cursor
类型: docs, 行数: 8

### Cursor

<Note>
  **Prefer [`fastmcp install cursor`](/integrations/cursor)** for automatic installation. Use MCP JSON for advanced configuration needs.
</Note>

Add to `~/.cursor/mcp.json`


------------------------------------------------------------

## VS Code
类型: docs, 行数: 4

### VS Code

Add to your workspace's `.vscode/mcp.json` file


------------------------------------------------------------

## Custom Applications
类型: docs, 行数: 4

### Custom Applications

Use the JSON configuration with any application that supports the MCP protocol


------------------------------------------------------------

## Configuration Format
类型: setup, 行数: 28

## Configuration Format

The generated configuration follows the standard MCP server specification:

```json
{
  "mcpServers": {
    "<server-name>": {
      "command": "<executable>",
      "args": ["<arg1>", "<arg2>", "..."],
      "env": {
        "<ENV_VAR>": "<value>"
      }
    }
  }
}
```

**Fields:**

* `command`: The executable to run (always `uv` for FastMCP servers)
* `args`: Command-line arguments including dependencies and server path
* `env`: Environment variables (only included if specified)

<Warning>
  **All file paths in the generated configuration are absolute paths**. This ensures the configuration works regardless of the working directory when the MCP client starts the server.
</Warning>


------------------------------------------------------------

## Requirements
类型: docs, 行数: 8

## Requirements

* **uv**: Must be installed and available in your system PATH
* **pyperclip** (optional): Required only for `--copy` functionality

Install uv if not already available:

```bash

------------------------------------------------------------

## macOS
类型: docs, 行数: 3

# macOS
brew install uv


------------------------------------------------------------

## Linux/Windows
类型: docs, 行数: 5

# Linux/Windows  
curl -LsSf https://astral.sh/uv/install.sh | sh
```



------------------------------------------------------------

## OpenAI API 🤝 FastMCP
类型: api, 行数: 14

# OpenAI API 🤝 FastMCP
Source: https://gofastmcp.com/integrations/openai

Call FastMCP servers from the OpenAI API

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};


------------------------------------------------------------

## Responses API
类型: api, 行数: 12

## Responses API

OpenAI's [Responses API](https://platform.openai.com/docs/api-reference/responses) supports [MCP servers](https://platform.openai.com/docs/guides/tools-remote-mcp) as remote tool sources, allowing you to extend AI capabilities with custom functions.

<Note>
  The Responses API is a distinct API from OpenAI's Completions API or Assistants API. At this time, only the Responses API supports MCP.
</Note>

<Tip>
  Currently, the Responses API only accesses **tools** from MCP servers—it queries the `list_tools` endpoint and exposes those functions to the AI agent. Other MCP features like resources and prompts are not currently supported.
</Tip>


------------------------------------------------------------

## Create a Server
类型: docs, 行数: 19

### Create a Server

First, create a FastMCP server with the tools you want to expose. For this example, we'll create a server with a single tool that rolls dice.

```python server.py
import random
from fastmcp import FastMCP

mcp = FastMCP(name="Dice Roller")

@mcp.tool
def roll_dice(n_dice: int) -> list[int]:
    """Roll `n_dice` 6-sided dice and return the results."""
    return [random.randint(1, 6) for _ in range(n_dice)]

if __name__ == "__main__":
    mcp.run(transport="http", port=8000)
```


------------------------------------------------------------

## Deploy the Server
类型: docs, 行数: 22

### Deploy the Server

Your server must be deployed to a public URL in order for OpenAI to access it.

For development, you can use tools like `ngrok` to temporarily expose a locally-running server to the internet. We'll do that for this example (you may need to install `ngrok` and create a free account), but you can use any other method to deploy your server.

Assuming you saved the above code as `server.py`, you can run the following two commands in two separate terminals to deploy your server and expose it to the internet:

<CodeGroup>
  ```bash FastMCP server
  python server.py
  ```

  ```bash ngrok
  ngrok http 8000
  ```
</CodeGroup>

<Warning>
  This exposes your unauthenticated server to the internet. Only run this command in a safe environment if you understand the risks.
</Warning>


------------------------------------------------------------

## Call the Server
类型: docs, 行数: 19

### Call the Server

To use the Responses API, you'll need to install the OpenAI Python SDK (not included with FastMCP):

```bash
pip install openai
```

You'll also need to authenticate with OpenAI. You can do this by setting the `OPENAI_API_KEY` environment variable. Consult the OpenAI SDK documentation for more information.

```bash
export OPENAI_API_KEY="your-api-key"
```

Here is an example of how to call your server from Python. Note that you'll need to replace `https://your-server-url.com` with the actual URL of your server. In addition, we use `/mcp/` as the endpoint because we deployed a streamable-HTTP server with the default path; you may need to use a different endpoint if you customized your server's deployment.

```python {4, 11-16}
from openai import OpenAI


------------------------------------------------------------

## Your server URL (replace with your actual URL)
类型: docs, 行数: 27

# Your server URL (replace with your actual URL)
url = 'https://your-server-url.com'

client = OpenAI()

resp = client.responses.create(
    model="gpt-4.1",
    tools=[
        {
            "type": "mcp",
            "server_label": "dice_server",
            "server_url": f"{url}/mcp/",
            "require_approval": "never",
        },
    ],
    input="Roll a few dice!",
)

print(resp.output_text)
```

If you run this code, you'll see something like the following output:

```text
You rolled 3 dice and got the following results: 6, 4, and 2!
```


------------------------------------------------------------

## Authentication
类型: docs, 行数: 6

### Authentication

<VersionBadge version="2.6.0" />

The Responses API can include headers to authenticate the request, which means you don't have to worry about your server being publicly accessible.


------------------------------------------------------------

