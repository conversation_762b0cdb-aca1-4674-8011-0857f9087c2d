# MySQL数据库分析MCP服务器依赖

# 核心框架
fastmcp>=2.0.0

# 数据库连接
mysql-connector-python>=8.0.0

# 数据处理和分析
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.7.0

# 图表生成
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
kaleido>=0.2.1

# 语音功能
pyttsx3>=2.90
speechrecognition>=3.8.0
pyaudio>=0.2.11

# 工具库
python-dateutil>=2.8.0
pytz>=2021.3

# HTTP API桥接器
fastapi>=0.104.0
uvicorn>=0.24.0

# 可选：机器学习库（用于高级分析）
scikit-learn>=1.0.0

# 可选：更好的语音识别支持
# baidu-aip>=4.16.0  # 百度语音API
# azure-cognitiveservices-speech>=1.24.0  # Azure语音服务
