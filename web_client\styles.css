/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 强制保持页面字体不被ECharts影响 */
.container,
.header,
.sidebar,
.main-content,
.content-section,
.nav-item,
.section-title,
.form-group,
.btn,
.stats-grid,
.stat-card,
.notification,
h1, h2, h3, h4, h5, h6,
p, span, div, label, input, select, textarea {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* 只有图表容器内部使用ECharts默认字体 */
.chart-container canvas,
[id*="Chart"] canvas,
[id*="chart"] canvas {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif !important;
}

/* 确保ECharts不影响页面布局 */
.echarts-for-react,
.echarts-container {
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.5rem;
    color: #333;
    margin: 0;
}

.version {
    background: #4CAF50;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-indicator.online {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-indicator.online::before {
    background: #4caf50;
}

.status-indicator.offline {
    background: #ffebee;
    color: #c62828;
}

.status-indicator.offline::before {
    background: #f44336;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    gap: 2rem;
    padding: 2rem;
}

/* 侧边栏 */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 120px;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section h3 {
    color: #555;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-section ul {
    list-style: none;
}

.nav-section li {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: block;
    padding: 0.75rem 1rem;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.nav-link:hover {
    background: #f5f5f5;
    color: #333;
    transform: translateX(4px);
}

.nav-link.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: bold;
}

/* 内容区域 */
.content-area {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    min-height: 600px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.page-header h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #666;
    font-size: 0.875rem;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-voice {
    background: #ff6b6b;
    color: white;
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn-voice:hover {
    background: #ff5252;
    transform: translateY(-2px);
}

.btn-voice:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
    color: #333;
    font-size: 1.1rem;
}

.card-content {
    color: #666;
}

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.dashboard-grid .card.full-width {
    grid-column: 1 / -1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* 分析表单 */
.analysis-form {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

/* 表单分组 */
.form-section {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-section h4 {
    margin: 0 0 15px 0;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    width: auto !important;
    margin: 0;
}

/* 操作按钮区域 */
.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e5e7eb;
    flex-wrap: wrap;
}

.form-actions .btn {
    flex: 1;
    min-width: 150px;
    max-width: 200px;
}

/* 异常检测结果样式 */
.anomaly-result {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.anomaly-result.warning {
    border-left: 4px solid #f59e0b;
}

.anomaly-result.critical {
    border-left: 4px solid #ef4444;
}

.anomaly-result.info {
    border-left: 4px solid #3b82f6;
}

.time-range-info {
    background: #f3f4f6;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #6b7280;
}

.anomaly-list {
    margin-top: 20px;
}

.anomaly-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 6px;
    background: #f9fafb;
    border-left: 3px solid #e5e7eb;
}

.anomaly-item.warning {
    background: #fef3c7;
    border-left-color: #f59e0b;
}

.anomaly-item.critical {
    background: #fee2e2;
    border-left-color: #ef4444;
}

.anomaly-item.info {
    background: #dbeafe;
    border-left-color: #3b82f6;
}

.anomaly-index {
    background: #6b7280;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    flex-shrink: 0;
}

.anomaly-value {
    font-weight: 600;
    color: #374151;
}

.anomaly-reason {
    color: #6b7280;
    font-size: 14px;
}

.anomaly-time {
    color: #9ca3af;
    font-size: 12px;
    margin-left: auto;
}

.more-anomalies {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    margin-top: 10px;
}

.stat-value.warning {
    color: #f59e0b;
    font-weight: bold;
}

.stat-value.critical {
    color: #ef4444;
    font-weight: bold;
}

.stat-value.info {
    color: #3b82f6;
    font-weight: bold;
}

/* 实时监控状态样式 */
.monitoring-status {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.status-header h4 {
    margin: 0;
    color: white;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-weight: 500;
}

.monitoring-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 12px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 16px;
    font-weight: 600;
}

/* 告警横幅样式 */
.alert-banner {
    margin-bottom: 15px;
    border-radius: 8px;
    padding: 12px;
    animation: slideDown 0.3s ease-out;
}

.alert-banner.info {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.alert-banner.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.alert-banner.critical {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    animation: slideDown 0.3s ease-out, pulse 1s infinite;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.alert-icon {
    font-size: 20px;
    animation: bounce 1s infinite;
}

.alert-text {
    flex: 1;
    font-weight: 500;
}

.alert-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    transition: background 0.2s;
}

.alert-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* 语音控制样式 */
.btn-voice {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.btn-voice:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-voice.listening {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    animation: pulse 1s infinite;
}

.btn-voice.listening::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 1.5s infinite;
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

.voice-status {
    margin-top: 8px;
    padding: 8px 12px;
    background: #f3f4f6;
    border-radius: 6px;
    font-size: 14px;
    color: #6b7280;
    text-align: center;
    min-height: 20px;
}

.voice-status.listening {
    background: #fee2e2;
    color: #dc2626;
    animation: pulse 1s infinite;
}

.voice-status.processing {
    background: #dbeafe;
    color: #2563eb;
}

.voice-status.success {
    background: #d1fae5;
    color: #059669;
}

/* 结果区域 */
.result-area {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: 200px;
}

/* 图表控制 */
.chart-controls {
    margin-bottom: 2rem;
}

.chart-type-selector {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.chart-type-btn {
    padding: 1rem 1.5rem;
    border: 2px solid #e0e0e0;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.chart-type-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.chart-type-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.chart-form-section {
    display: none;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
}

.chart-form-section.active {
    display: block;
}

.chart-display-area {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

/* SQL编辑器 */
.sql-editor {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.editor-actions {
    display: flex;
    gap: 0.5rem;
}

.sql-textarea {
    width: 100%;
    min-height: 200px;
    padding: 1.5rem;
    border: none;
    border-radius: 0 0 12px 12px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
}

.sql-textarea:focus {
    outline: none;
}

/* 语音界面 */
.voice-interface {
    text-align: center;
}

.voice-controls {
    margin-bottom: 2rem;
}

.voice-output {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.voice-text, .voice-response {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.voice-result {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    min-height: 100px;
    margin-top: 1rem;
    font-family: monospace;
}

.voice-commands {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: left;
}

.voice-commands ul {
    list-style: none;
    padding-left: 0;
}

.voice-commands li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.voice-commands li:last-child {
    border-bottom: none;
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #666;
}

.loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e0e0e0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.empty-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
}

/* 图表容器 */
.chart-container {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-container img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 通知系统 */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.notification {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 1rem;
    border-left: 4px solid #4CAF50;
    animation: slideIn 0.3s ease;
}

.notification.error {
    border-left-color: #f44336;
}

.notification.warning {
    border-left-color: #ff9800;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 结果表格样式 */
.result-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-table th,
.result-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.result-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.result-table tr:hover {
    background: #f8f9fa;
}

.result-note {
    margin-top: 1rem;
    padding: 0.75rem;
    background: #e3f2fd;
    border-radius: 8px;
    color: #1976d2;
    font-size: 0.875rem;
}

/* 异常检测结果样式 */
.anomaly-list {
    margin-top: 1rem;
}

.anomaly-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    margin-bottom: 0.5rem;
    border-radius: 4px;
}

.anomaly-index {
    background: #ff9800;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
}

.anomaly-value {
    font-weight: bold;
    color: #333;
}

.anomaly-reason {
    color: #666;
    font-size: 0.875rem;
}

/* 错误状态样式 */
.error {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #f44336;
}

/* 数据库信息样式 */
.db-info p {
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.db-info p:last-child {
    border-bottom: none;
}

/* 图表结果样式 */
.chart-result h3 {
    margin-bottom: 1rem;
    color: #333;
    text-align: center;
}

.chart-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.chart-info p {
    margin-bottom: 0.5rem;
    color: #666;
}

/* 统计结果样式 */
.stats-result h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* 增强的统计分析样式 */
.stats-result {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.stats-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.stats-timestamp {
    font-size: 0.85rem;
    color: #666;
    background: #f8f9fa;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border-left: 4px solid #6366f1;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-label {
    display: block;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.stat-value {
    display: block;
    font-size: 1.4rem;
    font-weight: bold;
    color: #333;
}

.stats-actions {
    display: flex;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.stats-actions .btn {
    flex: 1;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* 复选框组样式 */
.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #555;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.checkbox-label:hover {
    background-color: #f8f9fa;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.1);
}

/* 表单操作按钮样式 */
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #f0f0f0;
}

.form-actions .btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 结果区域样式 */
.result-area {
    margin-top: 2rem;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.result-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.result-controls {
    display: flex;
    gap: 0.5rem;
}

.result-controls .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.result-controls .btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.result-content {
    background: #fff;
    border-radius: 0 0 10px 10px;
    min-height: 200px;
    border: 1px solid #e5e7eb;
    border-top: none;
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #9ca3af;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-text {
    font-size: 1rem;
    text-align: center;
}

/* 趋势分析样式 */
.trend-result {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
}

.trend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.trend-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.trend-timestamp {
    font-size: 0.85rem;
    color: #666;
    background: #f8f9fa;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.trend-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.trend-metric {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border-left: 4px solid #8b5cf6;
    transition: all 0.3s ease;
}

.trend-metric:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.metric-content {
    flex: 1;
}

.metric-label {
    display: block;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.metric-value {
    display: block;
    font-size: 1.4rem;
    font-weight: bold;
    color: #333;
}

.metric-value.positive {
    color: #10b981;
}

.metric-value.negative {
    color: #ef4444;
}

.metric-value.neutral {
    color: #6b7280;
}

.trend-chart-container {
    margin: 1.5rem 0;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.trend-forecast {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.trend-forecast h5 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1rem;
}

.forecast-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
}

.forecast-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.forecast-period {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.forecast-value {
    font-size: 1.1rem;
    font-weight: bold;
    color: #8b5cf6;
}

.trend-insights {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #fef3c7;
    border-radius: 8px;
    border-left: 4px solid #f59e0b;
}

.trend-insights h5 {
    margin: 0 0 1rem 0;
    color: #92400e;
    font-size: 1rem;
}

.insights-content {
    color: #92400e;
}

.insight-item {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.insight-item:last-child {
    margin-bottom: 0;
}

.trend-actions {
    display: flex;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.trend-actions .btn {
    flex: 1;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.sql-result h3 {
    margin-bottom: 1rem;
    color: #333;
}

.anomaly-result h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        padding: 1rem;
    }

    .sidebar {
        width: 100%;
        position: static;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .voice-output {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .chart-type-selector {
        flex-direction: column;
    }

    .result-table {
        font-size: 0.75rem;
    }

    .result-table th,
    .result-table td {
        padding: 0.5rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .alert-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .gallery-controls {
        flex-direction: column;
    }
}

/* 图表库样式 */
.gallery-controls {
    margin-bottom: 2rem;
    display: flex;
    gap: 1rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.gallery-item:hover {
    transform: translateY(-2px);
}

.chart-thumbnail {
    text-align: center;
    padding: 2rem;
}

.chart-icon {
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
}

/* 提醒管理样式 */
.alert-form {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-list {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.alert-info h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.alert-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.alert-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.alert-status.active {
    background: #e8f5e8;
    color: #2e7d32;
}

.alert-actions {
    display: flex;
    gap: 0.5rem;
}

/* 趋势分析样式 */
.trend-result {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.trend-summary {
    margin-top: 1rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 6px;
}

.trend-summary p {
    margin-bottom: 0.5rem;
    color: #666;
}

/* 企业级图表样式 */
.chart-theme-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 2rem;
}

.chart-theme-selector label {
    font-weight: bold;
    color: #333;
}

.chart-actions {
    display: flex;
    gap: 1rem;
    margin-left: auto;
}

.chart-container-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    min-height: 500px;
}

.enterprise-chart-container {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

.chart-info-panel {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.chart-stats {
    margin-bottom: 2rem;
}

.chart-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.chart-stats .stat-item:last-child {
    border-bottom: none;
}

.chart-stats .stat-label {
    font-weight: 500;
    color: #666;
}

.chart-stats .stat-value {
    font-weight: bold;
    color: #333;
}

.chart-insights h4 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.1rem;
}

.insights-content {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #666;
}

.insights-content p {
    margin-bottom: 0.5rem;
}

.insights-content ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.insights-content li {
    margin-bottom: 0.25rem;
}

/* 图表类型按钮扩展 */
.chart-type-btn[data-type="scatter"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chart-type-btn[data-type="heatmap"] {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 全屏模式 */
.chart-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: white;
    z-index: 9999;
    display: flex;
    flex-direction: column;
}

.chart-fullscreen .enterprise-chart-container {
    flex: 1;
    margin: 1rem;
    min-height: calc(100vh - 4rem);
}

.chart-fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.chart-fullscreen-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

.chart-fullscreen-close {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.chart-fullscreen-close:hover {
    background: #c82333;
}

/* 响应式设计扩展 */
@media (max-width: 1024px) {
    .chart-container-wrapper {
        grid-template-columns: 1fr;
    }

    .chart-theme-selector {
        margin-left: 0;
        margin-top: 1rem;
    }

    .chart-actions {
        margin-left: 0;
        margin-top: 1rem;
    }

    .chart-controls {
        flex-direction: column;
        gap: 1rem;
    }
}

/* 错误状态样式 */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
    color: #666;
}

.error-state .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.error-state h3 {
    margin-bottom: 0.5rem;
    color: #dc3545;
}

.error-state p {
    color: #666;
    font-size: 0.9rem;
}

/* 趋势分析结果样式 */
.trend-result {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
}

.trend-result h3 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.2rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.trend-chart-container {
    margin-bottom: 1.5rem;
    background: #fafafa;
    border-radius: 6px;
    padding: 1rem;
}

.trend-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.summary-label {
    font-weight: 500;
    color: #666;
}

.summary-value {
    font-weight: bold;
    color: #333;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* 快速图表样式优化 */
#latestChart {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-height: 300px;
}

#quickChart {
    border-radius: 6px;
    background: #fafafa;
}
