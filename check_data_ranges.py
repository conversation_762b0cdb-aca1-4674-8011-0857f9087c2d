#!/usr/bin/env python3
"""
检查数据范围
"""

import pandas as pd

def check_data_ranges():
    """检查数据范围"""
    print("🔍 检查数据范围...")
    
    df = pd.read_excel(r'date\数据库.xls', sheet_name='第1页')
    
    print("📊 数值列的范围:")
    for col in df.columns:
        if df[col].dtype in ['float64', 'int64']:
            min_val = df[col].min()
            max_val = df[col].max()
            print(f"  {col}: {min_val} 到 {max_val}")

if __name__ == "__main__":
    check_data_ranges()
