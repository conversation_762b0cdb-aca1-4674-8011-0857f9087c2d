# 🏠 完整本地MCP MySQL数据分析系统

## 🎯 系统概述

这是一个**完全本地化**的MySQL数据分析系统，基于MCP协议构建，支持语音交互，专为大数据量实时数据库设计。

### 🔧 核心特性

- ✅ **完全本地部署** - 不依赖互联网连接
- ✅ **渐进式LLM切换** - 当前OpenAI测试，将来本地LLM
- ✅ **大数据量优化** - 支持实时数据库处理
- ✅ **语音交互** - 本地TTS/ASR，中文支持
- ✅ **标准MCP协议** - 完整的工具、资源、提示支持
- ✅ **AI增强分析** - 智能异常分析、趋势洞察、优先级评估

## 🏗️ 系统架构

### 当前架构（测试阶段）
```
本地MCP客户端 (语音支持)
    ↕️ MCP协议 (本地网络)
本地MCP服务器 (完整功能)
    ↕️ LLM抽象层
OpenAI GPT-4o-mini (临时测试)
    ↕️ 
MySQL实时数据库 (大数据量)
```

### 目标架构（最终部署）
```
本地MCP客户端 (语音支持)
    ↕️ MCP协议 (本地网络)
本地MCP服务器 (完整功能)
    ↕️ LLM抽象层
本地LLM (Ollama/LM Studio)
    ↕️ 
MySQL实时数据库 (大数据量)
```

## 🚀 快速开始

### 1. 安装系统
```bash
python install_local_system.py
```

### 2. 配置数据库
编辑 `config.json` 中的数据库配置：
```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "your_password",
    "database": "your_database"
  }
}
```

### 3. 启动MCP服务器
```bash
# Windows
start_server.bat

# Linux/macOS
./start_server.sh

# 或直接运行
python local_mcp_server.py
```

### 4. 启动MCP客户端
```bash
python local_mcp_client.py
```

## 📊 核心功能

### 1. 统计分析 📈
**功能**：按条件求和、求平均、计数等
```python
# 工具调用
calculate_statistics(
    table="payment",
    column="amount", 
    operation="sum",
    start_time="2024-01-01",
    end_time="2024-12-31"
)
```

### 2. 异常检测 🔍
**功能**：检测异常数据并提供AI原因分析
```python
# 工具调用
detect_anomalies_with_ai(
    table="payment",
    column="amount",
    method="zscore",
    threshold=2.0,
    enable_ai_analysis=True
)
```

**AI分析内容**：
- 可能的业务原因
- 异常模式特征
- 建议的后续行动
- 风险评估

### 3. 智能提醒 ⚠️
**功能**：按时间或数值阈值提醒，AI优先级评估
```python
# 数值阈值提醒
create_smart_alert(
    alert_type="value_threshold",
    table="payment",
    column="amount",
    threshold=10000.0,
    operator=">",
    enable_ai_priority=True
)

# 时间提醒
create_smart_alert(
    alert_type="time_based",
    table="payment",
    alert_time="2024-12-31 23:59:59"
)
```

### 4. 图表生成 📊
**功能**：生成柱状图、饼图、折线图、散点图
```python
# 工具调用
generate_chart(
    table="payment",
    chart_type="bar",
    x_column="date",
    y_column="amount",
    title="每日支付金额"
)
```

**支持的图表类型**：
- `bar` - 柱状图
- `pie` - 饼图
- `line` - 折线图
- `scatter` - 散点图

### 5. 趋势分析 📉
**功能**：分析数据走势并提供AI洞察
```python
# 工具调用
analyze_trend_with_ai(
    table="payment",
    time_column="payment_date",
    value_column="amount",
    period="day",
    forecast_days=7,
    enable_ai_insights=True
)
```

**AI洞察内容**：
- 趋势模式分析（季节性、周期性）
- 业务含义解读
- 风险与机会识别
- 预测可信度评估
- 监控重点建议

### 6. 语音交互 🎤
**功能**：本地语音识别和合成
```python
# 语音命令
voice_command(
    command="分析payment表的异常数据",
    listen_for_command=False
)
```

**语音特性**：
- 本地TTS（文本转语音）
- 本地ASR（语音转文本）
- 中文语音支持
- 智能命令理解

## 🔧 配置说明

### LLM提供者配置
```json
{
  "llm": {
    "provider": "openai",  // 当前使用OpenAI
    "providers": {
      "openai": {
        "enabled": true,
        "model": "gpt-4o-mini"
      },
      "local": {
        "enabled": false,  // 将来启用
        "base_url": "http://127.0.0.1:11434",
        "model": "llama3.1:8b"
      }
    }
  }
}
```

### 性能优化配置
```json
{
  "database": {
    "pool_size": 20,
    "cache_enabled": true,
    "batch_size": 1000,
    "query_timeout": 60
  },
  "performance": {
    "enable_caching": true,
    "enable_query_optimization": true,
    "max_query_time": 30
  }
}
```

### 语音功能配置
```json
{
  "voice": {
    "enabled": true,
    "tts": {
      "engine": "pyttsx3",
      "rate": 150,
      "language": "zh-CN"
    },
    "asr": {
      "engine": "speech_recognition",
      "language": "zh-CN"
    }
  }
}
```

## 🔄 LLM切换指南

### 当前：OpenAI GPT-4o-mini
```json
{
  "llm": {
    "provider": "openai"
  }
}
```

### 将来：本地LLM (Ollama)
1. **安装Ollama**：
```bash
# 下载并安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull llama3.1:8b
```

2. **修改配置**：
```json
{
  "llm": {
    "provider": "local",
    "providers": {
      "local": {
        "enabled": true,
        "base_url": "http://127.0.0.1:11434",
        "model": "llama3.1:8b",
        "api_type": "ollama"
      }
    }
  }
}
```

3. **重启服务器**：
```bash
python local_mcp_server.py
```

### 将来：LM Studio
1. **安装LM Studio**：下载并安装LM Studio
2. **启动本地服务器**：在LM Studio中启动本地API服务器
3. **修改配置**：
```json
{
  "llm": {
    "provider": "lm_studio",
    "providers": {
      "lm_studio": {
        "enabled": true,
        "base_url": "http://127.0.0.1:1234/v1",
        "model": "local-model"
      }
    }
  }
}
```

## 🎤 语音交互使用

### 启动语音模式
```bash
# 在客户端中输入
voice

# 或直接说出命令
"数据助手，分析payment表的异常"
```

### 支持的语音命令
- "统计payment表amount字段的总和"
- "检测payment表amount字段的异常"
- "生成payment表的柱状图"
- "分析payment表的趋势"
- "创建金额超过1万的提醒"
- "查看系统状态"

## 📈 性能优化

### 大数据量处理
- **连接池**：支持20个并发连接
- **查询缓存**：5分钟TTL缓存
- **批量处理**：1000条记录批次
- **索引建议**：自动分析查询性能

### 实时数据支持
- **异步处理**：非阻塞查询执行
- **流式处理**：大结果集分批返回
- **内存优化**：智能内存管理
- **查询优化**：自动查询计划优化

## 🔒 安全特性

- **本地部署**：数据不离开本地网络
- **无外网依赖**：完全离线运行（除OpenAI测试期）
- **访问控制**：本地网络访问限制
- **数据加密**：传输层加密

## 🛠️ 故障排除

### 常见问题

1. **语音功能不工作**
```bash
# 检查音频设备
python -c "import speech_recognition as sr; print(sr.Microphone.list_microphone_names())"

# 重新安装语音依赖
pip install --upgrade pyttsx3 speechrecognition pyaudio
```

2. **数据库连接失败**
```bash
# 检查MySQL服务
mysql -u root -p

# 测试连接
python -c "import mysql.connector; print('MySQL可用')"
```

3. **LLM调用失败**
```bash
# 检查OpenAI连接
python -c "from openai import OpenAI; print('OpenAI可用')"

# 切换LLM提供者
python local_mcp_server.py --llm-provider local
```

## 📞 技术支持

### 系统要求
- **Python**: 3.8+
- **MySQL**: 5.7+
- **内存**: 4GB+
- **存储**: 2GB+
- **网络**: 本地网络

### 依赖库
- FastMCP 2.0+
- MySQL Connector 8.0+
- Pandas 1.5+
- NumPy 1.21+
- OpenAI 1.0+
- 语音库（pyttsx3, speechrecognition）

## 🎉 总结

这是一个**完整的本地MCP系统**，实现了客户的所有要求：

✅ **本地搭建，不联网**（除OpenAI测试期）  
✅ **当前用OpenAI测试，将来切换本地LLM**  
✅ **支持大数据量实时数据库**  
✅ **完整的语音对话功能**  
✅ **标准MCP协议架构**  
✅ **所有要求的分析功能**  

系统设计为渐进式部署：先用OpenAI验证功能，再无缝切换到本地LLM，最终实现完全离线的智能数据分析系统！
