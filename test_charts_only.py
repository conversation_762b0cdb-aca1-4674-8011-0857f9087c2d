#!/usr/bin/env python3
"""
专门测试图表功能
"""

import asyncio
import sys

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    sys.exit(1)

async def test_charts():
    """测试图表功能"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("📊 专门测试图表功能")
    print("=" * 40)
    
    try:
        async with Client(server_url) as client:
            
            # 测试柱状图
            print("📊 测试柱状图...")
            result = await client.call_tool("generate_bar_chart", {
                "table": "film",
                "x_column": "rating",
                "y_column": "rental_rate",
                "title": "电影评级与租赁费用",
                "limit": 10
            })
            if result.data.get('success'):
                print("   ✅ 柱状图生成成功！")
                print(f"   图表类型: {result.data.get('chart_type')}")
                print(f"   标题: {result.data.get('title')}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 测试饼状图
            print("\n🥧 测试饼状图...")
            result = await client.call_tool("generate_pie_chart", {
                "table": "film",
                "label_column": "rating",
                "value_column": "rental_rate",
                "title": "电影评级分布"
            })
            if result.data.get('success'):
                print("   ✅ 饼状图生成成功！")
                print(f"   图表类型: {result.data.get('chart_type')}")
                print(f"   标题: {result.data.get('title')}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 测试趋势图
            print("\n📈 测试趋势图...")
            result = await client.call_tool("generate_trend_chart", {
                "table": "payment",
                "x_column": "payment_date",
                "y_column": "amount",
                "title": "支付金额趋势",
                "time_range": "30 DAY"
            })
            if result.data.get('success'):
                print("   ✅ 趋势图生成成功！")
                print(f"   图表类型: {result.data.get('chart_type')}")
                print(f"   标题: {result.data.get('title')}")
                print(f"   时间范围: {result.data.get('time_range')}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(test_charts())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}")
