#!/usr/bin/env python3
"""
测试趋势分析功能
"""

import requests
import json
import time

def test_trend_analysis():
    """测试趋势分析功能"""
    print("🧪 测试趋势分析功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    payload = {
        "tool_name": "analyze_data_trend",
        "arguments": {
            "table": "payment",
            "column": "amount",
            "time_column": "payment_date",
            "period": "month"
        }
    }
    
    try:
        print(f"📤 发送请求到: {url}")
        print(f"📦 请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=payload, timeout=10)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 趋势分析成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ 趋势分析失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_web_interface():
    """测试Web界面的趋势分析调用"""
    print("🧪 测试Web界面趋势分析调用...")
    
    # 模拟前端的调用方式
    url = "http://127.0.0.1:8081/api/call-tool"
    payload = {
        "tool": "analyze_data_trend",
        "arguments": {
            "table": "payment",
            "column": "amount",
            "time_column": "payment_date",
            "period": "month"
        }
    }
    
    try:
        print(f"📤 发送请求到Web服务器: {url}")
        print(f"📦 请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=payload, timeout=10)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Web界面调用成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ Web界面调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Web界面请求失败: {e}")
        return False

def main():
    print("🚀 开始测试趋势分析功能...")
    print("=" * 60)
    
    # 等待系统完全启动
    print("⏳ 等待系统启动...")
    time.sleep(2)
    
    results = []
    
    # 测试直接调用简化桥接器
    results.append(("直接调用桥接器", test_trend_analysis()))
    
    print("\n" + "-" * 60)
    
    # 测试通过Web界面调用
    results.append(("Web界面调用", test_web_interface()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
