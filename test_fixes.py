#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复的功能
"""

import asyncio
import aiohttp
import json

async def test_analyze_data_trend():
    """测试趋势分析功能"""
    print("测试趋势分析功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    data = {
        "tool_name": "analyze_data_trend",
        "arguments": {
            "table": "payment",
            "time_column": "created_at",
            "value_column": "amount",
            "period": "day"
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                result = await response.json()
                print(f"状态码: {response.status}")
                print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('result', {}).get('success'):
                    print("✓ 趋势分析功能正常")
                    return True
                else:
                    print("✗ 趋势分析功能异常")
                    return False
                    
    except Exception as e:
        print(f"✗ 趋势分析测试失败: {e}")
        return False

async def test_chart_generation():
    """测试图表生成功能"""
    print("\n测试图表生成功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    data = {
        "tool_name": "generate_bar_chart",
        "arguments": {
            "table": "payment",
            "x_column": "category",
            "y_column": "amount",
            "title": "测试柱状图"
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                result = await response.json()
                print(f"状态码: {response.status}")
                print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('result', {}).get('success'):
                    print("✓ 图表生成功能正常")
                    return True
                else:
                    print("✗ 图表生成功能异常")
                    return False
                    
    except Exception as e:
        print(f"✗ 图表生成测试失败: {e}")
        return False

async def test_statistics():
    """测试统计分析功能"""
    print("\n测试统计分析功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    data = {
        "tool_name": "get_database_statistics",
        "arguments": {
            "table": "payment",
            "column": "amount"
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                result = await response.json()
                print(f"状态码: {response.status}")
                print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if response.status == 200 and result.get('result', {}).get('success'):
                    print("✓ 统计分析功能正常")
                    return True
                else:
                    print("✗ 统计分析功能异常")
                    return False
                    
    except Exception as e:
        print(f"✗ 统计分析测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("测试修复的功能")
    print("=" * 60)
    
    # 等待服务器启动
    await asyncio.sleep(2)
    
    # 运行测试
    tests = [
        test_analyze_data_trend(),
        test_chart_generation(),
        test_statistics()
    ]
    
    results = await asyncio.gather(*tests, return_exceptions=True)
    
    # 统计结果
    success_count = sum(1 for result in results if result is True)
    total_count = len(results)
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{total_count} 通过")
    print("=" * 60)
    
    if success_count == total_count:
        print("🎉 所有功能修复成功！")
    else:
        print("⚠️ 部分功能仍有问题，请检查日志")

if __name__ == "__main__":
    asyncio.run(main())
