# FastMCP 文档 - 第 9 部分
# 主要内容: `run` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/cli.py#L227"><Icon icon="github" size="14" /></a></sup>
# 包含段落: 148 个
# 总行数: 1031

================================================================================

## `run` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/cli.py#L227"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 22

### `run` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/cli.py#L227"><Icon icon="github" size="14" /></a></sup>

```python
run(ctx: typer.Context, server_spec: str = typer.Argument(..., help='Python file, object specification (file:obj), or URL'), transport: Annotated[str | None, typer.Option('--transport', '-t', help='Transport protocol to use (stdio, http, or sse)')] = None, host: Annotated[str | None, typer.Option('--host', help='Host to bind to when using http transport (default: 127.0.0.1)')] = None, port: Annotated[int | None, typer.Option('--port', '-p', help='Port to bind to when using http transport (default: 8000)')] = None, log_level: Annotated[str | None, typer.Option('--log-level', '-l', help='Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)')] = None) -> None
```

Run a MCP server or connect to a remote one.

The server can be specified in three ways:

1. Module approach: server.py - runs the module directly, looking for an object named mcp/server/app.

2. Import approach: server.py:app - imports and runs the specified server object.

3. URL approach: [http://server-url](http://server-url) - connects to a remote server and creates a proxy.

Note: This command runs the server directly. You are responsible for ensuring
all dependencies are available.

Server arguments can be passed after -- :
fastmcp run server.py -- --config config.json --debug


------------------------------------------------------------

## `install` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/cli.py#L313"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 11

### `install` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/cli.py#L313"><Icon icon="github" size="14" /></a></sup>

```python
install(server_spec: str = typer.Argument(..., help='Python file to run, optionally with :object suffix'), server_name: Annotated[str | None, typer.Option('--name', '-n', help="Custom name for the server (defaults to server's name attribute or file name)")] = None, with_editable: Annotated[Path | None, typer.Option('--with-editable', '-e', help='Directory containing pyproject.toml to install in editable mode', exists=True, file_okay=False, resolve_path=True)] = None, with_packages: Annotated[list[str], typer.Option('--with', help='Additional packages to install')] = [], env_vars: Annotated[list[str], typer.Option('--env-var', '-v', help='Environment variables in KEY=VALUE format')] = [], env_file: Annotated[Path | None, typer.Option('--env-file', '-f', help='Load environment variables from a .env file', exists=True, file_okay=True, dir_okay=False, resolve_path=True)] = None) -> None
```

Install a MCP server in the Claude desktop app.

Environment variables are preserved once added and only updated if new values
are explicitly provided.


------------------------------------------------------------

## `inspect` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/cli.py#L444"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 21

### `inspect` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/cli.py#L444"><Icon icon="github" size="14" /></a></sup>

```python
inspect(server_spec: str = typer.Argument(..., help='Python file to inspect, optionally with :object suffix'), output: Annotated[Path, typer.Option('--output', '-o', help='Output file path for the JSON report (default: server-info.json)')] = Path('server-info.json')) -> None
```

Inspect a FastMCP server and generate a JSON report.

This command analyzes a FastMCP server (v1.x or v2.x) and generates
a comprehensive JSON report containing information about the server's
name, instructions, version, tools, prompts, resources, templates,
and capabilities.

**Examples:**

fastmcp inspect server.py
fastmcp inspect server.py -o report.json
fastmcp inspect server.py:mcp -o analysis.json
fastmcp inspect path/to/server.py:app -o /tmp/server-info.json



------------------------------------------------------------

## run
类型: docs, 行数: 5

# run
Source: https://gofastmcp.com/python-sdk/fastmcp-cli-run




------------------------------------------------------------

## `fastmcp.cli.run`
类型: docs, 行数: 4

# `fastmcp.cli.run`

FastMCP run command implementation.


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `is_url` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L14"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

### `is_url` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L14"><Icon icon="github" size="14" /></a></sup>

```python
is_url(path: str) -> bool
```

Check if a string is a URL.


------------------------------------------------------------

## `parse_file_path` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L20"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 16

### `parse_file_path` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L20"><Icon icon="github" size="14" /></a></sup>

```python
parse_file_path(server_spec: str) -> tuple[Path, str | None]
```

Parse a file path that may include a server object specification.

**Args:**

* `server_spec`: Path to file, optionally with :object suffix

**Returns:**

* Tuple of (file\_path, server\_object)


------------------------------------------------------------

## `import_server` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L51"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 17

### `import_server` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L51"><Icon icon="github" size="14" /></a></sup>

```python
import_server(file: Path, server_object: str | None = None) -> Any
```

Import a MCP server from a file.

**Args:**

* `file`: Path to the file
* `server_object`: Optional object name in format "module:object" or just "object"

**Returns:**

* The server object


------------------------------------------------------------

## `create_client_server` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L121"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 16

### `create_client_server` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L121"><Icon icon="github" size="14" /></a></sup>

```python
create_client_server(url: str) -> Any
```

Create a FastMCP server from a client URL.

**Args:**

* `url`: The URL to connect to

**Returns:**

* A FastMCP server instance


------------------------------------------------------------

## `import_server_with_args` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L141"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 18

### `import_server_with_args` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L141"><Icon icon="github" size="14" /></a></sup>

```python
import_server_with_args(file: Path, server_object: str | None = None, server_args: list[str] | None = None) -> Any
```

Import a server with optional command line arguments.

**Args:**

* `file`: Path to the server file
* `server_object`: Optional server object name
* `server_args`: Optional command line arguments to inject

**Returns:**

* The imported server object


------------------------------------------------------------

## `run_command` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L165"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 18

### `run_command` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/cli/run.py#L165"><Icon icon="github" size="14" /></a></sup>

```python
run_command(server_spec: str, transport: str | None = None, host: str | None = None, port: int | None = None, log_level: str | None = None, server_args: list[str] | None = None) -> None
```

Run a MCP server or connect to a remote one.

**Args:**

* `server_spec`: Python file, object specification (file:obj), or URL
* `transport`: Transport protocol to use
* `host`: Host to bind to when using http transport
* `port`: Port to bind to when using http transport
* `log_level`: Log level
* `server_args`: Additional arguments to pass to the server



------------------------------------------------------------

## __init__
类型: docs, 行数: 5

# __init__
Source: https://gofastmcp.com/python-sdk/fastmcp-client-__init__




------------------------------------------------------------

## `fastmcp.client`
类型: docs, 行数: 5

# `fastmcp.client`

*This module is empty or contains only private/internal implementations.*



------------------------------------------------------------

## __init__
类型: docs, 行数: 5

# __init__
Source: https://gofastmcp.com/python-sdk/fastmcp-client-auth-__init__




------------------------------------------------------------

## `fastmcp.client.auth`
类型: docs, 行数: 5

# `fastmcp.client.auth`

*This module is empty or contains only private/internal implementations.*



------------------------------------------------------------

## bearer
类型: docs, 行数: 5

# bearer
Source: https://gofastmcp.com/python-sdk/fastmcp-client-auth-bearer




------------------------------------------------------------

## `fastmcp.client.auth.bearer`
类型: docs, 行数: 2

# `fastmcp.client.auth.bearer`


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `BearerAuth` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/bearer.py#L11"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `BearerAuth` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/bearer.py#L11"><Icon icon="github" size="14" /></a></sup>

**Methods:**


------------------------------------------------------------

## `auth_flow` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/bearer.py#L15"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

#### `auth_flow` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/bearer.py#L15"><Icon icon="github" size="14" /></a></sup>

```python
auth_flow(self, request)
```



------------------------------------------------------------

## oauth
类型: docs, 行数: 5

# oauth
Source: https://gofastmcp.com/python-sdk/fastmcp-client-auth-oauth




------------------------------------------------------------

## `fastmcp.client.auth.oauth`
类型: docs, 行数: 2

# `fastmcp.client.auth.oauth`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `default_cache_dir` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L38"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `default_cache_dir` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L38"><Icon icon="github" size="14" /></a></sup>

```python
default_cache_dir() -> Path
```


------------------------------------------------------------

## `OAuth` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L295"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 23

### `OAuth` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L295"><Icon icon="github" size="14" /></a></sup>

```python
OAuth(mcp_url: str, scopes: str | list[str] | None = None, client_name: str = 'FastMCP Client', token_storage_cache_dir: Path | None = None, additional_client_metadata: dict[str, Any] | None = None) -> _MCPOAuthClientProvider
```

Create an OAuthClientProvider for an MCP server.

This is intended to be provided to the `auth` parameter of an
httpx.AsyncClient (or appropriate FastMCP client/transport instance)

**Args:**

* `mcp_url`: Full URL to the MCP endpoint (e.g. "http\://host/mcp/sse/")
* `scopes`: OAuth scopes to request. Can be a
* `client_name`: Name for this client during registration
* `token_storage_cache_dir`: Directory for FileTokenStorage
* `additional_client_metadata`: Extra fields for OAuthClientMetadata

**Returns:**

* OAuthClientProvider


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `ServerOAuthMetadata` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L43"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

### `ServerOAuthMetadata` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L43"><Icon icon="github" size="14" /></a></sup>

More flexible OAuth metadata model that accepts broader ranges of values
than the restrictive MCP standard model.

This handles real-world OAuth servers like PayPal that may support
additional methods not in the MCP specification.


------------------------------------------------------------

## `OAuthClientProvider` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L68"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `OAuthClientProvider` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L68"><Icon icon="github" size="14" /></a></sup>

OAuth client provider with more flexible OAuth metadata discovery.


------------------------------------------------------------

## `FileTokenStorage` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L116"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 9

### `FileTokenStorage` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L116"><Icon icon="github" size="14" /></a></sup>

File-based token storage implementation for OAuth credentials and tokens.
Implements the mcp.client.auth.TokenStorage protocol.

Each instance is tied to a specific server URL for proper token isolation.

**Methods:**


------------------------------------------------------------

## `get_base_url` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L131"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `get_base_url` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L131"><Icon icon="github" size="14" /></a></sup>

```python
get_base_url(url: str) -> str
```

Extract the base URL (scheme + host) from a URL.


------------------------------------------------------------

## `get_cache_key` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L136"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `get_cache_key` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L136"><Icon icon="github" size="14" /></a></sup>

```python
get_cache_key(self) -> str
```

Generate a safe filesystem key from the server's base URL.


------------------------------------------------------------

## `clear` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L208"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `clear` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L208"><Icon icon="github" size="14" /></a></sup>

```python
clear(self) -> None
```

Clear all cached data for this server.


------------------------------------------------------------

## `clear_all` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L217"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 9

#### `clear_all` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/auth/oauth.py#L217"><Icon icon="github" size="14" /></a></sup>

```python
clear_all(cls, cache_dir: Path | None = None) -> None
```

Clear all cached data for all servers.



------------------------------------------------------------

## client
类型: docs, 行数: 5

# client
Source: https://gofastmcp.com/python-sdk/fastmcp-client-client




------------------------------------------------------------

## `fastmcp.client.client`
类型: docs, 行数: 2

# `fastmcp.client.client`


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `Client` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L60"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 32

### `Client` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L60"><Icon icon="github" size="14" /></a></sup>

MCP client that delegates connection management to a Transport instance.

The Client class is responsible for MCP protocol logic, while the Transport
handles connection establishment and management. Client provides methods for
working with resources, prompts, tools and other MCP capabilities.

**Args:**

* `transport`: Connection source specification, which can be:
* ClientTransport: Direct transport instance
* FastMCP: In-process FastMCP server
* AnyUrl | str: URL to connect to
* Path: File path for local socket
* MCPConfig: MCP server configuration
* dict: Transport configuration
* `roots`: Optional RootsList or RootsHandler for filesystem access
* `sampling_handler`: Optional handler for sampling requests
* `log_handler`: Optional handler for log messages
* `message_handler`: Optional handler for protocol messages
* `progress_handler`: Optional handler for progress notifications
* `timeout`: Optional timeout for requests (seconds or timedelta)
* `init_timeout`: Optional timeout for initial connection (seconds or timedelta).
  Set to 0 to disable. If None, uses the value in the FastMCP global settings.

**Examples:**

```python # Connect to FastMCP server client =
Client("http://localhost:8080")

async with client:

------------------------------------------------------------

## List available resources resources = await client.list_resources()
类型: docs, 行数: 2

    # List available resources resources = await client.list_resources()


------------------------------------------------------------

## Call a tool result = await client.call_tool("my_tool", {"param":
类型: docs, 行数: 6

    # Call a tool result = await client.call_tool("my_tool", {"param":
    "value"})
```

**Methods:**


------------------------------------------------------------

## `session` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L207"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `session` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L207"><Icon icon="github" size="14" /></a></sup>

```python
session(self) -> ClientSession
```

Get the current active session. Raises RuntimeError if not connected.


------------------------------------------------------------

## `initialize_result` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L217"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `initialize_result` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L217"><Icon icon="github" size="14" /></a></sup>

```python
initialize_result(self) -> mcp.types.InitializeResult
```

Get the result of the initialization request.


------------------------------------------------------------

## `set_roots` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L225"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `set_roots` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L225"><Icon icon="github" size="14" /></a></sup>

```python
set_roots(self, roots: RootsList | RootsHandler) -> None
```

Set the roots for the client. This does not automatically call `send_roots_list_changed`.


------------------------------------------------------------

## `set_sampling_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L229"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `set_sampling_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L229"><Icon icon="github" size="14" /></a></sup>

```python
set_sampling_callback(self, sampling_callback: SamplingHandler) -> None
```

Set the sampling callback for the client.


------------------------------------------------------------

## `is_connected` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L235"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 9

#### `is_connected` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/client.py#L235"><Icon icon="github" size="14" /></a></sup>

```python
is_connected(self) -> bool
```

Check if the client is currently connected.



------------------------------------------------------------

## logging
类型: docs, 行数: 5

# logging
Source: https://gofastmcp.com/python-sdk/fastmcp-client-logging




------------------------------------------------------------

## `fastmcp.client.logging`
类型: docs, 行数: 2

# `fastmcp.client.logging`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `create_log_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/logging.py#L20"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

### `create_log_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/logging.py#L20"><Icon icon="github" size="14" /></a></sup>

```python
create_log_callback(handler: LogHandler | None = None) -> LoggingFnT
```



------------------------------------------------------------

## oauth_callback
类型: docs, 行数: 5

# oauth_callback
Source: https://gofastmcp.com/python-sdk/fastmcp-client-oauth_callback




------------------------------------------------------------

## `fastmcp.client.oauth_callback`
类型: docs, 行数: 7

# `fastmcp.client.oauth_callback`

OAuth callback server for handling authorization code flows.

This module provides a reusable callback server that can handle OAuth redirects
and display styled responses to users.


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `create_callback_html` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L25"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

### `create_callback_html` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L25"><Icon icon="github" size="14" /></a></sup>

```python
create_callback_html(message: str, is_success: bool = True, title: str = 'FastMCP OAuth', server_url: str | None = None) -> str
```

Create a styled HTML response for OAuth callbacks.


------------------------------------------------------------

## `create_oauth_callback_server` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L197"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 19

### `create_oauth_callback_server` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L197"><Icon icon="github" size="14" /></a></sup>

```python
create_oauth_callback_server(port: int, callback_path: str = '/callback', server_url: str | None = None, response_future: asyncio.Future | None = None) -> Server
```

Create an OAuth callback server.

**Args:**

* `port`: The port to run the server on
* `callback_path`: The path to listen for OAuth redirects on
* `server_url`: Optional server URL to display in success messages
* `response_future`: Optional future to resolve when OAuth callback is received

**Returns:**

* Configured uvicorn Server instance (not yet running)


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `CallbackResponse` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L183"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `CallbackResponse` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L183"><Icon icon="github" size="14" /></a></sup>

**Methods:**


------------------------------------------------------------

## `from_dict` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L190"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

#### `from_dict` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L190"><Icon icon="github" size="14" /></a></sup>

```python
from_dict(cls, data: dict[str, str]) -> CallbackResponse
```


------------------------------------------------------------

## `to_dict` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L193"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

#### `to_dict` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/oauth_callback.py#L193"><Icon icon="github" size="14" /></a></sup>

```python
to_dict(self) -> dict[str, str]
```



------------------------------------------------------------

## progress
类型: docs, 行数: 5

# progress
Source: https://gofastmcp.com/python-sdk/fastmcp-client-progress




------------------------------------------------------------

## `fastmcp.client.progress`
类型: docs, 行数: 5

# `fastmcp.client.progress`

*This module is empty or contains only private/internal implementations.*



------------------------------------------------------------

## roots
类型: docs, 行数: 5

# roots
Source: https://gofastmcp.com/python-sdk/fastmcp-client-roots




------------------------------------------------------------

## `fastmcp.client.roots`
类型: docs, 行数: 2

# `fastmcp.client.roots`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `convert_roots_list` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/roots.py#L19"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `convert_roots_list` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/roots.py#L19"><Icon icon="github" size="14" /></a></sup>

```python
convert_roots_list(roots: RootsList) -> list[mcp.types.Root]
```


------------------------------------------------------------

## `create_roots_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/roots.py#L33"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

### `create_roots_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/roots.py#L33"><Icon icon="github" size="14" /></a></sup>

```python
create_roots_callback(handler: RootsList | RootsHandler) -> ListRootsFnT
```



------------------------------------------------------------

## sampling
类型: docs, 行数: 5

# sampling
Source: https://gofastmcp.com/python-sdk/fastmcp-client-sampling




------------------------------------------------------------

## `fastmcp.client.sampling`
类型: docs, 行数: 2

# `fastmcp.client.sampling`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `create_sampling_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/sampling.py#L25"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

### `create_sampling_callback` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/sampling.py#L25"><Icon icon="github" size="14" /></a></sup>

```python
create_sampling_callback(sampling_handler: SamplingHandler) -> SamplingFnT
```



------------------------------------------------------------

## transports
类型: docs, 行数: 5

# transports
Source: https://gofastmcp.com/python-sdk/fastmcp-client-transports




------------------------------------------------------------

## `fastmcp.client.transports`
类型: docs, 行数: 2

# `fastmcp.client.transports`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `infer_transport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L837"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 31

### `infer_transport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L837"><Icon icon="github" size="14" /></a></sup>

```python
infer_transport(transport: ClientTransport | FastMCP | FastMCP1Server | AnyUrl | Path | MCPConfig | dict[str, Any] | str) -> ClientTransport
```

Infer the appropriate transport type from the given transport argument.

This function attempts to infer the correct transport type from the provided
argument, handling various input types and converting them to the appropriate
ClientTransport subclass.

The function supports these input types:

* ClientTransport: Used directly without modification
* FastMCP or FastMCP1Server: Creates an in-memory FastMCPTransport
* Path or str (file path): Creates PythonStdioTransport (.py) or NodeStdioTransport (.js)
* AnyUrl or str (URL): Creates StreamableHttpTransport (default) or SSETransport (for /sse endpoints)
* MCPConfig or dict: Creates MCPConfigTransport, potentially connecting to multiple servers

For HTTP URLs, they are assumed to be Streamable HTTP URLs unless they end in `/sse`.

For MCPConfig with multiple servers, a composite client is created where each server
is mounted with its name as prefix. This allows accessing tools and resources from multiple
servers through a single unified client interface, using naming patterns like
`servername_toolname` for tools and `protocol://servername/path` for resources.
If the MCPConfig contains only one server, a direct connection is established without prefixing.

**Examples:**

```python

------------------------------------------------------------

## Connect to a local Python script
类型: docs, 行数: 3

# Connect to a local Python script
transport = infer_transport("my_script.py")


------------------------------------------------------------

## Connect to a remote server via HTTP
类型: docs, 行数: 3

# Connect to a remote server via HTTP
transport = infer_transport("http://example.com/mcp")


------------------------------------------------------------

## Connect to multiple servers using MCPConfig
类型: setup, 行数: 10

# Connect to multiple servers using MCPConfig
config = {
    "mcpServers": {
        "weather": {"url": "http://weather.example.com/mcp"},
        "calendar": {"url": "http://calendar.example.com/mcp"}
    }
}
transport = infer_transport(config)
```


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `SessionKwargs` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L52"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `SessionKwargs` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L52"><Icon icon="github" size="14" /></a></sup>

Keyword arguments for the MCP ClientSession constructor.


------------------------------------------------------------

## `ClientTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L63"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

### `ClientTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L63"><Icon icon="github" size="14" /></a></sup>

Abstract base class for different MCP client transport mechanisms.

A Transport is responsible for establishing and managing connections
to an MCP server, and providing a ClientSession within an async context.


------------------------------------------------------------

## `WSTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L109"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `WSTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L109"><Icon icon="github" size="14" /></a></sup>

Transport implementation that connects to an MCP server via WebSockets.


------------------------------------------------------------

## `SSETransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L148"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `SSETransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L148"><Icon icon="github" size="14" /></a></sup>

Transport implementation that connects to an MCP server via Server-Sent Events.


------------------------------------------------------------

## `StreamableHttpTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L223"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `StreamableHttpTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L223"><Icon icon="github" size="14" /></a></sup>

Transport implementation that connects to an MCP server via Streamable HTTP Requests.


------------------------------------------------------------

## `StdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L299"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

### `StdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L299"><Icon icon="github" size="14" /></a></sup>

Base transport for connecting to an MCP server via subprocess with stdio.

This is a base class that can be subclassed for specific command-based
transports like Python, Node, Uvx, etc.


------------------------------------------------------------

## `PythonStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L416"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `PythonStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L416"><Icon icon="github" size="14" /></a></sup>

Transport for running Python scripts.


------------------------------------------------------------

## `FastMCPStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L462"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `FastMCPStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L462"><Icon icon="github" size="14" /></a></sup>

Transport for running FastMCP servers using the FastMCP CLI.


------------------------------------------------------------

## `NodeStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L489"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `NodeStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L489"><Icon icon="github" size="14" /></a></sup>

Transport for running Node.js scripts.


------------------------------------------------------------

## `UvxStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L531"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `UvxStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L531"><Icon icon="github" size="14" /></a></sup>

Transport for running commands via the uvx tool.


------------------------------------------------------------

## `NpxStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L597"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `NpxStdioTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L597"><Icon icon="github" size="14" /></a></sup>

Transport for running commands via the npx tool.


------------------------------------------------------------

## `FastMCPTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L659"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 9

### `FastMCPTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L659"><Icon icon="github" size="14" /></a></sup>

In-memory transport for FastMCP servers.

This transport connects directly to a FastMCP server instance in the same
Python process. It works with both FastMCP 2.x servers and FastMCP 1.0
servers from the low-level MCP SDK. This is particularly useful for unit
tests or scenarios where client and server run in the same runtime.


------------------------------------------------------------

## `MCPConfigTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L713"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 23

### `MCPConfigTransport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/client/transports.py#L713"><Icon icon="github" size="14" /></a></sup>

Transport for connecting to one or more MCP servers defined in an MCPConfig.

This transport provides a unified interface to multiple MCP servers defined in an MCPConfig
object or dictionary matching the MCPConfig schema. It supports two key scenarios:

1. If the MCPConfig contains exactly one server, it creates a direct transport to that server.
2. If the MCPConfig contains multiple servers, it creates a composite client by mounting
   all servers on a single FastMCP instance, with each server's name used as its mounting prefix.

In the multi-server case, tools are accessible with the prefix pattern `{server_name}_{tool_name}`
and resources with the pattern `protocol://{server_name}/path/to/resource`.

This is particularly useful for creating clients that need to interact with multiple specialized
MCP servers through a single interface, simplifying client code.

**Examples:**

```python
from fastmcp import Client
from fastmcp.utilities.mcp_config import MCPConfig


------------------------------------------------------------

## Create a config with multiple servers
类型: setup, 行数: 14

# Create a config with multiple servers
config = {
    "mcpServers": {
        "weather": {
            "url": "https://weather-api.example.com/mcp",
            "transport": "http"
        },
        "calendar": {
            "url": "https://calendar-api.example.com/mcp",
            "transport": "http"
        }
    }
}


------------------------------------------------------------

## Create a client with the config
类型: setup, 行数: 4

# Create a client with the config
client = Client(config)

async with client:

------------------------------------------------------------

## Access tools with prefixes
类型: docs, 行数: 4

    # Access tools with prefixes
    weather = await client.call_tool("weather_get_forecast", {"city": "London"})
    events = await client.call_tool("calendar_list_events", {"date": "2023-06-01"})


------------------------------------------------------------

## Access resources with prefixed URIs
类型: docs, 行数: 5

    # Access resources with prefixed URIs
    icons = await client.read_resource("weather://weather/icons/sunny")
```



------------------------------------------------------------

## exceptions
类型: docs, 行数: 5

# exceptions
Source: https://gofastmcp.com/python-sdk/fastmcp-exceptions




------------------------------------------------------------

## `fastmcp.exceptions`
类型: docs, 行数: 4

# `fastmcp.exceptions`

Custom exceptions for FastMCP.


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `FastMCPError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L6"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `FastMCPError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L6"><Icon icon="github" size="14" /></a></sup>

Base error for FastMCP.


------------------------------------------------------------

## `ValidationError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L10"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `ValidationError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L10"><Icon icon="github" size="14" /></a></sup>

Error in validating parameters or return values.


------------------------------------------------------------

## `ResourceError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L14"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `ResourceError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L14"><Icon icon="github" size="14" /></a></sup>

Error in resource operations.


------------------------------------------------------------

## `ToolError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L18"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `ToolError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L18"><Icon icon="github" size="14" /></a></sup>

Error in tool operations.


------------------------------------------------------------

## `PromptError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L22"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `PromptError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L22"><Icon icon="github" size="14" /></a></sup>

Error in prompt operations.


------------------------------------------------------------

## `InvalidSignature` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L26"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `InvalidSignature` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L26"><Icon icon="github" size="14" /></a></sup>

Invalid signature for use with FastMCP.


------------------------------------------------------------

## `ClientError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L30"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `ClientError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L30"><Icon icon="github" size="14" /></a></sup>

Error in client operations.


------------------------------------------------------------

## `NotFoundError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L34"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `NotFoundError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L34"><Icon icon="github" size="14" /></a></sup>

Object not found.


------------------------------------------------------------

## `DisabledError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L38"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 5

### `DisabledError` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/exceptions.py#L38"><Icon icon="github" size="14" /></a></sup>

Object is disabled.



------------------------------------------------------------

## __init__
类型: docs, 行数: 5

# __init__
Source: https://gofastmcp.com/python-sdk/fastmcp-prompts-__init__




------------------------------------------------------------

## `fastmcp.prompts`
类型: docs, 行数: 5

# `fastmcp.prompts`

*This module is empty or contains only private/internal implementations.*



------------------------------------------------------------

## prompt
类型: docs, 行数: 5

# prompt
Source: https://gofastmcp.com/python-sdk/fastmcp-prompts-prompt




------------------------------------------------------------

## `fastmcp.prompts.prompt`
类型: docs, 行数: 4

# `fastmcp.prompts.prompt`

Base classes for FastMCP prompts.


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `Message` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L32"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

### `Message` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L32"><Icon icon="github" size="14" /></a></sup>

```python
Message(content: str | MCPContent, role: Role | None = None, **kwargs: Any) -> PromptMessage
```

A user-friendly constructor for PromptMessage.


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `PromptArgument` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L54"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `PromptArgument` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L54"><Icon icon="github" size="14" /></a></sup>

An argument that can be passed to a prompt.


------------------------------------------------------------

## `Prompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L66"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `Prompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L66"><Icon icon="github" size="14" /></a></sup>

A prompt template that can be rendered with parameters.

**Methods:**


------------------------------------------------------------

## `to_mcp_prompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L73"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `to_mcp_prompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L73"><Icon icon="github" size="14" /></a></sup>

```python
to_mcp_prompt(self, **overrides: Any) -> MCPPrompt
```

Convert the prompt to an MCP prompt.


------------------------------------------------------------

## `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L91"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 15

#### `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L91"><Icon icon="github" size="14" /></a></sup>

```python
from_function(fn: Callable[..., PromptResult | Awaitable[PromptResult]], name: str | None = None, description: str | None = None, tags: set[str] | None = None, enabled: bool | None = None) -> FunctionPrompt
```

Create a Prompt from a function.

The function can return:

* A string (converted to a message)
* A Message object
* A dict (converted to a message)
* A sequence of any of the above


------------------------------------------------------------

## `FunctionPrompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L119"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 6

### `FunctionPrompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L119"><Icon icon="github" size="14" /></a></sup>

A prompt that is a function.

**Methods:**


------------------------------------------------------------

## `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L125"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 16

#### `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt.py#L125"><Icon icon="github" size="14" /></a></sup>

```python
from_function(cls, fn: Callable[..., PromptResult | Awaitable[PromptResult]], name: str | None = None, description: str | None = None, tags: set[str] | None = None, enabled: bool | None = None) -> FunctionPrompt
```

Create a Prompt from a function.

The function can return:

* A string (converted to a message)
* A Message object
* A dict (converted to a message)
* A sequence of any of the above



------------------------------------------------------------

## prompt_manager
类型: docs, 行数: 5

# prompt_manager
Source: https://gofastmcp.com/python-sdk/fastmcp-prompts-prompt_manager




------------------------------------------------------------

## `fastmcp.prompts.prompt_manager`
类型: docs, 行数: 2

# `fastmcp.prompts.prompt_manager`


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `PromptManager` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L21"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `PromptManager` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L21"><Icon icon="github" size="14" /></a></sup>

Manages FastMCP prompts.

**Methods:**


------------------------------------------------------------

## `mount` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L45"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `mount` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L45"><Icon icon="github" size="14" /></a></sup>

```python
mount(self, server: MountedServer) -> None
```

Adds a mounted server as a source for prompts.


------------------------------------------------------------

## `add_prompt_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L114"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `add_prompt_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L114"><Icon icon="github" size="14" /></a></sup>

```python
add_prompt_from_fn(self, fn: Callable[..., PromptResult | Awaitable[PromptResult]], name: str | None = None, description: str | None = None, tags: set[str] | None = None) -> FunctionPrompt
```

Create a prompt from a function.


------------------------------------------------------------

## `add_prompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L134"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 9

#### `add_prompt` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/prompts/prompt_manager.py#L134"><Icon icon="github" size="14" /></a></sup>

```python
add_prompt(self, prompt: Prompt) -> Prompt
```

Add a prompt to the manager.



------------------------------------------------------------

## __init__
类型: docs, 行数: 5

# __init__
Source: https://gofastmcp.com/python-sdk/fastmcp-resources-__init__




------------------------------------------------------------

## `fastmcp.resources`
类型: docs, 行数: 5

# `fastmcp.resources`

*This module is empty or contains only private/internal implementations.*



------------------------------------------------------------

## resource
类型: docs, 行数: 5

# resource
Source: https://gofastmcp.com/python-sdk/fastmcp-resources-resource




------------------------------------------------------------

## `fastmcp.resources.resource`
类型: docs, 行数: 4

# `fastmcp.resources.resource`

Base classes and interfaces for FastMCP resources.


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `Resource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L32"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `Resource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L32"><Icon icon="github" size="14" /></a></sup>

Base class for all resources.

**Methods:**


------------------------------------------------------------

## `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L48"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 6

#### `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L48"><Icon icon="github" size="14" /></a></sup>

```python
from_function(fn: Callable[[], Any], uri: str | AnyUrl, name: str | None = None, description: str | None = None, mime_type: str | None = None, tags: set[str] | None = None, enabled: bool | None = None) -> FunctionResource
```


------------------------------------------------------------

## `set_default_mime_type` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L69"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `set_default_mime_type` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L69"><Icon icon="github" size="14" /></a></sup>

```python
set_default_mime_type(cls, mime_type: str | None) -> str
```

Set default MIME type if not provided.


------------------------------------------------------------

## `set_default_name` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L76"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `set_default_name` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L76"><Icon icon="github" size="14" /></a></sup>

```python
set_default_name(self) -> Self
```

Set default name from URI if not provided.


------------------------------------------------------------

## `to_mcp_resource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L91"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `to_mcp_resource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L91"><Icon icon="github" size="14" /></a></sup>

```python
to_mcp_resource(self, **overrides: Any) -> MCPResource
```

Convert the resource to an MCPResource.


------------------------------------------------------------

## `key` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L105"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 11

#### `key` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L105"><Icon icon="github" size="14" /></a></sup>

```python
key(self) -> str
```

The key of the component. This is used for internal bookkeeping
and may reflect e.g. prefixes or other identifiers. You should not depend on
keys having a certain value, as the same tool loaded from different
hierarchies of servers may have different keys.


------------------------------------------------------------

## `FunctionResource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L115"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 16

### `FunctionResource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L115"><Icon icon="github" size="14" /></a></sup>

A resource that defers data loading by wrapping a function.

The function is only called when the resource is read, allowing for lazy loading
of potentially expensive data. This is particularly useful when listing resources,
as the function won't be called until the resource is actually accessed.

The function can return:

* str for text content (default)
* bytes for binary content
* other types will be converted to JSON

**Methods:**


------------------------------------------------------------

## `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L131"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 9

#### `from_function` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource.py#L131"><Icon icon="github" size="14" /></a></sup>

```python
from_function(cls, fn: Callable[[], Any], uri: str | AnyUrl, name: str | None = None, description: str | None = None, mime_type: str | None = None, tags: set[str] | None = None, enabled: bool | None = None) -> FunctionResource
```

Create a FunctionResource from a function.



------------------------------------------------------------

## resource_manager
类型: docs, 行数: 5

# resource_manager
Source: https://gofastmcp.com/python-sdk/fastmcp-resources-resource_manager




------------------------------------------------------------

## `fastmcp.resources.resource_manager`
类型: docs, 行数: 4

# `fastmcp.resources.resource_manager`

Resource manager functionality.


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `ResourceManager` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L28"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `ResourceManager` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L28"><Icon icon="github" size="14" /></a></sup>

Manages FastMCP resources.

**Methods:**


------------------------------------------------------------

## `mount` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L60"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `mount` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L60"><Icon icon="github" size="14" /></a></sup>

```python
mount(self, server: MountedServer) -> None
```

Adds a mounted server as a source for resources and templates.


------------------------------------------------------------

## `add_resource_or_template_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L182"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 22

#### `add_resource_or_template_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L182"><Icon icon="github" size="14" /></a></sup>

```python
add_resource_or_template_from_fn(self, fn: Callable[..., Any], uri: str, name: str | None = None, description: str | None = None, mime_type: str | None = None, tags: set[str] | None = None) -> Resource | ResourceTemplate
```

Add a resource or template to the manager from a function.

**Args:**

* `fn`: The function to register as a resource or template
* `uri`: The URI for the resource or template
* `name`: Optional name for the resource or template
* `description`: Optional description of the resource or template
* `mime_type`: Optional MIME type for the resource or template
* `tags`: Optional set of tags for categorizing the resource or template

**Returns:**

* The added resource or template. If a resource or template with the same URI already exists,
* returns the existing resource or template.


------------------------------------------------------------

## `add_resource_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L230"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 22

#### `add_resource_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L230"><Icon icon="github" size="14" /></a></sup>

```python
add_resource_from_fn(self, fn: Callable[..., Any], uri: str, name: str | None = None, description: str | None = None, mime_type: str | None = None, tags: set[str] | None = None) -> Resource
```

Add a resource to the manager from a function.

**Args:**

* `fn`: The function to register as a resource
* `uri`: The URI for the resource
* `name`: Optional name for the resource
* `description`: Optional description of the resource
* `mime_type`: Optional MIME type for the resource
* `tags`: Optional set of tags for categorizing the resource

**Returns:**

* The added resource. If a resource with the same URI already exists,
* returns the existing resource.


------------------------------------------------------------

## `add_resource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L270"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 14

#### `add_resource` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L270"><Icon icon="github" size="14" /></a></sup>

```python
add_resource(self, resource: Resource) -> Resource
```

Add a resource to the manager.

**Args:**

* `resource`: A Resource instance to add. The resource's .key attribute
  will be used as the storage key. To overwrite it, call
  Resource.with\_key() before calling this method.


------------------------------------------------------------

## `add_template_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L292"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `add_template_from_fn` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/resources/resource_manager.py#L292"><Icon icon="github" size="14" /></a></sup>

```python
add_template_from_fn(self, fn: Callable[..., Any], uri_template: str, name: str | None = None, description: str | None = None, mime_type: str | None = None, tags: set[str] | None = None) -> ResourceTemplate
```

Create a template from a function.


------------------------------------------------------------

