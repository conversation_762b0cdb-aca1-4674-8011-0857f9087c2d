#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前运行的系统
"""

import requests
import json

def test_bridge():
    """测试HTTP桥接器"""
    print("=" * 50)
    print("测试HTTP桥接器")
    print("=" * 50)
    
    # 测试根路径
    try:
        print("1. 测试根路径...")
        response = requests.get("http://127.0.0.1:8080/", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   消息: {data.get('message')}")
            print(f"   版本: {data.get('version')}")
            print("   ✓ 根路径测试成功")
        else:
            print(f"   ✗ 根路径测试失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 根路径测试异常: {e}")
    
    # 测试健康检查
    try:
        print("\n2. 测试健康检查...")
        response = requests.get("http://127.0.0.1:8080/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   状态: {data.get('status')}")
            print(f"   消息: {data.get('message')}")
            print(f"   文件存在: {data.get('file_exists')}")
            print("   ✓ 健康检查成功")
        else:
            print(f"   ✗ 健康检查失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 健康检查异常: {e}")
    
    # 测试工具列表
    try:
        print("\n3. 测试工具列表...")
        response = requests.get("http://127.0.0.1:8080/mcp/tools", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            tools = data.get('tools', [])
            print(f"   工具数量: {len(tools)}")
            for tool in tools:
                print(f"   - {tool.get('name')}: {tool.get('description')}")
            print("   ✓ 工具列表获取成功")
        else:
            print(f"   ✗ 工具列表获取失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 工具列表获取异常: {e}")

def test_web_server():
    """测试Web服务器"""
    print("\n" + "=" * 50)
    print("测试Web服务器")
    print("=" * 50)
    
    try:
        print("1. 测试Web服务器...")
        response = requests.get("http://127.0.0.1:8081/", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✓ Web服务器运行正常")
        else:
            print(f"   ✗ Web服务器响应异常: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Web服务器连接失败: {e}")

def main():
    """主函数"""
    print("测试当前运行的MySQL数据分析系统")
    
    test_bridge()
    test_web_server()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
