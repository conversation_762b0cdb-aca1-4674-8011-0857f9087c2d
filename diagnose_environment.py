#!/usr/bin/env python3
"""
环境诊断脚本
"""

import sys
import os

print("🔍 环境诊断开始")
print("=" * 50)

print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径列表:")
for path in sys.path:
    print(f"  {path}")

print("\n📦 检查关键包:")
packages = ['fastmcp', 'flask', 'sqlite3', 'json', 'datetime']
for package in packages:
    try:
        __import__(package)
        print(f"✅ {package}: 可用")
    except ImportError as e:
        print(f"❌ {package}: 不可用 - {e}")

print("\n🌐 检查网络端口:")
import socket

def check_port(host, port):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

ports_to_check = [8080, 9002]
for port in ports_to_check:
    if check_port('127.0.0.1', port):
        print(f"❌ 端口 {port}: 已被占用")
    else:
        print(f"✅ 端口 {port}: 可用")

print("\n📁 检查文件:")
files_to_check = [
    'web_client/index.html',
    'web_client/app.js',
    'web_client/styles.css',
    'standalone_server.py',
    'web_server.py'
]

for file in files_to_check:
    if os.path.exists(file):
        print(f"✅ {file}: 存在")
    else:
        print(f"❌ {file}: 不存在")

print("\n🔍 环境诊断完成")
print("=" * 50)
