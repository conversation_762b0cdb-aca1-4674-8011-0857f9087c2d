<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趋势图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <h1>趋势图API测试</h1>
    
    <div class="test-section">
        <h2>测试趋势图生成</h2>
        <button onclick="testTrendChart()">测试趋势图</button>
        <div id="trendResult" class="result">点击按钮开始测试...</div>
    </div>

    <script>
        const serverUrl = 'http://127.0.0.1:8080';
        
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = message;
        }
        
        async function testTrendChart() {
            try {
                showResult('trendResult', '正在测试趋势图生成...');
                
                const payload = {
                    chart_type: "line",
                    table: "payment",
                    x_column: "payment_date",
                    y_column: "amount",
                    title: "支付金额趋势分析",
                    time_range: "全部数据"
                };
                
                console.log('发送请求:', payload);
                
                const response = await fetch(`${serverUrl}/api/generate-chart`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });
                
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('响应数据:', data);
                
                if (data.success && data.chart_data) {
                    const chartData = data.chart_data;
                    showResult('trendResult', `
                        <strong>✅ 趋势图测试成功！</strong><br>
                        图表类型: ${chartData.chart_type}<br>
                        标题: ${chartData.title}<br>
                        数据点数量: ${chartData.data.length}<br>
                        <details>
                            <summary>查看完整响应</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `, true);
                } else {
                    showResult('trendResult', `
                        <strong>❌ 趋势图测试失败！</strong><br>
                        数据格式不正确<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, false);
                }
            } catch (error) {
                console.error('测试错误:', error);
                showResult('trendResult', `
                    <strong>❌ 趋势图测试失败！</strong><br>
                    错误信息: ${error.message}<br>
                    请检查：<br>
                    1. HTTP服务器是否在运行 (http://127.0.0.1:8080)<br>
                    2. 浏览器控制台是否有CORS错误<br>
                    3. 网络连接是否正常
                `, false);
            }
        }
        
        // 页面加载时自动测试连接
        window.onload = async function() {
            try {
                const response = await fetch(`${serverUrl}/health`);
                if (response.ok) {
                    showResult('trendResult', '✅ 服务器连接正常，点击按钮测试趋势图功能', true);
                } else {
                    showResult('trendResult', '❌ 服务器连接失败，请检查服务器是否运行', false);
                }
            } catch (error) {
                showResult('trendResult', '❌ 无法连接到服务器，请检查服务器是否运行', false);
            }
        };
    </script>
</body>
</html>
