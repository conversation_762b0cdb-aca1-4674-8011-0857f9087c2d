#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API响应格式
"""

import requests
import json

def test_api_responses():
    """测试所有API端点的响应格式"""
    base_url = "http://127.0.0.1:8082"
    
    print("=" * 60)
    print("调试API响应格式")
    print("=" * 60)
    
    # 1. 测试数据库信息API
    print("\n1. 测试数据库信息API")
    try:
        response = requests.get(f"{base_url}/api/database-info")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据:")
            print(json.dumps(data, indent=4, ensure_ascii=False))
            
            # 检查前端期望的字段
            if 'success' in data and 'database_info' in data:
                print("   ✅ 响应格式正确")
                db_info = data['database_info']
                required_fields = ['database', 'table_count', 'connection_status']
                missing_fields = [field for field in required_fields if field not in db_info]
                if missing_fields:
                    print(f"   ⚠️  缺少字段: {missing_fields}")
                else:
                    print("   ✅ 所有必需字段都存在")
            else:
                print("   ❌ 响应格式不正确")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 2. 测试统计API
    print("\n2. 测试统计API")
    try:
        payload = {
            "table": "payment",
            "column": "amount"
        }
        
        response = requests.post(f"{base_url}/api/statistics", json=payload)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据:")
            print(json.dumps(data, indent=4, ensure_ascii=False))
            
            # 检查前端期望的字段
            if 'success' in data and 'data' in data:
                print("   ✅ 响应格式正确")
                stats_data = data['data']
                required_fields = ['count', 'average']
                missing_fields = [field for field in required_fields if field not in stats_data]
                if missing_fields:
                    print(f"   ⚠️  缺少字段: {missing_fields}")
                else:
                    print("   ✅ 所有必需字段都存在")
            else:
                print("   ❌ 响应格式不正确")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 3. 测试异常检测API
    print("\n3. 测试异常检测API")
    try:
        payload = {
            "table": "payment",
            "column": "amount",
            "method": "zscore",
            "threshold": 2
        }
        
        response = requests.post(f"{base_url}/api/anomaly-detection", json=payload)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据:")
            print(json.dumps(data, indent=4, ensure_ascii=False))
            
            # 检查前端期望的字段
            if 'success' in data and 'data' in data:
                print("   ✅ 响应格式正确")
                anomaly_data = data['data']
                required_fields = ['anomaly_count', 'total_count']
                missing_fields = [field for field in required_fields if field not in anomaly_data]
                if missing_fields:
                    print(f"   ⚠️  缺少字段: {missing_fields}")
                else:
                    print("   ✅ 所有必需字段都存在")
            else:
                print("   ❌ 响应格式不正确")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 4. 测试健康检查API
    print("\n4. 测试健康检查API")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据:")
            print(json.dumps(data, indent=4, ensure_ascii=False))
            print("   ✅ 健康检查正常")
        else:
            print(f"   ❌ 健康检查失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")

def test_cors_headers():
    """测试CORS头部"""
    base_url = "http://127.0.0.1:8082"
    
    print("\n" + "=" * 60)
    print("测试CORS头部")
    print("=" * 60)
    
    # 测试OPTIONS请求
    try:
        response = requests.options(f"{base_url}/api/database-info")
        print(f"OPTIONS请求状态码: {response.status_code}")
        print("CORS头部:")
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        for header, value in cors_headers.items():
            print(f"   {header}: {value}")
            
        if cors_headers:
            print("   ✅ CORS头部存在")
        else:
            print("   ❌ 缺少CORS头部")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")

def main():
    """主函数"""
    test_api_responses()
    test_cors_headers()
    
    print("\n" + "=" * 60)
    print("API调试完成")
    print("=" * 60)
    print("\n建议:")
    print("1. 检查浏览器控制台是否有JavaScript错误")
    print("2. 检查网络标签页中的API请求和响应")
    print("3. 确认前端代码正确处理了API响应格式")

if __name__ == "__main__":
    main()
