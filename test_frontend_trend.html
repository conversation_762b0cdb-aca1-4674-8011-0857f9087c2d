<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试前端趋势分析</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 前端趋势分析功能测试</h1>
        
        <div>
            <button class="test-button" onclick="testTrendAnalysis()">测试趋势分析</button>
            <button class="test-button" onclick="testStatistics()">测试统计分析</button>
            <button class="test-button" onclick="testAnomalyDetection()">测试异常检测</button>
        </div>
        
        <div id="result" class="result loading">点击按钮开始测试...</div>
    </div>

    <script>
        // 简化的MCP工具调用函数
        async function callMCPTool(toolName, params) {
            const bridgeUrl = 'http://127.0.0.1:8083/mcp/call-tool';
            
            const requestData = {
                tool_name: toolName,
                arguments: params
            };

            const response = await fetch(bridgeUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return data.result || data;
        }

        async function testTrendAnalysis() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 正在测试趋势分析...';

            try {
                const result = await callMCPTool('analyze_data_trend', {
                    table: 'payment',
                    column: 'amount',
                    time_column: 'payment_date',
                    period: 'month'
                });

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 趋势分析测试成功！

📊 分析结果:
- 趋势方向: ${result.analysis?.trend_direction || '未知'}
- 增长率: ${result.analysis?.growth_rate || 0}%
- 平均值: ${result.analysis?.avg_value || 0}
- 最大值: ${result.analysis?.max_value || 0}
- 最小值: ${result.analysis?.min_value || 0}
- 波动性: ${result.analysis?.volatility || 0}

📈 数据点数量: ${result.data?.length || 0}

🔮 预测数据:
${result.forecast?.map(f => `- ${f.period}: ${f.predicted_value}`).join('\n') || '无预测数据'}

原始响应:
${JSON.stringify(result, null, 2)}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 趋势分析测试失败: ${error.message}`;
            }
        }

        async function testStatistics() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 正在测试统计分析...';

            try {
                const result = await callMCPTool('get_database_statistics', {
                    table: 'payment',
                    column: 'amount'
                });

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 统计分析测试成功！

📊 统计结果:
${JSON.stringify(result, null, 2)}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 统计分析测试失败: ${error.message}`;
            }
        }

        async function testAnomalyDetection() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 正在测试异常检测...';

            try {
                const result = await callMCPTool('detect_data_anomalies', {
                    table: 'payment',
                    column: 'amount',
                    method: 'zscore',
                    threshold: 2.0
                });

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 异常检测测试成功！

📊 检测结果:
${JSON.stringify(result, null, 2)}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 异常检测测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
