#!/usr/bin/env python3
"""
调试趋势图生成问题
"""

import asyncio
import json
import sys

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    sys.exit(1)

async def debug_trend_chart():
    """调试趋势图"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("🔍 调试趋势图生成")
    print("=" * 40)
    
    try:
        async with Client(server_url) as client:
            
            # 测试趋势图
            print("📈 测试趋势图生成...")
            result = await client.call_tool("generate_trend_chart", {
                "table": "payment",
                "x_column": "payment_date",
                "y_column": "amount",
                "title": "支付金额趋势分析",
                "time_range": "30 DAY"
            })
            
            print(f"成功状态: {result.data.get('success')}")
            
            if result.data.get('success'):
                chart_data = result.data.get('chart_data', {})
                print(f"图表数据键: {list(chart_data.keys())}")
                print(f"图表类型: {chart_data.get('chart_type')}")
                print(f"标题: {chart_data.get('title')}")
                print(f"是否有图片: {'image_base64' in chart_data}")
                print(f"数据点数: {len(chart_data.get('data', []))}")
                
                if 'image_base64' in chart_data:
                    print(f"图片数据长度: {len(chart_data['image_base64'])} 字符")
                else:
                    print("❌ 没有找到image_base64字段")
                    print(f"完整数据: {json.dumps(chart_data, indent=2, ensure_ascii=False)}")
                
            else:
                print(f"❌ 失败: {result.data.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(debug_trend_chart())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}")
