(index):411 页面加载时间: 2025-07-27T15:58:58.796Z
(index):412 当前URL: http://127.0.0.1:8081/
app.js?v=20250724-trend-fix:742 🔄 [DEBUG] showLoading() 被调用
app.js?v=20250724-trend-fix:744 🔄 [DEBUG] loadingOverlay元素: <div id=​"loadingOverlay" class=​"loading-overlay">​…​</div>​
app.js?v=20250724-trend-fix:746 🔄 [DEBUG] 添加show类后的classList: loading-overlay show
app.js?v=20250724-trend-fix:165 开始连接测试，服务器地址: http://127.0.0.1:8083
app.js?v=20250724-trend-fix:169 发送健康检查请求...
app.js?v=20250724-trend-fix:179 收到响应，状态码: 200
app.js?v=20250724-trend-fix:183 健康检查响应数据: Object
app.js?v=20250724-trend-fix:234 🔄 [DEBUG] 开始加载数据库信息，设置loading状态
app.js?v=20250724-trend-fix:235 🔄 [DEBUG] dbInfoElement: <div id=​"dbInfo" class=​"loading">​…​</div>​flex
app.js?v=20250724-trend-fix:237 🔄 [DEBUG] loading HTML已设置: <div class="loading">加载中...</div>
app.js?v=20250724-trend-fix:240 🔄 [DEBUG] 开始调用API...
app.js?v=20250724-trend-fix:751 🔄 [DEBUG] hideLoading() 被调用
app.js?v=20250724-trend-fix:753 🔄 [DEBUG] loadingOverlay元素: <div id=​"loadingOverlay" class=​"loading-overlay">​…​</div>​
app.js?v=20250724-trend-fix:755 🔄 [DEBUG] 移除show类后的classList: loading-overlay
app.js?v=20250724-trend-fix:699 API响应 (get_database_info): Object
app.js?v=20250724-trend-fix:242 🔄 [DEBUG] API响应收到: Object
app.js?v=20250724-trend-fix:244 🔄 [DEBUG] 检查响应格式...
app.js?v=20250724-trend-fix:245 🔄 [DEBUG] response.success: true
app.js?v=20250724-trend-fix:246 🔄 [DEBUG] response.database_info: Object
app.js?v=20250724-trend-fix:250 🔄 [DEBUG] 数据库信息解析成功: Object
app.js?v=20250724-trend-fix:261 🔄 [DEBUG] 准备更新HTML: 
                    <div class="db-info">
                        <p><strong>数据库:</strong> test</p>
                        <p><strong>表数量:</strong> 4</p>
                        <p><strong>连接状态:</strong> connected</p>
                        <p><strong>服务器版本:</strong> N/A</p>
                    </div>
                
app.js?v=20250724-trend-fix:263 🔄 [DEBUG] HTML更新完成，当前内容: 
                    <div class="db-info">
                        <p><strong>数据库:</strong> test</p>
                        <p><strong>表数量:</strong> 4</p>
                        <p><strong>连接状态:</strong> connected</p>
                        <p><strong>服务器版本:</strong> N/A</p>
                    </div>
                
app.js?v=20250724-trend-fix:264 🔄 [DEBUG] 检查是否还有loading类: null
app.js?v=20250724-trend-fix:274 🔄 [DEBUG] loadDatabaseInfo函数执行完成
app.js?v=20250724-trend-fix:280 开始加载快速统计...
app.js?v=20250724-trend-fix:699 API响应 (get_database_statistics): Object
app.js?v=20250724-trend-fix:288 统计响应: Object
app.js?v=20250724-trend-fix:699 API响应 (detect_data_anomalies): Object
app.js?v=20250724-trend-fix:307 异常检测响应: Object
Warning: Don’t paste code into the DevTools Console that you don’t understand or haven’t reviewed yourself. This could allow attackers to steal your identity or take control of your computer. Please type ‘allow pasting’ below and press Enter to allow pasting.
Warning: Don’t paste code into the DevTools Console that you don’t understand or haven’t reviewed yourself. This could allow attackers to steal your identity or take control of your computer. Please type ‘allow pasting’ below and press Enter to allow pasting.
Warning: Don’t paste code into the DevTools Console that you don’t understand or haven’t reviewed yourself. This could allow attackers to steal your identity or take control of your computer. Please type ‘allow pasting’ below and press Enter to allow pasting.
Warning: Don’t paste code into the DevTools Console that you don’t understand or haven’t reviewed yourself. This could allow attackers to steal your identity or take control of your computer. Please type ‘allow pasting’ below and press Enter to allow pasting.
allow pasting
// 1. 检查所有loading元素
console.log('=== 检查Loading元素 ===');
const allLoading = document.querySelectorAll('.loading');
console.log('找到的loading元素数量:', allLoading.length);
allLoading.forEach((el, index) => {
    console.log(`Loading元素 ${index + 1}:`, el);
    console.log('父元素:', el.parentElement);
    console.log('内容:', el.textContent);
});

// 2. 检查全局loading覆盖层
const loadingOverlay = document.getElementById('loadingOverlay');
console.log('Loading覆盖层:', loadingOverlay);
console.log('是否显示:', loadingOverlay ? loadingOverlay.classList.contains('show') : 'not found');

// 3. 检查数据库信息区域
const dbInfo = document.getElementById('dbInfo');
console.log('数据库信息区域:', dbInfo);
console.log('当前内容:', dbInfo ? dbInfo.innerHTML : 'not found');
VM106:2 === 检查Loading元素 ===
VM106:4 找到的loading元素数量: 1
VM106:6 Loading元素 1: <div id=​"dbInfo" class=​"loading">​…​</div>​flex::before​<div class=​"db-info">​<p>​<strong>​数据库:​</strong>​" test"</p>​<p>​…​</p>​<strong>​表数量:​</strong>​" 4"</p>​<p>​…​</p>​<p>​…​</p>​</div>​</div>​
VM106:7 父元素: <div class=​"card-content">​…​</div>​
VM106:8 内容: 
                    
                        数据库: test
                        表数量: 4
                        连接状态: connected
                        服务器版本: N/A
                    
                
VM106:13 Loading覆盖层: <div id=​"loadingOverlay" class=​"loading-overlay">​…​</div>​
VM106:14 是否显示: false
VM106:18 数据库信息区域: <div id=​"dbInfo" class=​"loading">​…​</div>​flex
VM106:19 当前内容: 
                    <div class="db-info">
                        <p><strong>数据库:</strong> test</p>
                        <p><strong>表数量:</strong> 4</p>
                        <p><strong>连接状态:</strong> connected</p>
                        <p><strong>服务器版本:</strong> N/A</p>
                    </div>
                
undefined
