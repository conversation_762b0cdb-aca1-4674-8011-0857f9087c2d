#!/usr/bin/env python3
"""
创建MySQL示例数据脚本
用于测试数据库分析功能
"""

import json
import random
import mysql.connector
from datetime import datetime, timedelta
from typing import Dict, Any

def load_config() -> Dict[str, Any]:
    """加载数据库配置"""
    try:
        with open('db_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("错误: 未找到 db_config.json 配置文件")
        return None
    except Exception as e:
        print(f"错误: 加载配置文件失败 - {e}")
        return None

def create_connection(config: Dict[str, Any]):
    """创建数据库连接"""
    try:
        conn = mysql.connector.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['database'],
            charset=config.get('charset', 'utf8mb4'),
            autocommit=True
        )
        return conn
    except Exception as e:
        print(f"错误: 数据库连接失败 - {e}")
        return None

def create_sample_tables(conn):
    """创建示例表"""
    cursor = conn.cursor()
    
    # 销售数据表
    sales_table = """
    CREATE TABLE IF NOT EXISTS sales_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_name VARCHAR(100) NOT NULL,
        category VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        quantity INT NOT NULL,
        sale_date DATETIME NOT NULL,
        region VARCHAR(50) NOT NULL,
        salesperson VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    """
    
    # 用户活动表
    user_activity_table = """
    CREATE TABLE IF NOT EXISTS user_activity (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        activity_type VARCHAR(50) NOT NULL,
        duration_minutes INT NOT NULL,
        score DECIMAL(5,2) NOT NULL,
        device_type VARCHAR(30) NOT NULL,
        activity_date DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    """
    
    # 系统监控表
    system_metrics_table = """
    CREATE TABLE IF NOT EXISTS system_metrics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        metric_name VARCHAR(50) NOT NULL,
        metric_value DECIMAL(10,4) NOT NULL,
        unit VARCHAR(20) NOT NULL,
        server_name VARCHAR(50) NOT NULL,
        recorded_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    """
    
    try:
        cursor.execute(sales_table)
        print("✓ 创建销售数据表")
        
        cursor.execute(user_activity_table)
        print("✓ 创建用户活动表")
        
        cursor.execute(system_metrics_table)
        print("✓ 创建系统监控表")
        
    except Exception as e:
        print(f"错误: 创建表失败 - {e}")
    finally:
        cursor.close()

def insert_sales_data(conn, num_records=1000):
    """插入销售数据"""
    cursor = conn.cursor()
    
    products = [
        ('iPhone 15', '电子产品'), ('MacBook Pro', '电子产品'), ('iPad Air', '电子产品'),
        ('Nike运动鞋', '服装'), ('Adidas T恤', '服装'), ('Levi\'s牛仔裤', '服装'),
        ('星巴克咖啡', '食品'), ('可口可乐', '食品'), ('薯片', '食品'),
        ('洗发水', '日用品'), ('牙膏', '日用品'), ('洗衣液', '日用品')
    ]
    
    regions = ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安']
    salespersons = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
    
    insert_query = """
    INSERT INTO sales_data 
    (product_name, category, amount, quantity, sale_date, region, salesperson)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    """
    
    data = []
    start_date = datetime.now() - timedelta(days=90)
    
    for i in range(num_records):
        product, category = random.choice(products)
        quantity = random.randint(1, 10)
        
        # 根据产品类型设置价格范围
        if category == '电子产品':
            base_price = random.uniform(500, 8000)
        elif category == '服装':
            base_price = random.uniform(50, 500)
        elif category == '食品':
            base_price = random.uniform(5, 50)
        else:  # 日用品
            base_price = random.uniform(10, 100)
        
        amount = round(base_price * quantity, 2)
        
        # 添加一些异常数据（5%概率）
        if random.random() < 0.05:
            amount *= random.uniform(5, 20)  # 异常高价
        
        sale_date = start_date + timedelta(
            days=random.randint(0, 90),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )
        
        region = random.choice(regions)
        salesperson = random.choice(salespersons)
        
        data.append((product, category, amount, quantity, sale_date, region, salesperson))
    
    try:
        cursor.executemany(insert_query, data)
        print(f"✓ 插入 {num_records} 条销售数据")
    except Exception as e:
        print(f"错误: 插入销售数据失败 - {e}")
    finally:
        cursor.close()

def insert_user_activity_data(conn, num_records=2000):
    """插入用户活动数据"""
    cursor = conn.cursor()
    
    activity_types = ['登录', '浏览', '购买', '评论', '分享', '收藏', '搜索']
    device_types = ['手机', '电脑', '平板', '智能电视']
    
    insert_query = """
    INSERT INTO user_activity 
    (user_id, activity_type, duration_minutes, score, device_type, activity_date)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    
    data = []
    start_date = datetime.now() - timedelta(days=30)
    
    for i in range(num_records):
        user_id = random.randint(1, 500)
        activity_type = random.choice(activity_types)
        duration_minutes = random.randint(1, 120)
        score = round(random.uniform(1.0, 10.0), 2)
        device_type = random.choice(device_types)
        
        # 添加一些异常数据
        if random.random() < 0.03:
            duration_minutes = random.randint(300, 1440)  # 异常长时间
        
        activity_date = start_date + timedelta(
            days=random.randint(0, 30),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )
        
        data.append((user_id, activity_type, duration_minutes, score, device_type, activity_date))
    
    try:
        cursor.executemany(insert_query, data)
        print(f"✓ 插入 {num_records} 条用户活动数据")
    except Exception as e:
        print(f"错误: 插入用户活动数据失败 - {e}")
    finally:
        cursor.close()

def insert_system_metrics_data(conn, num_records=5000):
    """插入系统监控数据"""
    cursor = conn.cursor()
    
    metrics = [
        ('CPU使用率', '%', (0, 100)),
        ('内存使用率', '%', (0, 100)),
        ('磁盘使用率', '%', (0, 100)),
        ('网络流量', 'MB/s', (0, 1000)),
        ('响应时间', 'ms', (10, 5000)),
        ('并发用户数', '个', (0, 10000))
    ]
    
    servers = ['web-server-01', 'web-server-02', 'db-server-01', 'cache-server-01']
    
    insert_query = """
    INSERT INTO system_metrics 
    (metric_name, metric_value, unit, server_name, recorded_at)
    VALUES (%s, %s, %s, %s, %s)
    """
    
    data = []
    start_date = datetime.now() - timedelta(days=7)
    
    for i in range(num_records):
        metric_name, unit, (min_val, max_val) = random.choice(metrics)
        server_name = random.choice(servers)
        
        # 正常值
        metric_value = round(random.uniform(min_val, max_val * 0.8), 4)
        
        # 添加一些异常数据（8%概率）
        if random.random() < 0.08:
            metric_value = round(random.uniform(max_val * 0.9, max_val), 4)
        
        recorded_at = start_date + timedelta(
            days=random.randint(0, 7),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59),
            seconds=random.randint(0, 59)
        )
        
        data.append((metric_name, metric_value, unit, server_name, recorded_at))
    
    try:
        cursor.executemany(insert_query, data)
        print(f"✓ 插入 {num_records} 条系统监控数据")
    except Exception as e:
        print(f"错误: 插入系统监控数据失败 - {e}")
    finally:
        cursor.close()

def main():
    """主函数"""
    print("=" * 50)
    print("MySQL示例数据创建工具")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config:
        return
    
    # 连接数据库
    conn = create_connection(config)
    if not conn:
        return
    
    try:
        print(f"\n连接到数据库: {config['database']}")
        
        # 创建表
        print("\n创建示例表...")
        create_sample_tables(conn)
        
        # 插入数据
        print("\n插入示例数据...")
        insert_sales_data(conn, 1000)
        insert_user_activity_data(conn, 2000)
        insert_system_metrics_data(conn, 5000)
        
        print("\n" + "=" * 50)
        print("示例数据创建完成！")
        print("=" * 50)
        print("\n创建的表:")
        print("1. sales_data - 销售数据 (1000条)")
        print("2. user_activity - 用户活动 (2000条)")
        print("3. system_metrics - 系统监控 (5000条)")
        print("\n现在可以启动MCP服务器进行测试了！")
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
