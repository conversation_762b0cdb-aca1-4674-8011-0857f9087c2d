#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动HTTP桥接器
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def start_bridge():
    """启动HTTP桥接器"""
    print("启动简化HTTP桥接器...")
    
    try:
        # 启动桥接器
        process = subprocess.Popen(
            [sys.executable, "simple_bridge_test.py"],
            cwd=Path.cwd()
        )
        
        print(f"进程ID: {process.pid}")
        
        # 等待启动
        for i in range(10):
            try:
                response = requests.get("http://127.0.0.1:8080/", timeout=2)
                if response.status_code == 200:
                    print(f"✓ HTTP桥接器启动成功 (第{i+1}秒)")
                    print(f"✓ 服务地址: http://127.0.0.1:8080")
                    return True
            except:
                pass
            time.sleep(1)
            print(f"等待启动... ({i+1}/10)")
        
        print("⚠ 启动超时")
        return False
        
    except Exception as e:
        print(f"启动失败: {e}")
        return False

def test_connection():
    """测试连接"""
    print("\n测试连接...")
    
    try:
        # 测试根路径
        response = requests.get("http://127.0.0.1:8080/", timeout=5)
        print(f"✓ 根路径: {response.status_code}")
        
        # 测试健康检查
        response = requests.get("http://127.0.0.1:8080/health", timeout=5)
        print(f"✓ 健康检查: {response.status_code}")
        
        # 测试数据库信息
        response = requests.get("http://127.0.0.1:8080/api/database-info", timeout=5)
        print(f"✓ 数据库信息: {response.status_code}")
        
        print("✓ 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"✗ 连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("快速启动HTTP桥接器")
    print("=" * 50)
    
    if start_bridge():
        if test_connection():
            print("\n" + "=" * 50)
            print("HTTP桥接器启动成功!")
            print("现在可以在Web界面中点击'连接服务器'")
            print("=" * 50)
        else:
            print("\n启动成功但连接测试失败")
    else:
        print("\n启动失败")
    
    input("\n按回车键退出...")
