#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试HTTP桥接器
"""

import asyncio
import requests
import time
import subprocess
import sys
from pathlib import Path

def test_bridge_directly():
    """直接测试HTTP桥接器"""
    print("=" * 60)
    print("直接测试HTTP桥接器")
    print("=" * 60)
    
    # 启动HTTP桥接器
    print("1. 启动HTTP桥接器...")
    try:
        process = subprocess.Popen(
            [sys.executable, "mcp_http_bridge.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # 合并stderr到stdout
            text=True,
            bufsize=1,  # 行缓冲
            universal_newlines=True,
            cwd=Path.cwd()
        )
        
        print(f"   进程ID: {process.pid}")
        print("   等待启动...")
        
        # 实时读取输出
        for i in range(10):  # 等待10秒
            time.sleep(1)
            
            # 检查进程是否还在运行
            if process.poll() is not None:
                print(f"   进程在第{i+1}秒时退出")
                # 读取所有输出
                output = process.stdout.read()
                print(f"   输出: {output}")
                return False
            
            # 尝试连接
            try:
                response = requests.get("http://127.0.0.1:8082/", timeout=2)
                if response.status_code == 200:
                    print(f"   ✓ HTTP桥接器在第{i+1}秒时启动成功")
                    break
            except:
                pass
            
            print(f"   等待中... ({i+1}/10)")
        else:
            print("   ⚠ 启动超时，但进程仍在运行")
        
        # 测试健康检查
        print("\n2. 测试健康检查...")
        try:
            print("   发送健康检查请求...")
            response = requests.get("http://127.0.0.1:8082/health", timeout=30)
            print(f"   响应状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   响应数据: {data}")
            else:
                print(f"   错误响应: {response.text}")
        except Exception as e:
            print(f"   健康检查失败: {e}")
        
        # 停止进程
        print("\n3. 停止进程...")
        process.terminate()
        try:
            process.wait(timeout=5)
            print("   进程已停止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("   进程已强制停止")
        
        return True
        
    except Exception as e:
        print(f"   启动失败: {e}")
        return False

def main():
    """主函数"""
    success = test_bridge_directly()
    if success:
        print("\n✓ 测试完成")
    else:
        print("\n✗ 测试失败")

if __name__ == "__main__":
    main()
