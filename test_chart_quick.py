#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试图表生成
"""

import requests
import json

def test_chart_generation():
    """测试图表生成功能"""
    base_url = "http://127.0.0.1:8082"
    
    print("=" * 50)
    print("快速测试图表生成")
    print("=" * 50)
    
    # 测试柱状图
    print("\n测试柱状图...")
    try:
        payload = {
            "chart_type": "bar",
            "table": "film",
            "x_column": "rating",
            "y_column": "rental_rate",
            "title": "电影评级与租赁费用分析"
        }
        
        response = requests.post(f"{base_url}/api/generate-chart", json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and data.get("chart_data", {}).get("image_base64"):
                print("✅ 图表生成成功")
                print(f"图像数据长度: {len(data['chart_data']['image_base64'])} 字符")
                print(f"图表标题: {data['chart_data'].get('title', 'N/A')}")
                print(f"数据点数量: {data['chart_data'].get('data_points', 'N/A')}")
            else:
                print("❌ 图表生成失败")
                print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def test_server_health():
    """测试服务器健康状态"""
    base_url = "http://127.0.0.1:8082"
    
    print("\n测试服务器健康状态...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务器健康")
            print(f"状态: {data.get('status')}")
            print(f"时间: {data.get('timestamp')}")
        else:
            print(f"❌ 服务器不健康: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接服务器: {e}")

def main():
    """主函数"""
    test_server_health()
    test_chart_generation()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    print("\n建议:")
    print("1. 如果服务器不健康，请重启HTTP桥接器")
    print("2. 如果图表生成失败，检查PIL是否安装")
    print("3. 刷新浏览器页面重新测试")

if __name__ == "__main__":
    main()
