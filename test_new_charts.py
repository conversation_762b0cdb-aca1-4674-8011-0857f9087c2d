#!/usr/bin/env python3
"""
测试新的散点图和热力图功能
"""

import asyncio
import json
import sys
import requests

async def test_new_charts():
    """测试新的图表类型"""
    base_url = "http://127.0.0.1:8083"
    
    print("🧪 测试新的图表类型")
    print("=" * 50)
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 服务器连接成功")
        else:
            print(f"❌ 服务器连接失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 测试散点图
    print("\n🔵 测试散点图生成...")
    try:
        scatter_data = {
            "chart_type": "scatter",
            "table": "film",
            "x_column": "rental_rate",
            "y_column": "replacement_cost",
            "title": "租赁费用与替换成本关系分析"
        }
        
        response = requests.post(f"{base_url}/api/generate-chart", json=scatter_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 散点图生成成功")
                print(f"   数据点数: {len(result.get('chart_data', {}).get('data', []))}")
            else:
                print(f"❌ 散点图生成失败: {result.get('error')}")
        else:
            print(f"❌ 散点图请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 散点图测试异常: {e}")
    
    # 测试热力图
    print("\n🔥 测试热力图生成...")
    try:
        heatmap_data = {
            "chart_type": "heatmap",
            "table": "payment",
            "x_column": "HOUR(payment_date)",
            "y_column": "DAYOFWEEK(payment_date)",
            "title": "支付时间热力图分析"
        }
        
        response = requests.post(f"{base_url}/api/generate-chart", json=heatmap_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 热力图生成成功")
                print(f"   数据点数: {len(result.get('chart_data', {}).get('data', []))}")
            else:
                print(f"❌ 热力图生成失败: {result.get('error')}")
        else:
            print(f"❌ 热力图请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 热力图测试异常: {e}")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(test_new_charts())
