#!/usr/bin/env python3
"""
测试优化后的异常检测功能
"""

import asyncio
import json
from datetime import datetime, timedelta

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    exit(1)

async def test_optimized_anomaly_detection():
    """测试优化后的异常检测功能"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("🔧 测试优化后的异常检测功能")
    print("=" * 60)
    
    try:
        async with Client(server_url) as client:
            
            # 1. 测试基础异常检测
            print("\n1. 🔍 测试基础异常检测...")
            result = await client.call_tool("detect_data_anomalies", {
                "table": "payment",
                "column": "amount",
                "method": "zscore",
                "threshold": 2.0
            })
            
            if result.data.get('success'):
                data = result.data.get('data', {})
                print(f"   ✅ 基础检测成功:")
                print(f"   📊 总记录数: {data.get('total_count', 0)}")
                print(f"   🚨 异常记录数: {data.get('anomaly_count', 0)}")
                print(f"   📈 异常率: {data.get('anomaly_rate', 0):.2f}%")
            else:
                print(f"   ❌ 基础检测失败: {result.data.get('error')}")
            
            # 2. 测试时间范围异常检测
            print("\n2. ⏰ 测试时间范围异常检测...")
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            result = await client.call_tool("detect_data_anomalies", {
                "table": "payment",
                "column": "amount",
                "method": "zscore",
                "threshold": 2.0,
                "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "time_column": "payment_date"
            })
            
            if result.data.get('success'):
                data = result.data.get('data', {})
                print(f"   ✅ 时间范围检测成功:")
                print(f"   📅 时间范围: {start_time.date()} 至 {end_time.date()}")
                print(f"   📊 总记录数: {data.get('total_count', 0)}")
                print(f"   🚨 异常记录数: {data.get('anomaly_count', 0)}")
            else:
                print(f"   ❌ 时间范围检测失败: {result.data.get('error')}")
            
            # 3. 测试IQR方法
            print("\n3. 📐 测试IQR异常检测方法...")
            result = await client.call_tool("detect_data_anomalies", {
                "table": "payment",
                "column": "amount",
                "method": "iqr",
                "threshold": 1.5
            })
            
            if result.data.get('success'):
                data = result.data.get('data', {})
                print(f"   ✅ IQR检测成功:")
                print(f"   📊 总记录数: {data.get('total_count', 0)}")
                print(f"   🚨 异常记录数: {data.get('anomaly_count', 0)}")
                print(f"   📈 异常率: {data.get('anomaly_rate', 0):.2f}%")
            else:
                print(f"   ❌ IQR检测失败: {result.data.get('error')}")
            
            # 4. 测试业务规则检测
            print("\n4. 📋 测试业务规则异常检测...")
            result = await client.call_tool("detect_data_anomalies", {
                "table": "payment",
                "column": "amount",
                "method": "business_rule",
                "business_rule": "amount > 100 OR amount < 0.01"
            })
            
            if result.data.get('success'):
                data = result.data.get('data', {})
                print(f"   ✅ 业务规则检测成功:")
                print(f"   📋 规则: amount > 100 OR amount < 0.01")
                print(f"   📊 总记录数: {data.get('total_count', 0)}")
                print(f"   🚨 异常记录数: {data.get('anomaly_count', 0)}")
            else:
                print(f"   ❌ 业务规则检测失败: {result.data.get('error')}")
            
            # 5. 测试阈值检测
            print("\n5. 🎯 测试阈值异常检测...")
            result = await client.call_tool("detect_data_anomalies", {
                "table": "payment",
                "column": "amount",
                "method": "threshold",
                "threshold": 50.0
            })
            
            if result.data.get('success'):
                data = result.data.get('data', {})
                print(f"   ✅ 阈值检测成功:")
                print(f"   🎯 阈值: 50.0")
                print(f"   📊 总记录数: {data.get('total_count', 0)}")
                print(f"   🚨 异常记录数: {data.get('anomaly_count', 0)}")
            else:
                print(f"   ❌ 阈值检测失败: {result.data.get('error')}")
            
            print("\n" + "=" * 60)
            print("🎉 异常检测功能测试完成!")
            print("\n📋 新增功能总结:")
            print("   ✅ 时间范围筛选")
            print("   ✅ 多种检测方法 (Z-Score, IQR, 业务规则, 阈值)")
            print("   ✅ 实时监控支持")
            print("   ✅ 告警系统")
            print("   ✅ 语音控制")
            print("   ✅ 性能优化")
            print("   ✅ 结果导出")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_optimized_anomaly_detection())
