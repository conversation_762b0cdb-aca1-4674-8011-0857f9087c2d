#!/usr/bin/env python3
"""
启动增强MCP系统
支持混合LLM架构：MCP客户端 + OpenAI API
"""

import subprocess
import time
import sys
import os
import signal
import threading
from pathlib import Path

class EnhancedMCPManager:
    def __init__(self):
        self.processes = []
        self.running = True
        
    def start_process(self, name, command, cwd=None):
        """启动一个进程"""
        try:
            print(f"🚀 启动 {name}...")
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=cwd or os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes.append({
                'name': name,
                'process': process,
                'command': command
            })
            
            # 启动输出监控线程
            def monitor_output():
                try:
                    for line in iter(process.stdout.readline, ''):
                        if line.strip():
                            print(f"[{name}] {line.strip()}")
                        if not self.running:
                            break
                except Exception as e:
                    print(f"[{name}] 输出监控错误: {e}")
            
            thread = threading.Thread(target=monitor_output, daemon=True)
            thread.start()
            
            return process
            
        except Exception as e:
            print(f"❌ 启动 {name} 失败: {e}")
            return None
    
    def stop_all(self):
        """停止所有进程"""
        print("\n🛑 正在停止所有服务...")
        self.running = False
        
        for proc_info in self.processes:
            try:
                process = proc_info['process']
                name = proc_info['name']
                
                if process.poll() is None:  # 进程还在运行
                    print(f"⏹️ 停止 {name}...")
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        print(f"🔪 强制终止 {name}...")
                        process.kill()
                        process.wait()
                        
            except Exception as e:
                print(f"❌ 停止进程失败: {e}")
        
        print("✅ 所有服务已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}")
        self.stop_all()
        sys.exit(0)
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查系统依赖...")
        
        # 检查Python包
        required_packages = [
            'fastmcp',
            'openai',
            'mysql.connector',
            'pandas',
            'numpy',
            'matplotlib',
            'seaborn',
            'plotly',
            'scipy'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} - 未安装")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: python install_dependencies.py")
            return False
        
        # 检查文件
        required_files = [
            'enhanced_mcp_server.py',
            'mysql_analysis_mcp.py'
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path} - 文件不存在")
                return False
        
        return True
    
    def run(self):
        """运行系统"""
        print("🎯 增强MCP MySQL数据分析系统")
        print("🏗️ 混合架构：MCP客户端 + OpenAI API")
        print("=" * 60)
        
        # 检查依赖
        if not self.check_dependencies():
            print("❌ 依赖检查失败，请先安装缺少的依赖")
            return
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 启动增强MCP服务器
            enhanced_process = self.start_process(
                "增强MCP服务器",
                "python enhanced_mcp_server.py"
            )
            
            if not enhanced_process:
                print("❌ 增强MCP服务器启动失败")
                return
            
            time.sleep(3)  # 等待服务器启动
            
            # 可选：启动Web服务器
            if os.path.exists("web_server.py"):
                web_process = self.start_process(
                    "Web服务器",
                    "python web_server.py"
                )
                time.sleep(2)
            
            print("\n" + "=" * 60)
            print("🎉 增强MCP系统启动完成!")
            print("\n📊 服务地址:")
            print("  - 增强MCP服务器: http://127.0.0.1:8085")
            if os.path.exists("web_server.py"):
                print("  - Web界面: http://127.0.0.1:8081")
            
            print("\n🏗️ 混合LLM架构:")
            print("  1️⃣ 优先使用MCP客户端LLM")
            print("  2️⃣ 自动回退到OpenAI GPT-4o-mini")
            print("  3️⃣ 智能故障恢复")
            
            print("\n🔧 AI增强功能:")
            print("  - AI异常原因分析")
            print("  - AI数据洞察分析")
            print("  - AI智能提醒优先级")
            print("  - 综合AI数据分析")
            
            print("\n💡 使用说明:")
            print("  1. 连接MCP客户端到: http://127.0.0.1:8085")
            print("  2. 或直接调用API测试: python test_hybrid_mcp.py")
            print("  3. 按 Ctrl+C 停止系统")
            
            print("\n🤖 LLM调用流程:")
            print("  MCP客户端 → 优先使用")
            print("  OpenAI API → 自动回退")
            print("  故障恢复 → 智能切换")
            
            print("=" * 60)
            
            # 保持运行
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                for proc_info in self.processes:
                    process = proc_info['process']
                    name = proc_info['name']
                    
                    if process.poll() is not None:
                        print(f"⚠️ {name} 进程已退出 (返回码: {process.returncode})")
                        if self.running:
                            print(f"🔄 重启 {name}...")
                            # 重启进程
                            new_process = self.start_process(name, proc_info['command'])
                            if new_process:
                                proc_info['process'] = new_process
                            time.sleep(2)
                
        except KeyboardInterrupt:
            print("\n⌨️ 用户中断")
        except Exception as e:
            print(f"\n❌ 系统运行错误: {e}")
        finally:
            self.stop_all()

def main():
    """主函数"""
    manager = EnhancedMCPManager()
    manager.run()

if __name__ == "__main__":
    main()
