#!/usr/bin/env python3
"""
简单的前端测试脚本
检查Web服务器和页面内容
"""

import requests
import time
import json

def test_web_server():
    """测试Web服务器"""
    print("🌐 测试Web服务器...")
    
    try:
        response = requests.get("http://127.0.0.1:8080", timeout=10)
        print(f"📊 Web服务器状态: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"📄 页面内容长度: {len(content)} 字符")
            
            # 检查关键元素
            if "工业数据分析系统" in content:
                print("✅ 页面标题正确")
            else:
                print("❌ 页面标题不正确")
            
            if "connectBtn" in content:
                print("✅ 连接按钮存在")
            else:
                print("❌ 连接按钮不存在")
            
            if "app.js" in content:
                print("✅ JavaScript文件引用正确")
            else:
                print("❌ JavaScript文件引用缺失")
            
            return True
        else:
            print(f"❌ Web服务器响应错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web服务器连接失败: {e}")
        return False

def test_mcp_server():
    """测试MCP服务器"""
    print("\n🔧 测试MCP服务器...")
    
    try:
        # 测试初始化
        init_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream"
        }
        
        response = requests.post("http://127.0.0.1:9002/mcp", 
                               json=init_payload, 
                               headers=headers, 
                               timeout=10)
        
        print(f"📊 MCP初始化状态: {response.status_code}")
        
        if response.status_code == 200:
            session_id = response.headers.get('mcp-session-id')
            print(f"🔑 会话ID: {session_id}")
            
            if session_id:
                # 发送initialized通知
                init_notification = {
                    "jsonrpc": "2.0",
                    "method": "notifications/initialized"
                }
                
                headers_with_session = headers.copy()
                headers_with_session['mcp-session-id'] = session_id
                
                notify_response = requests.post("http://127.0.0.1:9002/mcp",
                                               json=init_notification,
                                               headers=headers_with_session,
                                               timeout=10)
                
                print(f"📊 Initialized通知状态: {notify_response.status_code}")
                
                # 测试hello工具
                hello_payload = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/call",
                    "params": {
                        "name": "hello",
                        "arguments": {}
                    }
                }
                
                hello_response = requests.post("http://127.0.0.1:9002/mcp",
                                             json=hello_payload,
                                             headers=headers_with_session,
                                             timeout=10)
                
                print(f"📊 Hello工具状态: {hello_response.status_code}")
                
                if hello_response.status_code == 200:
                    # 解析事件流响应
                    text = hello_response.text
                    if "Hello from Industrial Data Analysis Server!" in text:
                        print("✅ Hello工具响应正确")
                        return True
                    else:
                        print("❌ Hello工具响应不正确")
                        print(f"响应内容: {text[:200]}...")
                        return False
                else:
                    print(f"❌ Hello工具调用失败: {hello_response.status_code}")
                    return False
            else:
                print("❌ 未获取到会话ID")
                return False
        else:
            print(f"❌ MCP初始化失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
        return False

def check_javascript_file():
    """检查JavaScript文件"""
    print("\n📄 检查JavaScript文件...")
    
    try:
        response = requests.get("http://127.0.0.1:8080/app.js", timeout=10)
        print(f"📊 JavaScript文件状态: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"📄 JavaScript文件大小: {len(content)} 字符")
            
            # 检查关键函数
            if "checkConnection" in content:
                print("✅ checkConnection函数存在")
            else:
                print("❌ checkConnection函数缺失")
            
            if "callMCPTool" in content:
                print("✅ callMCPTool函数存在")
            else:
                print("❌ callMCPTool函数缺失")
            
            if "updateConnectionLog" in content:
                print("✅ updateConnectionLog函数存在")
            else:
                print("❌ updateConnectionLog函数缺失")
            
            return True
        else:
            print(f"❌ JavaScript文件访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ JavaScript文件检查失败: {e}")
        return False

def main():
    print("🧪 简单前端测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试各个组件
    web_ok = test_web_server()
    js_ok = check_javascript_file()
    mcp_ok = test_mcp_server()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   Web服务器: {'✅ 正常' if web_ok else '❌ 异常'}")
    print(f"   JavaScript: {'✅ 正常' if js_ok else '❌ 异常'}")
    print(f"   MCP服务器: {'✅ 正常' if mcp_ok else '❌ 异常'}")
    
    if web_ok and js_ok and mcp_ok:
        print("\n🎉 所有测试通过！")
        print("💡 请在浏览器中打开 http://127.0.0.1:8080")
        print("💡 查看右上角的MCP连接日志窗口")
        print("💡 点击'连接服务器'按钮测试连接")
    else:
        print("\n❌ 部分测试失败！")
        print("💡 请检查服务器状态和配置")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
