#!/usr/bin/env python3
"""
MCP客户端测试脚本
用于测试MySQL数据分析MCP服务器的功能
"""

import asyncio
import json
import sys
from typing import Dict, Any

try:
    from fastmcp import Client
    import aiohttp
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请运行: pip install fastmcp aiohttp")
    sys.exit(1)

class MCPTester:
    """MCP测试器"""
    
    def __init__(self, server_url: str = "http://127.0.0.1:9000/mcp/"):
        self.server_url = server_url
        self.client = None
    
    async def test_connection(self) -> bool:
        """测试服务器连接"""
        try:
            print(f"🔗 正在连接MCP服务器: {self.server_url}")
            
            # 简单的HTTP连接测试
            async with aiohttp.ClientSession() as session:
                async with session.get(self.server_url.rstrip('/')) as response:
                    if response.status == 200:
                        print("✅ MCP服务器连接成功!")
                        return True
                    else:
                        print(f"❌ 服务器响应状态码: {response.status}")
                        return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def test_database_info(self):
        """测试获取数据库信息"""
        try:
            print("\n📊 测试获取数据库信息...")
            async with Client(self.server_url) as client:
                result = await client.call_tool("get_database_info", {})
                print("✅ 数据库信息获取成功:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                return result
        except Exception as e:
            print(f"❌ 获取数据库信息失败: {e}")
            return None
    
    async def test_sakila_statistics(self):
        """测试Sakila数据库的统计功能"""
        try:
            print("\n📈 测试Sakila数据库统计功能...")
            
            # 测试payment表的amount列统计
            async with Client(self.server_url) as client:
                result = await client.call_tool("get_database_statistics", {
                    "table": "payment",
                    "column": "amount",
                    "time_column": "payment_date"
                })
                print("✅ 支付金额统计成功:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                return result
        except Exception as e:
            print(f"❌ 统计测试失败: {e}")
            return None
    
    async def test_anomaly_detection(self):
        """测试异常检测功能"""
        try:
            print("\n🔍 测试异常检测功能...")
            
            async with Client(self.server_url) as client:
                result = await client.call_tool("detect_data_anomalies", {
                    "table": "payment",
                    "column": "amount",
                    "method": "zscore",
                    "threshold": 2.0
                })
                print("✅ 异常检测成功:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                return result
        except Exception as e:
            print(f"❌ 异常检测失败: {e}")
            return None
    
    async def test_chart_generation(self):
        """测试图表生成功能"""
        try:
            print("\n📊 测试图表生成功能...")
            
            async with Client(self.server_url) as client:
                # 生成柱状图
                result = await client.call_tool("generate_bar_chart", {
                    "table": "film",
                    "x_column": "rating",
                    "y_column": "rental_rate",
                    "title": "电影评级与租赁费用",
                    "limit": 10
                })
                print("✅ 柱状图生成成功:")
                print(f"图表类型: {result.get('chart_type', 'unknown')}")
                print(f"成功状态: {result.get('success', False)}")
                return result
        except Exception as e:
            print(f"❌ 图表生成失败: {e}")
            return None
    
    async def test_custom_sql(self):
        """测试自定义SQL查询"""
        try:
            print("\n💻 测试自定义SQL查询...")
            
            async with Client(self.server_url) as client:
                result = await client.call_tool("execute_custom_sql", {
                    "sql": "SELECT rating, COUNT(*) as count FROM film GROUP BY rating LIMIT 5"
                })
                print("✅ SQL查询成功:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                return result
        except Exception as e:
            print(f"❌ SQL查询失败: {e}")
            return None
    
    async def test_voice_command(self):
        """测试语音功能"""
        try:
            print("\n🔊 测试语音功能...")
            
            async with Client(self.server_url) as client:
                result = await client.call_tool("voice_command", {
                    "command": "MySQL数据分析系统测试成功",
                    "listen_for_input": False
                })
                print("✅ 语音测试成功:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                return result
        except Exception as e:
            print(f"❌ 语音测试失败: {e}")
            return None
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始MCP服务器功能测试")
        print("=" * 60)
        
        # 测试连接
        if not await self.test_connection():
            print("❌ 服务器连接失败，请检查服务器是否启动")
            return
        
        # 运行各项功能测试
        tests = [
            self.test_database_info,
            self.test_sakila_statistics,
            self.test_anomaly_detection,
            self.test_chart_generation,
            self.test_custom_sql,
            self.test_voice_command
        ]
        
        success_count = 0
        for test in tests:
            try:
                result = await test()
                if result and result.get('success', False):
                    success_count += 1
                await asyncio.sleep(1)  # 避免请求过快
            except Exception as e:
                print(f"❌ 测试执行异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"🎯 测试完成! 成功: {success_count}/{len(tests)}")
        
        if success_count == len(tests):
            print("🎉 所有功能测试通过！您的MCP服务器运行正常。")
        elif success_count > 0:
            print("⚠️  部分功能正常，建议检查失败的功能。")
        else:
            print("❌ 所有测试失败，请检查服务器配置。")

async def main():
    """主函数"""
    tester = MCPTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}")
