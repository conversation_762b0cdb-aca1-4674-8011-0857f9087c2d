#!/usr/bin/env python3
"""
快速启动脚本
帮助用户解决数据库连接问题并启动系统
"""

import os
import sys
import json
import subprocess
import mysql.connector
from typing import Dict, Any

def check_mysql_service():
    """检查MySQL服务状态"""
    print("🔍 检查MySQL服务状态...")
    
    try:
        # 尝试连接MySQL
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            connect_timeout=3
        )
        connection.close()
        print("✅ MySQL服务运行正常")
        return True
    except mysql.connector.Error as e:
        if e.errno == 1045:  # Access denied
            print("⚠️ MySQL服务运行，但认证失败")
            return "auth_error"
        elif e.errno == 2003:  # Can't connect
            print("❌ MySQL服务未运行")
            return False
        else:
            print(f"❌ MySQL连接错误: {e}")
            return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def start_mysql_service():
    """尝试启动MySQL服务"""
    print("🚀 尝试启动MySQL服务...")
    
    import platform
    system = platform.system().lower()
    
    try:
        if system == "windows":
            # Windows系统
            result = subprocess.run(
                ["net", "start", "mysql"],
                capture_output=True,
                text=True,
                shell=True
            )
            if result.returncode == 0:
                print("✅ MySQL服务启动成功")
                return True
            else:
                print("❌ MySQL服务启动失败")
                print("💡 请以管理员身份运行命令提示符，然后执行: net start mysql")
                return False
                
        elif system == "linux":
            # Linux系统
            commands = [
                ["sudo", "systemctl", "start", "mysql"],
                ["sudo", "service", "mysql", "start"]
            ]
            
            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        print("✅ MySQL服务启动成功")
                        return True
                except:
                    continue
            
            print("❌ MySQL服务启动失败")
            print("💡 请手动执行: sudo systemctl start mysql")
            return False
            
        elif system == "darwin":
            # macOS系统
            commands = [
                ["brew", "services", "start", "mysql"],
                ["sudo", "launchctl", "load", "/Library/LaunchDaemons/com.oracle.oss.mysql.mysqld.plist"]
            ]
            
            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        print("✅ MySQL服务启动成功")
                        return True
                except:
                    continue
            
            print("❌ MySQL服务启动失败")
            print("💡 请手动执行: brew services start mysql")
            return False
        
    except Exception as e:
        print(f"❌ 启动MySQL服务时出错: {e}")
        return False

def configure_database():
    """配置数据库连接"""
    print("\n🔧 配置数据库连接")
    print("=" * 40)
    
    # 加载当前配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except:
        print("❌ 无法读取配置文件")
        return False
    
    db_config = config.get('database', {})
    
    print("当前数据库配置:")
    print(f"  主机: {db_config.get('host', 'localhost')}")
    print(f"  端口: {db_config.get('port', 3306)}")
    print(f"  用户: {db_config.get('user', 'root')}")
    print(f"  数据库: {db_config.get('database', 'realtime_data')}")
    
    print("\n是否要修改配置？(y/n): ", end="")
    if input().lower() != 'y':
        return True
    
    # 获取新配置
    host = input(f"主机 [{db_config.get('host', 'localhost')}]: ").strip()
    if not host:
        host = db_config.get('host', 'localhost')
    
    port = input(f"端口 [{db_config.get('port', 3306)}]: ").strip()
    if not port:
        port = db_config.get('port', 3306)
    else:
        port = int(port)
    
    user = input(f"用户名 [{db_config.get('user', 'root')}]: ").strip()
    if not user:
        user = db_config.get('user', 'root')
    
    password = input("密码 (留空表示无密码): ").strip()
    
    database = input(f"数据库名 [{db_config.get('database', 'realtime_data')}]: ").strip()
    if not database:
        database = db_config.get('database', 'realtime_data')
    
    # 测试新配置
    print("\n🧪 测试数据库连接...")
    try:
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            connect_timeout=5
        )
        connection.close()
        print("✅ 数据库连接测试成功")
        
        # 更新配置文件
        config['database'].update({
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database
        })
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 配置文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def show_demo_mode_info():
    """显示演示模式信息"""
    print("\n" + "="*50)
    print("🎭 演示模式")
    print("="*50)
    print("如果暂时无法配置数据库，可以使用演示模式：")
    print("\n🚀 启动演示模式:")
    print("  python local_mcp_server.py --demo-mode")
    print("\n📋 演示模式功能:")
    print("  ✅ LLM功能测试")
    print("  ✅ 语音交互")
    print("  ✅ 系统状态检查")
    print("  ❌ 数据库相关功能")
    print("\n💡 配置好数据库后，可以正常启动:")
    print("  python local_mcp_server.py")
    print("="*50)

def main():
    """主函数"""
    print("🎯 本地MCP系统快速启动助手")
    print("=" * 50)
    
    # 检查配置文件
    if not os.path.exists('config.json'):
        print("❌ 配置文件不存在，请先运行: python install_local_system.py")
        return
    
    # 检查MySQL服务
    mysql_status = check_mysql_service()
    
    if mysql_status is False:
        print("\n💡 MySQL服务未运行，尝试启动...")
        if start_mysql_service():
            mysql_status = check_mysql_service()
    
    if mysql_status is True:
        print("✅ MySQL服务正常")
        
        # 配置数据库
        if configure_database():
            print("\n🚀 启动MCP服务器...")
            try:
                subprocess.run([sys.executable, "local_mcp_server.py"], check=True)
            except KeyboardInterrupt:
                print("\n👋 服务器已停止")
            except Exception as e:
                print(f"\n❌ 启动失败: {e}")
        else:
            show_demo_mode_info()
    
    elif mysql_status == "auth_error":
        print("\n⚠️ MySQL认证失败")
        print("💡 请检查用户名和密码")
        if configure_database():
            print("\n🚀 启动MCP服务器...")
            try:
                subprocess.run([sys.executable, "local_mcp_server.py"], check=True)
            except KeyboardInterrupt:
                print("\n👋 服务器已停止")
        else:
            show_demo_mode_info()
    
    else:
        print("\n❌ 无法连接MySQL数据库")
        print("\n💡 解决方案:")
        print("1. 安装MySQL数据库")
        print("2. 启动MySQL服务")
        print("3. 或使用演示模式")
        
        show_demo_mode_info()
        
        print("\n是否要启动演示模式？(y/n): ", end="")
        if input().lower() == 'y':
            print("\n🚀 启动演示模式...")
            try:
                subprocess.run([sys.executable, "local_mcp_server.py", "--demo-mode"], check=True)
            except KeyboardInterrupt:
                print("\n👋 服务器已停止")
            except Exception as e:
                print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
