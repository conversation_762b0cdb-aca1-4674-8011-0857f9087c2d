#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MCP服务器测试版本
用于调试启动问题
"""

import asyncio
import json
import logging
from fastmcp import FastMCP, Context

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 创建简单的MCP服务器
mcp = FastMCP(name="简单测试服务器")

@mcp.tool
async def test_tool(message: str = "Hello", ctx: Context = None) -> dict:
    """简单的测试工具"""
    logger.info(f"测试工具被调用: {message}")
    if ctx:
        await ctx.info(f"处理消息: {message}")
    
    return {
        "success": True,
        "message": f"收到消息: {message}",
        "timestamp": "2024-01-01 12:00:00"
    }

@mcp.tool
async def get_database_info(ctx: Context = None) -> dict:
    """模拟数据库信息获取"""
    logger.info("获取数据库信息")
    if ctx:
        await ctx.info("正在获取数据库信息...")
    
    return {
        "success": True,
        "database_info": {
            "database": "test",
            "host": "localhost",
            "port": 3306,
            "tables": ["users", "orders", "products"],
            "table_count": 3,
            "connection_status": "connected"
        }
    }

if __name__ == "__main__":
    try:
        print("=" * 50)
        print("简化MCP服务器测试")
        print("=" * 50)
        print("启动服务器...")
        print("监听地址: http://127.0.0.1:9000/mcp/")
        print("按 Ctrl+C 停止")
        print("=" * 50)
        
        # 运行MCP服务器
        mcp.run(
            transport="http",
            host="127.0.0.1",
            port=9000,
            log_level="debug"
        )
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        logger.error(f"启动失败: {e}")
