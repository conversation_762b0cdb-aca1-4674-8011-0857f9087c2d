#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版MySQL数据分析MCP服务器
专注于核心功能，确保稳定运行
"""

import json
import mysql.connector
from fastmcp import FastMCP

# 创建MCP服务器
mcp = FastMCP("MySQL数据分析服务器")

# 数据库配置
DB_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "12369874b",
    "database": "sakila",
    "charset": "utf8mb4",
    "use_unicode": True,
    "autocommit": True
}

def get_db_connection():
    """获取数据库连接"""
    return mysql.connector.connect(**DB_CONFIG)

@mcp.tool()
def get_database_info() -> str:
    """获取数据库基本信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取数据库版本
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        
        # 获取表数量
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        table_count = len(tables)
        
        cursor.close()
        conn.close()
        
        return json.dumps({
            "success": True,
            "database_info": {
                "database": DB_CONFIG["database"],
                "version": version,
                "table_count": table_count,
                "host": DB_CONFIG["host"]
            }
        }, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, ensure_ascii=False)

@mcp.tool()
def get_database_statistics(table: str, column: str) -> str:
    """获取数据库统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建查询
        query = f"""
        SELECT 
            COUNT(*) as count,
            AVG({column}) as average,
            MIN({column}) as minimum,
            MAX({column}) as maximum,
            STDDEV({column}) as std_dev
        FROM {table}
        WHERE {column} IS NOT NULL
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return json.dumps({
            "success": True,
            "data": {
                "count": result[0],
                "average": float(result[1]) if result[1] else 0,
                "minimum": float(result[2]) if result[2] else 0,
                "maximum": float(result[3]) if result[3] else 0,
                "std_dev": float(result[4]) if result[4] else 0
            }
        }, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, ensure_ascii=False)

@mcp.tool()
def detect_data_anomalies(table: str, column: str, method: str = "zscore", threshold: float = 2.0) -> str:
    """检测数据异常"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        if method == "zscore":
            # Z-Score方法
            query = f"""
            SELECT {column},
                   ABS(({column} - (SELECT AVG({column}) FROM {table})) / 
                       (SELECT STDDEV({column}) FROM {table})) as zscore
            FROM {table}
            WHERE {column} IS NOT NULL
            HAVING zscore > {threshold}
            ORDER BY zscore DESC
            LIMIT 10
            """
        else:
            # IQR方法
            query = f"""
            WITH quartiles AS (
                SELECT 
                    PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY {column}) as q1,
                    PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY {column}) as q3
                FROM {table}
                WHERE {column} IS NOT NULL
            )
            SELECT {column}
            FROM {table}, quartiles
            WHERE {column} IS NOT NULL
            AND ({column} < q1 - 1.5 * (q3 - q1) OR {column} > q3 + 1.5 * (q3 - q1))
            LIMIT 10
            """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        anomalies = []
        for row in results:
            if method == "zscore":
                anomalies.append({
                    "value": float(row[0]),
                    "score": float(row[1]),
                    "reason": f"Z-Score: {row[1]:.2f} > {threshold}"
                })
            else:
                anomalies.append({
                    "value": float(row[0]),
                    "reason": "超出IQR范围"
                })
        
        return json.dumps({
            "success": True,
            "data": {
                "anomalies": anomalies,
                "count": len(anomalies),
                "method": method,
                "threshold": threshold
            }
        }, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, ensure_ascii=False)

@mcp.tool()
def execute_custom_sql(sql: str) -> str:
    """执行自定义SQL查询"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute(sql)
        
        if cursor.description:
            # SELECT查询
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            
            return json.dumps({
                "success": True,
                "data": {
                    "columns": columns,
                    "rows": results,
                    "row_count": len(results)
                }
            }, ensure_ascii=False, default=str)
        else:
            # INSERT/UPDATE/DELETE查询
            return json.dumps({
                "success": True,
                "data": {
                    "affected_rows": cursor.rowcount,
                    "message": "查询执行成功"
                }
            }, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, ensure_ascii=False)
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@mcp.tool()
def generate_bar_chart(table: str, x_column: str, y_column: str, title: str = "柱状图", limit: int = 10) -> str:
    """生成柱状图"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import base64
        import io
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = f"SELECT {x_column}, AVG({y_column}) as avg_value FROM {table} GROUP BY {x_column} ORDER BY avg_value DESC LIMIT {limit}"
        cursor.execute(query)
        results = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        if not results:
            raise Exception("没有数据可用于生成图表")
        
        # 准备数据
        x_data = [str(row[0]) for row in results]
        y_data = [float(row[1]) for row in results]
        
        # 创建图表
        plt.figure(figsize=(10, 6))
        bars = plt.bar(x_data, y_data, color='skyblue', edgecolor='navy', alpha=0.7)
        
        # 添加数值标签
        for bar, value in zip(bars, y_data):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(y_data)*0.01,
                    f'{value:.2f}', ha='center', va='bottom')
        
        plt.title(title, fontsize=16, fontweight='bold')
        plt.xlabel(x_column, fontsize=12)
        plt.ylabel(f'平均 {y_column}', fontsize=12)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存为base64
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close()
        
        return json.dumps({
            "success": True,
            "chart_data": {
                "image_base64": image_base64,
                "title": title,
                "data_points": len(results)
            }
        }, ensure_ascii=False)
        
    except Exception as e:
        return json.dumps({
            "success": False,
            "error": str(e)
        }, ensure_ascii=False)

if __name__ == "__main__":
    print("启动MySQL数据分析MCP服务器...")
    print("服务器将运行在: http://127.0.0.1:9000/mcp/")
    mcp.run(transport="http", host="127.0.0.1", port=9000)
