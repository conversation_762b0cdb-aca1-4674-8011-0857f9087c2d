#!/usr/bin/env python3
"""
测试完整的MCP连接流程
模拟前端的连接过程
"""

import requests
import json
import time

def simulate_frontend_connection():
    """模拟前端连接流程"""
    print("🔄 模拟前端MCP连接流程")
    print("=" * 50)
    
    session_id = None
    
    try:
        # 步骤1：初始化MCP连接
        print("📤 步骤1：发送MCP初始化请求...")
        init_payload = {
            "jsonrpc": "2.0",
            "id": int(time.time() * 1000),  # 模拟Date.now()
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "industrial-analysis-client",
                    "version": "1.0.0"
                }
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream"
        }
        
        print(f"📤 请求体: {json.dumps(init_payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post("http://127.0.0.1:9002/mcp", 
                               json=init_payload, 
                               headers=headers, 
                               timeout=10)
        
        print(f"📥 初始化响应状态: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            session_id = response.headers.get('mcp-session-id')
            print(f"🔑 获取到会话ID: {session_id}")
            
            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'text/event-stream' in content_type:
                print("📥 响应类型: 事件流")
                response_text = response.text
                print(f"📥 事件流内容: {response_text[:200]}...")
            else:
                print("📥 响应类型: JSON")
                try:
                    data = response.json()
                    print(f"📥 JSON数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                except:
                    print("📥 无法解析JSON响应")
        else:
            print(f"❌ 初始化失败: {response.status_code}")
            print(f"❌ 错误内容: {response.text}")
            return False
        
        # 步骤2：发送initialized通知
        print("\n📤 步骤2：发送initialized通知...")
        notification_payload = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        headers_with_session = headers.copy()
        if session_id:
            headers_with_session['mcp-session-id'] = session_id
        
        print(f"📤 通知请求头: {json.dumps(headers_with_session, indent=2, ensure_ascii=False)}")
        
        notify_response = requests.post("http://127.0.0.1:9002/mcp",
                                       json=notification_payload,
                                       headers=headers_with_session,
                                       timeout=10)
        
        print(f"📥 Initialized通知响应状态: {notify_response.status_code}")
        
        if notify_response.status_code in [200, 202]:
            print("✅ Initialized通知发送成功")
        else:
            print(f"⚠️ Initialized通知响应异常: {notify_response.status_code}")
            print(f"⚠️ 响应内容: {notify_response.text}")
        
        # 步骤3：调用hello工具
        print("\n📤 步骤3：调用hello工具...")
        hello_payload = {
            "jsonrpc": "2.0",
            "id": int(time.time() * 1000),
            "method": "tools/call",
            "params": {
                "name": "hello",
                "arguments": {}
            }
        }
        
        print(f"📤 Hello工具请求: {json.dumps(hello_payload, indent=2, ensure_ascii=False)}")
        
        hello_response = requests.post("http://127.0.0.1:9002/mcp",
                                     json=hello_payload,
                                     headers=headers_with_session,
                                     timeout=10)
        
        print(f"📥 Hello工具响应状态: {hello_response.status_code}")
        
        if hello_response.status_code == 200:
            content_type = hello_response.headers.get('content-type', '')
            if 'text/event-stream' in content_type:
                print("📥 Hello响应类型: 事件流")
                response_text = hello_response.text
                print(f"📥 事件流内容: {response_text}")
                
                # 解析事件流
                lines = response_text.split('\n')
                for line in lines:
                    if line.startswith('data: '):
                        try:
                            json_data = json.loads(line[6:])
                            print(f"📥 解析的JSON: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
                            
                            if json_data.get('result'):
                                result = json_data['result']
                                if result.get('content'):
                                    for content in result['content']:
                                        if content.get('type') == 'text':
                                            print(f"✅ Hello工具响应: {content.get('text')}")
                        except Exception as e:
                            print(f"❌ 解析事件流数据失败: {e}")
            else:
                print("📥 Hello响应类型: JSON")
                try:
                    data = hello_response.json()
                    print(f"📥 JSON数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                except:
                    print("📥 无法解析JSON响应")
        else:
            print(f"❌ Hello工具调用失败: {hello_response.status_code}")
            print(f"❌ 错误内容: {hello_response.text}")
            return False
        
        print("\n✅ 完整连接流程测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 连接流程测试失败: {e}")
        return False

def main():
    print("🧪 MCP连接流程测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 模拟前端连接
    success = simulate_frontend_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 连接流程测试完成！")
        print("💡 这证明前端应该能够正常连接到MCP服务器")
        print("💡 如果Web界面仍有问题，请检查浏览器控制台日志")
    else:
        print("❌ 连接流程测试失败！")
        print("💡 请检查MCP服务器配置")
    print("=" * 60)

if __name__ == "__main__":
    main()
