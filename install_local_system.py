#!/usr/bin/env python3
"""
安装完整的本地MCP系统
支持OpenAI测试和将来的本地LLM切换
"""

import subprocess
import sys
import os
import json
import platform

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("需要Python 3.8或更高版本")
        return False

def install_python_packages():
    """安装Python包"""
    packages = [
        # 核心依赖
        "fastmcp>=2.0.0",
        "mysql-connector-python>=8.0.0",
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        
        # 数据分析和可视化
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "scipy>=1.7.0",
        "plotly>=5.0.0",
        "scikit-learn>=1.0.0",
        
        # LLM支持
        "openai>=1.0.0",
        "aiohttp>=3.8.0",
        
        # 语音功能
        "pyttsx3>=2.90",
        "speechrecognition>=3.8.0",
        "pyaudio>=0.2.11",
        
        # 其他工具
        "requests>=2.25.0",
        "python-dateutil>=2.8.0"
    ]
    
    print("📦 安装Python依赖包...")
    
    success_count = 0
    failed_packages = []
    
    for package in packages:
        print(f"  📥 安装 {package}...")
        if run_command(f"pip install {package}", f"安装 {package}"):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print(f"\n📊 安装结果: {success_count}/{len(packages)} 个包安装成功")
    
    if failed_packages:
        print("⚠️ 以下包安装失败:")
        for pkg in failed_packages:
            print(f"  ❌ {pkg}")
        print("\n💡 可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 升级pip: python -m pip install --upgrade pip")
        print("3. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple")
        
        # 尝试使用国内镜像重新安装失败的包
        print("\n🔄 尝试使用国内镜像重新安装...")
        for pkg in failed_packages:
            run_command(f"pip install -i https://pypi.tuna.tsinghua.edu.cn/simple {pkg}", f"重新安装 {pkg}")
    
    return len(failed_packages) == 0

def install_system_dependencies():
    """安装系统依赖"""
    system = platform.system().lower()
    
    if system == "windows":
        print("🪟 Windows系统检测到")
        print("💡 Windows用户请确保已安装:")
        print("  - Microsoft Visual C++ Redistributable")
        print("  - 音频驱动程序（用于语音功能）")
        return True
        
    elif system == "linux":
        print("🐧 Linux系统检测到")
        print("🔧 安装系统依赖...")
        
        # 检测包管理器
        if os.path.exists("/usr/bin/apt"):
            commands = [
                "sudo apt update",
                "sudo apt install -y python3-dev",
                "sudo apt install -y portaudio19-dev",
                "sudo apt install -y espeak espeak-data",
                "sudo apt install -y alsa-utils"
            ]
        elif os.path.exists("/usr/bin/yum"):
            commands = [
                "sudo yum install -y python3-devel",
                "sudo yum install -y portaudio-devel",
                "sudo yum install -y espeak espeak-data",
                "sudo yum install -y alsa-utils"
            ]
        else:
            print("⚠️ 未识别的Linux发行版，请手动安装音频相关依赖")
            return True
        
        for cmd in commands:
            run_command(cmd, f"执行 {cmd}")
        
        return True
        
    elif system == "darwin":
        print("🍎 macOS系统检测到")
        print("🔧 安装系统依赖...")
        
        # 检查是否安装了Homebrew
        if run_command("which brew", "检查Homebrew"):
            commands = [
                "brew install portaudio",
                "brew install espeak"
            ]
            for cmd in commands:
                run_command(cmd, f"执行 {cmd}")
        else:
            print("⚠️ 未安装Homebrew，请手动安装音频相关依赖")
        
        return True
    
    else:
        print(f"⚠️ 未知系统: {system}")
        return True

def create_config_file():
    """创建配置文件"""
    print("📝 创建配置文件...")
    
    if os.path.exists("config.json"):
        print("✅ 配置文件已存在")
        return True
    
    # 默认配置已经在config.json中创建
    print("✅ 使用默认配置文件")
    return True

def create_database_config():
    """创建数据库配置示例"""
    print("📝 创建数据库配置示例...")
    
    db_config = {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "",
        "database": "realtime_data",
        "charset": "utf8mb4"
    }
    
    try:
        with open("database_example.json", "w", encoding="utf-8") as f:
            json.dump(db_config, f, indent=2, ensure_ascii=False)
        print("✅ 数据库配置示例创建完成: database_example.json")
        return True
    except Exception as e:
        print(f"❌ 创建数据库配置失败: {e}")
        return False

def create_startup_scripts():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # Windows启动脚本
    windows_script = """@echo off
echo 启动本地MCP MySQL数据分析系统
echo =====================================
python local_mcp_server.py
pause
"""
    
    # Linux/macOS启动脚本
    unix_script = """#!/bin/bash
echo "启动本地MCP MySQL数据分析系统"
echo "====================================="
python3 local_mcp_server.py
"""
    
    try:
        # Windows脚本
        with open("start_server.bat", "w", encoding="utf-8") as f:
            f.write(windows_script)
        
        # Unix脚本
        with open("start_server.sh", "w", encoding="utf-8") as f:
            f.write(unix_script)
        
        # 给Unix脚本执行权限
        if platform.system().lower() != "windows":
            os.chmod("start_server.sh", 0o755)
        
        print("✅ 启动脚本创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False

def verify_installation():
    """验证安装"""
    print("🧪 验证安装...")
    
    test_imports = [
        ("fastmcp", "FastMCP"),
        ("mysql.connector", "MySQL连接器"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
        ("matplotlib", "Matplotlib"),
        ("seaborn", "Seaborn"),
        ("plotly", "Plotly"),
        ("scipy", "SciPy"),
        ("openai", "OpenAI"),
        ("pyttsx3", "TTS引擎"),
        ("speech_recognition", "语音识别")
    ]
    
    success_count = 0
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name}: {e}")
    
    print(f"\n📊 验证结果: {success_count}/{len(test_imports)} 个模块可用")
    
    # 检查配置文件
    config_files = ["config.json", "llm_providers.py", "local_mcp_server.py", "local_mcp_client.py"]
    for file in config_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 文件缺失")
    
    return success_count >= len(test_imports) * 0.8  # 80%成功率

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 60)
    print("🎉 本地MCP系统安装完成!")
    print("=" * 60)
    
    print("\n📋 下一步操作:")
    print("1. 配置MySQL数据库连接")
    print("   - 编辑 config.json 中的 database 部分")
    print("   - 或参考 database_example.json")
    
    print("\n2. 启动MCP服务器")
    print("   - Windows: 双击 start_server.bat")
    print("   - Linux/macOS: ./start_server.sh")
    print("   - 或直接运行: python local_mcp_server.py")
    
    print("\n3. 启动MCP客户端")
    print("   - 运行: python local_mcp_client.py")
    print("   - 支持语音交互和命令行操作")
    
    print("\n4. 切换到本地LLM（将来）")
    print("   - 安装 Ollama 或 LM Studio")
    print("   - 修改 config.json 中的 llm.provider 为 'local'")
    print("   - 配置本地LLM的API地址")
    
    print("\n🤖 当前LLM配置:")
    print("   - 提供者: OpenAI GPT-4o-mini (测试用)")
    print("   - 将来可切换到本地LLM")
    print("   - 完全离线运行")
    
    print("\n🎤 语音功能:")
    print("   - TTS: 本地语音合成")
    print("   - ASR: 本地语音识别")
    print("   - 支持中文语音交互")
    
    print("\n📊 支持的功能:")
    print("   - 统计分析（求和、平均值等）")
    print("   - 异常检测（AI原因分析）")
    print("   - 智能提醒（AI优先级评估）")
    print("   - 图表生成（柱状图、饼图等）")
    print("   - 趋势分析（AI洞察）")
    print("   - 语音交互")
    
    print("\n💡 技术特点:")
    print("   - 完全本地化部署")
    print("   - 支持大数据量实时处理")
    print("   - 渐进式LLM切换（OpenAI → 本地）")
    print("   - 标准MCP协议")
    print("   - 语音友好界面")
    
    print("=" * 60)

def main():
    """主函数"""
    print("🚀 本地MCP MySQL数据分析系统安装程序")
    print("🎯 目标：完全本地化的智能数据分析系统")
    print("=" * 60)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装系统依赖", install_system_dependencies),
        ("安装Python包", install_python_packages),
        ("创建配置文件", create_config_file),
        ("创建数据库配置", create_database_config),
        ("创建启动脚本", create_startup_scripts),
        ("验证安装", verify_installation)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n📋 步骤: {step_name}")
        if step_func():
            success_count += 1
        else:
            print(f"❌ 步骤失败: {step_name}")
    
    print(f"\n📊 安装结果: {success_count}/{len(steps)} 个步骤成功")
    
    if success_count >= len(steps) * 0.8:  # 80%成功率
        show_next_steps()
        return True
    else:
        print("❌ 安装失败，请检查错误信息并重试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
