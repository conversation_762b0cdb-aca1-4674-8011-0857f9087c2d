2025-07-24 13:49:18,716 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8080
2025-07-24 13:49:18,716 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 13:49:18,724 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 13:49:18,818 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 13:49:24,082 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/
2025-07-24 13:49:24,083 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 13:49:24,093 - __main__ - INFO - 访问根路径
2025-07-24 13:49:24,093 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8080/ - 状态码: 200 - 耗时: 0.016s
2025-07-24 13:49:24,098 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/health
2025-07-24 13:49:24,099 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 13:49:24,099 - __main__ - INFO - 开始健康检查
2025-07-24 13:49:24,100 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 13:49:24,100 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 13:49:24,100 - __main__ - DEBUG - 开始调用MCP工具: get_database_info, 参数: {}
2025-07-24 13:49:24,101 - __main__ - DEBUG - MCP服务器文件路径: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py
2025-07-24 13:49:24,101 - __main__ - DEBUG - 创建子进程执行MCP工具调用
2025-07-24 13:49:24,121 - __main__ - DEBUG - 等待子进程完成
2025-07-24 13:59:11,258 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8080
2025-07-24 13:59:11,259 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 13:59:11,267 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 13:59:11,352 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 13:59:11,353 - __main__ - INFO - HTTP桥接器已关闭
2025-07-24 14:01:21,583 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8080
2025-07-24 14:01:21,583 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:01:21,590 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:01:21,677 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:01:27,367 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/
2025-07-24 14:01:27,368 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:01:27,368 - __main__ - INFO - 访问根路径
2025-07-24 14:01:27,369 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8080/ - 状态码: 200 - 耗时: 0.000s
2025-07-24 14:01:30,819 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/health
2025-07-24 14:01:30,819 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'http://127.0.0.1:8081', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8081/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-07-24 14:01:30,819 - __main__ - INFO - 开始健康检查
2025-07-24 14:01:30,820 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:01:30,820 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:01:30,820 - __main__ - DEBUG - 开始调用MCP工具: get_database_info, 参数: {}
2025-07-24 14:01:30,821 - __main__ - DEBUG - MCP服务器文件路径: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py
2025-07-24 14:01:30,821 - __main__ - DEBUG - 创建子进程执行MCP工具调用
2025-07-24 14:01:30,841 - __main__ - DEBUG - 等待子进程完成
2025-07-24 14:01:51,554 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/health
2025-07-24 14:01:51,555 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'http://127.0.0.1:8081', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8081/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}
2025-07-24 14:01:51,556 - __main__ - INFO - 开始健康检查
2025-07-24 14:01:51,557 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:01:51,557 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:01:51,558 - __main__ - DEBUG - 开始调用MCP工具: get_database_info, 参数: {}
2025-07-24 14:09:53,543 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8080
2025-07-24 14:09:53,543 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:09:53,551 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:09:53,634 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:09:59,256 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/
2025-07-24 14:09:59,256 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:09:59,257 - __main__ - INFO - 访问根路径
2025-07-24 14:09:59,258 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8080/ - 状态码: 200 - 耗时: 0.000s
2025-07-24 14:10:03,179 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/health
2025-07-24 14:10:03,179 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'http://127.0.0.1:8081', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8081/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}
2025-07-24 14:10:03,180 - __main__ - INFO - 开始健康检查
2025-07-24 14:10:03,180 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:10:03,181 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:10:03,181 - __main__ - ERROR - MCP连接测试失败: 'gbk' codec can't encode character '\U0001f527' in position 2: illegal multibyte sequence
2025-07-24 14:10:03,181 - __main__ - ERROR - 健康检查失败: 'gbk' codec can't encode character '\U0001f527' in position 2: illegal multibyte sequence
2025-07-24 14:10:03,182 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8080/health - 状态码: 503 - 耗时: 0.000s
2025-07-24 14:10:03,928 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/health
2025-07-24 14:10:03,928 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8080', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'http://127.0.0.1:8081', 'sec-fetch-site': 'same-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8081/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}
2025-07-24 14:10:03,929 - __main__ - INFO - 开始健康检查
2025-07-24 14:10:03,929 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:11:37,357 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8080
2025-07-24 14:11:37,357 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:11:37,365 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:11:37,457 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:11:37,458 - __main__ - INFO - HTTP桥接器已关闭
2025-07-24 14:13:47,791 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8080
2025-07-24 14:13:47,791 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:13:47,798 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:13:47,871 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:13:47,872 - __main__ - INFO - HTTP桥接器已关闭
2025-07-24 14:14:47,587 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-24 14:14:47,588 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:14:47,595 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:14:47,682 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:14:47,948 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/
2025-07-24 14:14:47,948 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:14:47,949 - __main__ - INFO - 访问根路径
2025-07-24 14:14:47,949 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8082/ - 状态码: 200 - 耗时: 0.000s
2025-07-24 14:14:47,958 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/health
2025-07-24 14:14:47,958 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:14:47,959 - __main__ - INFO - 开始健康检查
2025-07-24 14:14:47,960 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:14:47,960 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:14:47,961 - __main__ - ERROR - MCP连接测试失败: 'gbk' codec can't encode character '\U0001f527' in position 2: illegal multibyte sequence
2025-07-24 14:14:47,961 - __main__ - ERROR - 健康检查失败: 'gbk' codec can't encode character '\U0001f527' in position 2: illegal multibyte sequence
2025-07-24 14:14:47,962 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8082/health - 状态码: 503 - 耗时: 0.000s
2025-07-24 14:17:28,095 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-24 14:17:28,095 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:17:28,103 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:17:28,186 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:17:28,412 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/
2025-07-24 14:17:28,413 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:17:28,413 - __main__ - INFO - 访问根路径
2025-07-24 14:17:28,414 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8082/ - 状态码: 200 - 耗时: 0.000s
2025-07-24 14:17:28,419 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/health
2025-07-24 14:17:28,419 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:17:28,420 - __main__ - INFO - 开始健康检查
2025-07-24 14:17:28,420 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:17:28,420 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:17:28,421 - __main__ - DEBUG - 开始调用MCP工具: get_database_info, 参数: {}
2025-07-24 14:17:28,421 - __main__ - DEBUG - MCP服务器文件路径: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py
2025-07-24 14:17:28,421 - __main__ - DEBUG - 创建子进程执行MCP工具调用
2025-07-24 14:17:28,441 - __main__ - DEBUG - 等待子进程完成
2025-07-24 14:18:20,660 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-24 14:18:20,661 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:18:20,668 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:18:20,754 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:20:18,522 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/
2025-07-24 14:20:18,523 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:20:18,523 - __main__ - INFO - 访问根路径
2025-07-24 14:20:18,525 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8082/ - 状态码: 200 - 耗时: 0.000s
2025-07-24 14:20:18,531 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/health
2025-07-24 14:20:18,532 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:20:18,533 - __main__ - INFO - 开始健康检查
2025-07-24 14:20:18,534 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:20:18,535 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:20:18,535 - __main__ - DEBUG - 开始调用MCP工具: get_database_info, 参数: {}
2025-07-24 14:20:18,537 - __main__ - DEBUG - MCP服务器文件路径: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py
2025-07-24 14:20:18,538 - __main__ - DEBUG - 创建子进程执行MCP工具调用
2025-07-24 14:20:18,560 - __main__ - DEBUG - 等待子进程完成
2025-07-24 14:21:20,087 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/
2025-07-24 14:21:20,088 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:21:20,089 - __main__ - INFO - 访问根路径
2025-07-24 14:21:20,090 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8082/ - 状态码: 200 - 耗时: 0.016s
2025-07-24 14:21:20,116 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/health
2025-07-24 14:21:20,116 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:21:20,117 - __main__ - INFO - 开始健康检查
2025-07-24 14:21:20,118 - __main__ - DEBUG - MCP服务器文件检查: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py, 存在: True
2025-07-24 14:21:20,118 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:21:20,119 - __main__ - DEBUG - 开始调用MCP工具: get_database_info, 参数: {}
2025-07-24 14:21:20,121 - __main__ - DEBUG - MCP服务器文件路径: C:\Users\<USER>\Desktop\lehu-3\mcp\au-716mcp-2\mysql_analysis_mcp.py
2025-07-24 14:21:20,122 - __main__ - DEBUG - 创建子进程执行MCP工具调用
2025-07-24 14:21:20,139 - __main__ - DEBUG - 等待子进程完成
2025-07-24 14:10:03,929 - __main__ - INFO - 尝试调用MCP工具进行连接测试
2025-07-24 14:22:21,143 - __main__ - ERROR - MCP连接测试失败: 'gbk' codec can't encode character '\U0001f527' in position 2: illegal multibyte sequence
2025-07-24 14:22:21,143 - __main__ - ERROR - 健康检查失败: 'gbk' codec can't encode character '\U0001f527' in position 2: illegal multibyte sequence
2025-07-24 14:22:21,145 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8080/health - 状态码: 503 - 耗时: 737.219s
2025-07-24 14:22:21,146 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8080/
2025-07-24 14:35:31,458 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-24 14:35:31,459 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:35:31,465 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:35:31,539 - __main__ - INFO - HTTP桥接器启动成功
2025-07-24 14:47:48,462 - __main__ - INFO - 收到请求: GET http://127.0.0.1:8082/
2025-07-24 14:47:48,462 - __main__ - DEBUG - 请求头: {'host': '127.0.0.1:8082', 'user-agent': 'python-requests/2.32.4', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept': '*/*', 'connection': 'keep-alive'}
2025-07-24 14:47:48,463 - __main__ - INFO - 访问根路径
2025-07-24 14:47:48,464 - __main__ - INFO - 请求完成: GET http://127.0.0.1:8082/ - 状态码: 200 - 耗时: 0.016s
2025-07-24 14:57:13,834 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-24 14:57:13,835 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-24 14:57:13,842 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-24 14:57:13,930 - __main__ - INFO - HTTP桥接器启动成功
2025-07-28 13:53:51,755 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-28 13:53:51,766 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-28 13:53:51,774 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-28 13:53:51,853 - __main__ - INFO - HTTP桥接器启动成功
2025-07-28 13:54:46,260 - mcp_http_bridge - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-28 13:54:46,261 - mcp_http_bridge - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-28 13:54:46,266 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-28 13:54:46,334 - mcp_http_bridge - INFO - HTTP桥接器启动成功
2025-07-28 13:54:46,335 - mcp_http_bridge - INFO - HTTP桥接器已关闭
2025-07-28 15:31:39,907 - __main__ - INFO - 启动HTTP API桥接器: http://127.0.0.1:8082
2025-07-28 15:31:39,907 - __main__ - INFO - MCP服务器路径: mysql_analysis_mcp.py
2025-07-28 15:31:39,915 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-28 15:31:39,992 - __main__ - INFO - HTTP桥接器启动成功
