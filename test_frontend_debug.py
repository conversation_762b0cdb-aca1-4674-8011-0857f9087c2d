#!/usr/bin/env python3
"""
前端调试测试脚本
用于检查Web界面的实际状态和MCP连接情况
"""

import time
import requests
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_frontend_with_browser():
    """使用浏览器测试前端状态"""
    print("🌐 启动浏览器测试...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    driver = None
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://127.0.0.1:8080")
        
        print("📄 页面加载完成")
        
        # 等待页面加载
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 获取页面标题
        title = driver.title
        print(f"📄 页面标题: {title}")
        
        # 等待连接日志容器出现
        time.sleep(3)
        
        # 检查连接日志
        try:
            log_container = driver.find_element(By.ID, "connectionLog")
            log_content = log_container.text
            print("📋 连接日志内容:")
            print("=" * 50)
            print(log_content)
            print("=" * 50)
        except Exception as e:
            print(f"❌ 无法找到连接日志: {e}")
        
        # 获取控制台日志
        try:
            logs = driver.get_log('browser')
            print("\n🔧 浏览器控制台日志:")
            print("=" * 50)
            for log in logs[-20:]:  # 显示最后20条日志
                level = log['level']
                message = log['message']
                timestamp = log['timestamp']
                print(f"[{level}] {message}")
            print("=" * 50)
        except Exception as e:
            print(f"❌ 无法获取控制台日志: {e}")
        
        # 检查连接状态指示器
        try:
            status_element = driver.find_element(By.CLASS_NAME, "status-indicator")
            status_class = status_element.get_attribute("class")
            status_text = status_element.text
            print(f"🔗 连接状态: {status_text} (class: {status_class})")
        except Exception as e:
            print(f"❌ 无法找到状态指示器: {e}")
        
        # 检查是否有错误通知
        try:
            notifications = driver.find_elements(By.CLASS_NAME, "notification")
            if notifications:
                print("📢 页面通知:")
                for notification in notifications:
                    print(f"  - {notification.text}")
            else:
                print("📢 无页面通知")
        except Exception as e:
            print(f"❌ 检查通知时出错: {e}")
        
        # 尝试点击连接按钮
        try:
            connect_btn = driver.find_element(By.ID, "connectBtn")
            print(f"🔘 连接按钮文本: {connect_btn.text}")
            
            # 如果按钮显示"连接服务器"，尝试点击
            if "连接" in connect_btn.text:
                print("🔘 点击连接按钮...")
                connect_btn.click()
                
                # 等待连接完成
                time.sleep(5)
                
                # 再次检查日志
                try:
                    log_container = driver.find_element(By.ID, "connectionLog")
                    log_content = log_container.text
                    print("\n📋 连接后的日志内容:")
                    print("=" * 50)
                    print(log_content)
                    print("=" * 50)
                except Exception as e:
                    print(f"❌ 无法获取连接后日志: {e}")
        except Exception as e:
            print(f"❌ 连接按钮操作失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def test_servers_status():
    """测试服务器状态"""
    print("\n🔍 检查服务器状态...")
    
    # 测试Web服务器
    try:
        response = requests.get("http://127.0.0.1:8080", timeout=5)
        print(f"🌐 Web服务器状态: {response.status_code}")
    except Exception as e:
        print(f"❌ Web服务器连接失败: {e}")
    
    # 测试MCP服务器
    try:
        response = requests.get("http://127.0.0.1:9002/mcp", timeout=5)
        print(f"🔧 MCP服务器状态: {response.status_code}")
    except Exception as e:
        print(f"❌ MCP服务器连接失败: {e}")

if __name__ == "__main__":
    print("🧪 前端调试测试")
    print("=" * 60)
    
    # 检查服务器状态
    test_servers_status()
    
    # 测试前端
    try:
        success = test_frontend_with_browser()
        if success:
            print("\n✅ 前端测试完成")
        else:
            print("\n❌ 前端测试失败")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        print("💡 提示: 请确保已安装Chrome浏览器和chromedriver")
        print("💡 或者手动在浏览器中打开 http://127.0.0.1:8080 查看")
    
    print("=" * 60)
