#!/usr/bin/env python3
"""
测试新增的LLM功能
"""

import requests
import json
import time

def test_ai_anomaly_detection():
    """测试AI异常检测功能"""
    print("🧪 测试AI异常检测功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    payload = {
        "tool_name": "detect_data_anomalies",
        "arguments": {
            "table": "payment",
            "column": "amount",
            "method": "zscore",
            "threshold": 2.0,
            "enable_ai_analysis": True
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI异常检测成功:")
            
            # 检查是否有AI分析
            if 'ai_analysis' in result.get('result', {}):
                print("🤖 AI异常原因分析:")
                print(result['result']['ai_analysis'])
            else:
                print("⚠️ 未找到AI分析结果")
                
            return True
        else:
            print(f"❌ AI异常检测失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_ai_trend_insights():
    """测试AI趋势洞察功能"""
    print("\n🧪 测试AI趋势洞察功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    payload = {
        "tool_name": "analyze_data_trend",
        "arguments": {
            "table": "payment",
            "time_column": "payment_date",
            "value_column": "amount",
            "period": "day",
            "enable_ai_insights": True
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI趋势分析成功:")
            
            # 检查是否有AI洞察
            if 'ai_insights' in result.get('result', {}):
                print("🤖 AI趋势洞察:")
                print(result['result']['ai_insights'])
            else:
                print("⚠️ 未找到AI洞察结果")
                
            return True
        else:
            print(f"❌ AI趋势分析失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_ai_smart_alert():
    """测试AI智能提醒功能"""
    print("\n🧪 测试AI智能提醒功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    payload = {
        "tool_name": "create_alert",
        "arguments": {
            "alert_type": "value_threshold",
            "table": "payment",
            "column": "amount",
            "threshold": 1000.0,
            "operator": ">",
            "description": "大额支付提醒",
            "enable_ai_priority": True
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI智能提醒创建成功:")
            
            # 检查是否有AI优先级评估
            if 'ai_priority_assessment' in result.get('result', {}):
                print("🤖 AI优先级评估:")
                print(result['result']['ai_priority_assessment'])
            else:
                print("⚠️ 未找到AI优先级评估结果")
                
            return True
        else:
            print(f"❌ AI智能提醒创建失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_comprehensive_ai_analysis():
    """测试综合AI分析功能"""
    print("\n🧪 测试综合AI分析功能...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    payload = {
        "tool_name": "comprehensive_ai_analysis",
        "arguments": {
            "table": "payment",
            "columns": ["amount", "user_id"],
            "analysis_type": "full",
            "time_range_days": 30
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=45)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 综合AI分析成功:")
            
            # 检查是否有AI分析报告
            if 'ai_analysis_report' in result.get('result', {}):
                print("🤖 AI综合分析报告:")
                print(result['result']['ai_analysis_report'])
            else:
                print("⚠️ 未找到AI分析报告")
                
            return True
        else:
            print(f"❌ 综合AI分析失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    print("🚀 开始测试OpenAI GPT-4o-mini增强功能...")
    print("🤖 使用模型: gpt-4o-mini")
    print("=" * 60)

    # 等待系统完全启动
    print("⏳ 等待系统启动...")
    time.sleep(3)

    results = []

    # 测试各项LLM功能
    results.append(("AI异常检测", test_ai_anomaly_detection()))
    results.append(("AI趋势洞察", test_ai_trend_insights()))
    results.append(("AI智能提醒", test_ai_smart_alert()))
    results.append(("综合AI分析", test_comprehensive_ai_analysis()))

    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 OpenAI GPT-4o-mini功能测试结果汇总:")

    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1

    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项AI功能测试通过")

    if success_count == len(results):
        print("🎉 所有OpenAI GPT-4o-mini功能都正常工作！")
    else:
        print("⚠️ 部分AI功能需要检查")
        print("💡 提示：确保OpenAI API密钥有效且网络连接正常")

if __name__ == "__main__":
    main()
