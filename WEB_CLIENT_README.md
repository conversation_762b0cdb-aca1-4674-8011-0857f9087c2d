# 🌐 MySQL数据分析系统 - Web客户端

## 📋 概述

这是一个完全本地化的MySQL数据分析Web系统，包含：
- 📊 **MCP服务器** - 提供数据分析API
- 🌐 **Web客户端** - 用户友好的浏览器界面
- 🔒 **完全离线** - 无需互联网连接

## 🚀 快速启动

### 方法1：一键启动（推荐）
```bash
python start_web_system.py
```

### 方法2：分别启动
```bash
# 终端1：启动MCP服务器
python mysql_analysis_mcp.py

# 终端2：启动Web服务器
python web_server.py
```

## 📁 文件结构

```
├── mysql_analysis_mcp.py      # MCP服务器
├── web_server.py              # Web服务器
├── start_web_system.py        # 一键启动脚本
├── db_config.json             # 数据库配置
├── web_client/                # Web客户端文件
│   ├── index.html            # 主页面
│   ├── styles.css            # 样式文件
│   └── app.js                # JavaScript应用
└── WEB_CLIENT_README.md       # 本文档
```

## 🌟 功能特性

### 📊 数据分析功能
- **实时统计分析** - 求和、平均值、最值、标准差
- **异常检测** - Z-Score和IQR方法检测异常数据
- **趋势分析** - 时间序列数据趋势分析和预测

### 📈 数据可视化
- **柱状图** - 分类数据对比分析
- **饼状图** - 数据分布比例展示
- **趋势图** - 时间序列数据变化趋势

### 💻 查询工具
- **自定义SQL** - 支持复杂SQL查询
- **SQL格式化** - 自动格式化SQL代码
- **结果展示** - 表格形式展示查询结果

### 🎤 智能交互
- **语音助手** - 语音命令控制（开发中）
- **智能提醒** - 数据阈值监控和提醒
- **实时通知** - 操作结果即时反馈

## 🖥️ 界面说明

### 主要页面

1. **📊 仪表板**
   - 数据库连接状态
   - 快速统计信息
   - 最新生成的图表

2. **📈 统计分析**
   - 选择表和列进行统计
   - 支持时间范围过滤
   - 显示详细统计结果

3. **🔍 异常检测**
   - 选择检测方法和阈值
   - 显示异常数据详情
   - 异常率统计

4. **📊 图表生成**
   - 三种图表类型选择
   - 自定义图表参数
   - 实时图表预览

5. **💻 SQL查询**
   - SQL代码编辑器
   - 查询结果表格显示
   - SQL格式化工具

6. **🎤 语音助手**
   - 语音命令输入
   - 系统语音响应
   - 常用命令提示

## 🔧 配置说明

### 服务器配置
- **MCP服务器**: `http://127.0.0.1:9000/mcp/`
- **Web服务器**: `http://127.0.0.1:8080`
- **数据库配置**: `db_config.json`

### 端口配置
如需修改端口，可以：
```bash
# 修改Web服务器端口
python web_server.py 8081

# 或修改一键启动脚本中的端口设置
```

## 🛠️ 故障排除

### 常见问题

1. **无法连接MCP服务器**
   - 检查MCP服务器是否启动
   - 确认端口9000未被占用
   - 检查数据库连接配置

2. **Web界面无法打开**
   - 检查端口8080是否被占用
   - 确认web_client目录下文件完整
   - 尝试手动打开 http://127.0.0.1:8080

3. **图表无法生成**
   - 检查matplotlib和seaborn是否安装
   - 确认数据表中有数据
   - 查看浏览器控制台错误信息

4. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证db_config.json中的连接信息
   - 确认数据库用户权限

### 日志查看
- **MCP服务器日志**: 终端输出
- **Web服务器日志**: 终端输出
- **浏览器日志**: F12开发者工具 → Console

## 🔒 安全特性

- **本地部署** - 所有数据处理在本地进行
- **无网络依赖** - 完全离线运行
- **数据隔离** - 数据不会离开本地环境
- **权限控制** - 基于数据库用户权限

## 📊 支持的数据库

- **MySQL 5.7+** - 主要支持
- **MariaDB 10.2+** - 兼容支持
- **示例数据库** - Sakila示例数据库

## 🎯 使用流程

1. **启动系统**
   ```bash
   python start_web_system.py
   ```

2. **连接数据库**
   - 浏览器自动打开Web界面
   - 点击"连接服务器"按钮
   - 等待连接成功提示

3. **开始分析**
   - 选择相应的功能页面
   - 配置分析参数
   - 查看分析结果

4. **生成图表**
   - 进入"图表生成"页面
   - 选择图表类型和数据
   - 生成并查看图表

5. **自定义查询**
   - 进入"SQL查询"页面
   - 编写SQL语句
   - 执行并查看结果

## 📞 技术支持

如遇到问题，请检查：
1. 系统要求是否满足
2. 必要文件是否完整
3. 数据库连接是否正常
4. 端口是否被占用

## 🎉 开始使用

现在您可以开始使用这个强大的本地化MySQL数据分析系统了！

```bash
# 一键启动
python start_web_system.py

# 然后在浏览器中享受数据分析的乐趣！
```

---

**🚀 MySQL数据分析系统 - 让数据分析变得简单而强大！**
