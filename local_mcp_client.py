#!/usr/bin/env python3
"""
本地MCP客户端
支持语音交互的数据分析客户端
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any, Optional
import threading
from fastmcp import Client

# 语音功能
try:
    import pyttsx3
    import speech_recognition as sr
except ImportError:
    print("⚠️ 语音功能需要安装: pip install pyttsx3 speechrecognition")
    pyttsx3 = None
    sr = None

class LocalMCPClient:
    """本地MCP客户端"""

    def __init__(self, server_url: str = "http://127.0.0.1:9000/mcp/"):
        self.server_url = server_url
        self.client = None
        self.voice_enabled = False
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None

        # 初始化语音功能
        self.init_voice()
    
    def init_voice(self):
        """初始化语音功能"""
        if not pyttsx3 or not sr:
            return
        
        try:
            # 初始化TTS
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 150)
            self.tts_engine.setProperty('volume', 0.8)
            
            # 初始化ASR
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # 调整环境噪音
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
            
            self.voice_enabled = True
            print("✅ 语音功能初始化成功")
            
        except Exception as e:
            print(f"⚠️ 语音功能初始化失败: {e}")
            self.voice_enabled = False
    
    def speak(self, text: str):
        """语音播报"""
        if self.voice_enabled and self.tts_engine:
            try:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            except Exception as e:
                print(f"语音播报失败: {e}")
    
    def listen(self, timeout: int = 5) -> Optional[str]:
        """语音识别"""
        if not self.voice_enabled:
            return None
        
        try:
            print("🎤 请说话...")
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=timeout)
            
            text = self.recognizer.recognize_google(audio, language='zh-CN')
            print(f"识别到: {text}")
            return text
            
        except sr.WaitTimeoutError:
            print("⏰ 语音识别超时")
            return None
        except Exception as e:
            print(f"语音识别失败: {e}")
            return None
    
    async def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            self.client = Client(self.server_url)
            await self.client.connect()
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            if not self.client:
                return {
                    "success": False,
                    "error": "未连接到服务器"
                }

            result = await self.client.call_tool(tool_name, arguments)
            return {
                "success": True,
                "result": result
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"工具调用失败: {str(e)}"
            }
    
    async def check_server_status(self) -> bool:
        """检查服务器状态"""
        try:
            if not self.client:
                return False

            # 尝试获取工具列表来检查连接
            tools = await self.client.list_tools()
            return True
        except:
            return False
    
    async def interactive_mode(self):
        """交互模式"""
        print("🎯 本地MCP数据分析客户端")
        print("=" * 50)

        # 连接到服务器
        if not await self.connect():
            print("❌ 无法连接到MCP服务器")
            print(f"请确保服务器运行在: {self.server_url}")
            return

        print("✅ 已连接到MCP服务器")

        # 获取系统状态
        status = await self.call_tool("get_system_status", {})
        if status.get("success"):
            system_info = status.get("result", {})
            print(f"📋 系统: {system_info.get('system', {}).get('name', 'Unknown')}")
            print(f"🤖 LLM: {system_info.get('llm', {}).get('current_provider', 'Unknown')}")
            print(f"🎤 语音: {'启用' if self.voice_enabled else '禁用'}")
        
        print("\n💡 支持的命令:")
        print("  1. stats - 统计分析")
        print("  2. anomaly - 异常检测")
        print("  3. alert - 创建提醒")
        print("  4. chart - 生成图表")
        print("  5. trend - 趋势分析")
        print("  6. voice - 语音命令")
        print("  7. status - 系统状态")
        print("  8. help - 帮助信息")
        print("  9. quit - 退出")
        
        if self.voice_enabled:
            print("\n🎤 语音模式:")
            print("  输入 'voice' 或说 '语音模式' 进入语音交互")
        
        print("=" * 50)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n📝 请输入命令 (或 'help' 查看帮助): ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                
                elif user_input.lower() in ['help', '帮助']:
                    self.show_help()
                
                elif user_input.lower() in ['status', '状态']:
                    self.handle_status()
                
                elif user_input.lower() in ['voice', '语音', '语音模式']:
                    self.handle_voice_mode()
                
                elif user_input.lower() in ['stats', '统计']:
                    self.handle_statistics()
                
                elif user_input.lower() in ['anomaly', '异常']:
                    self.handle_anomaly()
                
                elif user_input.lower() in ['alert', '提醒']:
                    self.handle_alert()
                
                elif user_input.lower() in ['chart', '图表']:
                    self.handle_chart()
                
                elif user_input.lower() in ['trend', '趋势']:
                    self.handle_trend()
                
                else:
                    print("❓ 未知命令，输入 'help' 查看帮助")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
🔧 命令说明:

📊 stats - 统计分析
   计算指定表和字段的统计信息（求和、平均值等）

🔍 anomaly - 异常检测
   检测数据中的异常值并提供AI分析

⚠️ alert - 创建提醒
   设置数据阈值或时间提醒

📈 chart - 生成图表
   创建柱状图、饼图、折线图等

📉 trend - 趋势分析
   分析数据趋势并提供AI洞察

🎤 voice - 语音交互
   使用语音进行数据分析

🔧 status - 系统状态
   查看系统和LLM状态

❓ help - 帮助信息
   显示此帮助信息

🚪 quit - 退出程序
   退出客户端
        """
        print(help_text)
    
    def handle_status(self):
        """处理状态查询"""
        print("🔍 正在查询系统状态...")
        result = self.call_tool("get_system_status", {})
        
        if result.get("success"):
            status = result.get("result", {})
            print("✅ 系统状态:")
            print(f"  📋 系统: {status.get('system', {}).get('name')}")
            print(f"  🤖 LLM提供者: {status.get('llm', {}).get('current_provider')}")
            print(f"  💾 数据库: {status.get('database', {}).get('status')}")
            print(f"  🎤 语音: {status.get('voice', {})}")
        else:
            print(f"❌ 状态查询失败: {result.get('error')}")
    
    def handle_voice_mode(self):
        """处理语音模式"""
        if not self.voice_enabled:
            print("❌ 语音功能未启用")
            return
        
        print("🎤 进入语音模式，说出您的命令...")
        self.speak("请说出您的数据分析命令")
        
        command = self.listen(timeout=10)
        if command:
            print(f"🗣️ 您说: {command}")
            
            result = self.call_tool("voice_command", {
                "command": command,
                "listen_for_command": False
            })
            
            if result.get("success"):
                response = result.get("result", {}).get("response", "")
                print(f"🤖 回应: {response}")
                self.speak(response)
            else:
                error_msg = f"语音命令处理失败: {result.get('error')}"
                print(f"❌ {error_msg}")
                self.speak(error_msg)
        else:
            print("❌ 未识别到语音命令")
            self.speak("未识别到语音命令")
    
    def handle_statistics(self):
        """处理统计分析"""
        print("📊 统计分析")
        table = input("表名: ").strip()
        column = input("字段名: ").strip()
        operation = input("操作 (sum/avg/count/max/min): ").strip() or "sum"
        
        if not table or not column:
            print("❌ 表名和字段名不能为空")
            return
        
        print(f"🔄 正在计算 {table}.{column} 的 {operation}...")
        
        result = self.call_tool("calculate_statistics", {
            "table": table,
            "column": column,
            "operation": operation
        })
        
        if result.get("success"):
            data = result.get("result", {}).get("data", {})
            print(f"✅ 结果: {data}")
        else:
            print(f"❌ 统计失败: {result.get('error')}")
    
    def handle_anomaly(self):
        """处理异常检测"""
        print("🔍 异常检测")
        table = input("表名: ").strip()
        column = input("字段名: ").strip()
        method = input("方法 (zscore/iqr): ").strip() or "zscore"
        
        if not table or not column:
            print("❌ 表名和字段名不能为空")
            return
        
        print(f"🔄 正在检测 {table}.{column} 的异常...")
        
        result = self.call_tool("detect_anomalies_with_ai", {
            "table": table,
            "column": column,
            "method": method,
            "enable_ai_analysis": True
        })
        
        if result.get("success"):
            res = result.get("result", {})
            print(f"✅ 检测完成:")
            print(f"  异常数量: {res.get('anomaly_count', 0)}")
            print(f"  异常率: {res.get('anomaly_rate', 0):.2f}%")
            
            if res.get("ai_analysis"):
                print(f"🤖 AI分析: {res['ai_analysis'][:200]}...")
        else:
            print(f"❌ 异常检测失败: {result.get('error')}")
    
    def handle_alert(self):
        """处理提醒创建"""
        print("⚠️ 创建提醒")
        alert_type = input("类型 (value_threshold/time_based): ").strip()
        table = input("表名: ").strip()
        
        if alert_type == "value_threshold":
            column = input("字段名: ").strip()
            threshold = float(input("阈值: ").strip())
            operator = input("操作符 (>/</>=/<=/==): ").strip() or ">"
            
            result = self.call_tool("create_smart_alert", {
                "alert_type": alert_type,
                "table": table,
                "column": column,
                "threshold": threshold,
                "operator": operator,
                "enable_ai_priority": True
            })
        else:
            alert_time = input("提醒时间 (YYYY-MM-DD HH:MM:SS): ").strip()
            
            result = self.call_tool("create_smart_alert", {
                "alert_type": alert_type,
                "table": table,
                "alert_time": alert_time,
                "enable_ai_priority": True
            })
        
        if result.get("success"):
            alert_id = result.get("result", {}).get("alert_id")
            print(f"✅ 提醒创建成功，ID: {alert_id}")
        else:
            print(f"❌ 提醒创建失败: {result.get('error')}")
    
    def handle_chart(self):
        """处理图表生成"""
        print("📈 生成图表")
        table = input("表名: ").strip()
        chart_type = input("图表类型 (bar/pie/line/scatter): ").strip()
        x_column = input("X轴字段: ").strip()
        y_column = input("Y轴字段 (可选): ").strip() or None
        
        if not table or not chart_type or not x_column:
            print("❌ 必填字段不能为空")
            return
        
        print(f"🔄 正在生成 {chart_type} 图表...")
        
        result = self.call_tool("generate_chart", {
            "table": table,
            "chart_type": chart_type,
            "x_column": x_column,
            "y_column": y_column,
            "title": f"{table} {chart_type}图表"
        })
        
        if result.get("success"):
            res = result.get("result", {})
            print(f"✅ 图表生成成功:")
            print(f"  数据点: {res.get('data_points', 0)}")
            print(f"  类型: {res.get('chart_type')}")
        else:
            print(f"❌ 图表生成失败: {result.get('error')}")
    
    def handle_trend(self):
        """处理趋势分析"""
        print("📉 趋势分析")
        table = input("表名: ").strip()
        time_column = input("时间字段: ").strip()
        value_column = input("数值字段: ").strip()
        period = input("周期 (hour/day/week/month): ").strip() or "day"
        
        if not table or not time_column or not value_column:
            print("❌ 必填字段不能为空")
            return
        
        print(f"🔄 正在分析 {table}.{value_column} 的趋势...")
        
        result = self.call_tool("analyze_trend_with_ai", {
            "table": table,
            "time_column": time_column,
            "value_column": value_column,
            "period": period,
            "enable_ai_insights": True
        })
        
        if result.get("success"):
            res = result.get("result", {})
            trend = res.get("trend_analysis", {})
            print(f"✅ 趋势分析完成:")
            print(f"  方向: {trend.get('direction')}")
            print(f"  增长率: {trend.get('growth_rate', 0):.2f}%")
            
            if res.get("ai_insights"):
                print(f"🤖 AI洞察: {res['ai_insights'][:200]}...")
        else:
            print(f"❌ 趋势分析失败: {result.get('error')}")

async def main():
    """主函数"""
    client = LocalMCPClient()
    await client.interactive_mode()

if __name__ == "__main__":
    asyncio.run(main())
