#!/usr/bin/env python3
"""
简单的Web服务器 - 使用端口8081
"""

import http.server
import socketserver
import os
import webbrowser
import time

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 设置Web客户端目录为服务根目录
        super().__init__(*args, directory="web_client", **kwargs)
    
    def end_headers(self):
        """添加CORS头部"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"🔍 [WEB DEBUG] [{timestamp}] {format % args}")
        
        # 添加详细的请求信息
        if hasattr(self, 'requestline'):
            print(f"🔍 [WEB DEBUG] 请求行: {self.requestline}")
        if hasattr(self, 'headers'):
            print(f"🔍 [WEB DEBUG] 请求头:")
            for header, value in self.headers.items():
                print(f"    {header}: {value}")
        if hasattr(self, 'path'):
            print(f"🔍 [WEB DEBUG] 请求路径: {self.path}")
        print("🔍 [WEB DEBUG] " + "-" * 40)

def main():
    """主函数"""
    port = 8081  # 使用8081端口避免冲突
    
    print("🌐 简单Web服务器启动")
    print("=" * 40)
    print(f"📍 地址: http://127.0.0.1:{port}")
    print("📁 静态文件目录: web_client")
    print("🎯 等待客户端访问...")
    
    # 检查web_client目录是否存在
    if not os.path.exists('web_client'):
        print("❌ 错误: web_client目录不存在")
        return
    
    # 检查必要文件
    required_files = ['index.html', 'styles.css', 'app.js']
    missing_files = []
    for file in required_files:
        if not os.path.exists(f'web_client/{file}'):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 错误: 缺少必要文件: {', '.join(missing_files)}")
        return
    
    try:
        # 创建HTTP服务器
        with socketserver.TCPServer(("127.0.0.1", port), CORSHTTPRequestHandler) as httpd:
            print(f"✅ Web服务器启动成功，监听端口 {port}")
            
            # 自动打开浏览器
            web_url = f"http://127.0.0.1:{port}"
            print(f"\n正在打开浏览器: {web_url}")
            try:
                webbrowser.open(web_url)
                print("✅ 浏览器已打开")
            except Exception as e:
                print(f"❌ 无法自动打开浏览器: {e}")
                print(f"请手动在浏览器中打开: {web_url}")
            
            print("\n" + "="*60)
            print("Web服务器运行中...")
            print("按 Ctrl+C 停止服务器")
            print("="*60)
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n收到停止信号...")
        print("感谢使用简单Web服务器！")
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
