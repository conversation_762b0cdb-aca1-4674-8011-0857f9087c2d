#!/usr/bin/env python3
"""
预览Excel文件内容
"""

import pandas as pd
import os

def preview_excel_file(file_path: str):
    """预览Excel文件"""
    print(f"🔍 预览Excel文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        sheets = excel_file.sheet_names
        
        print(f"📊 发现 {len(sheets)} 个工作表: {sheets}")
        
        # 预览每个工作表
        for sheet_name in sheets:
            print(f"\n" + "="*50)
            print(f"📋 工作表: {sheet_name}")
            print("="*50)
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"📏 尺寸: {len(df)} 行 × {len(df.columns)} 列")
            print(f"📝 列名: {list(df.columns)}")
            
            if len(df) > 0:
                print(f"\n📄 前5行数据:")
                print(df.head().to_string(index=False))
                
                print(f"\n📊 数据类型:")
                for col in df.columns:
                    dtype = df[col].dtype
                    null_count = df[col].isnull().sum()
                    unique_count = df[col].nunique()
                    print(f"  {col}: {dtype} (空值: {null_count}, 唯一值: {unique_count})")
                
                # 显示数值列的统计信息
                numeric_cols = df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    print(f"\n📈 数值列统计:")
                    print(df[numeric_cols].describe().to_string())
            else:
                print("⚠️ 工作表为空")
        
        print(f"\n" + "="*50)
        print("✅ 预览完成")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        print("💡 可能的原因:")
        print("  - 文件格式不支持")
        print("  - 文件损坏")
        print("  - 缺少依赖库")
        print("\n🔧 解决方案:")
        print("  pip install pandas openpyxl xlrd")

def main():
    """主函数"""
    excel_file = r"date\数据库.xls"
    preview_excel_file(excel_file)

if __name__ == "__main__":
    main()
