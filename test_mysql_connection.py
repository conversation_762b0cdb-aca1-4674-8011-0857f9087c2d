#!/usr/bin/env python3
"""
MySQL连接测试脚本
用于测试不同的用户名和密码组合
"""

import mysql.connector
import json
import os
from mysql.connector import Error

def test_mysql_connection(host, port, user, password, database=None):
    """测试MySQL连接"""
    try:
        print(f"正在测试连接: {user}@{host}:{port}")
        
        connection_config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True
        }
        
        if database:
            connection_config['database'] = database
        
        connection = mysql.connector.connect(**connection_config)
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ 连接成功！MySQL版本: {version[0]}")
            
            # 显示所有数据库
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            print("📁 可用数据库:")
            for db in databases:
                print(f"   - {db[0]}")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 MySQL连接测试工具")
    print("=" * 50)
    
    # 常见的默认配置
    test_configs = [
        {"host": "localhost", "port": 3306, "user": "root", "password": ""},
        {"host": "localhost", "port": 3306, "user": "root", "password": "root"},
        {"host": "localhost", "port": 3306, "user": "root", "password": "123456"},
        {"host": "localhost", "port": 3306, "user": "root", "password": "password"},
        {"host": "127.0.0.1", "port": 3306, "user": "root", "password": ""},
        {"host": "127.0.0.1", "port": 3306, "user": "root", "password": "root"},
    ]
    
    print("🧪 测试常见配置...")
    success_config = None
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n测试配置 {i}:")
        if test_mysql_connection(**config):
            success_config = config
            break
        print("-" * 30)
    
    if success_config:
        print(f"\n🎉 找到可用配置!")
        print(f"主机: {success_config['host']}")
        print(f"端口: {success_config['port']}")
        print(f"用户: {success_config['user']}")
        print(f"密码: {'(空)' if not success_config['password'] else success_config['password']}")
        
        # 更新db_config.json
        update_config = input("\n是否更新 db_config.json 文件? (y/n): ")
        if update_config.lower() == 'y':
            update_db_config(success_config)
    else:
        print("\n❌ 所有测试配置都失败了")
        print("\n💡 建议:")
        print("1. 确认MySQL服务已启动")
        print("2. 检查MySQL安装目录下的错误日志")
        print("3. 尝试重置MySQL root密码")
        
        # 手动输入配置
        manual_test = input("\n是否手动输入配置进行测试? (y/n): ")
        if manual_test.lower() == 'y':
            manual_test_connection()

def manual_test_connection():
    """手动输入配置测试连接"""
    print("\n📝 请输入MySQL连接信息:")
    
    host = input("主机 (默认: localhost): ").strip() or "localhost"
    port = input("端口 (默认: 3306): ").strip() or "3306"
    user = input("用户名 (默认: root): ").strip() or "root"
    password = input("密码: ").strip()
    database = input("数据库 (可选): ").strip() or None
    
    try:
        port = int(port)
    except ValueError:
        print("❌ 端口必须是数字")
        return
    
    config = {
        "host": host,
        "port": port,
        "user": user,
        "password": password
    }
    
    if database:
        config["database"] = database
    
    if test_mysql_connection(**config):
        update_config = input("\n是否更新 db_config.json 文件? (y/n): ")
        if update_config.lower() == 'y':
            update_db_config(config)

def update_db_config(mysql_config):
    """更新db_config.json文件"""
    try:
        # 读取现有配置
        if os.path.exists("db_config.json"):
            with open("db_config.json", 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {}
        
        # 更新MySQL连接信息
        config.update({
            "host": mysql_config["host"],
            "port": mysql_config["port"],
            "user": mysql_config["user"],
            "password": mysql_config["password"],
            "database": mysql_config.get("database", "test"),
            "pool_name": "mysql_analysis_pool",
            "pool_size": 10,
            "pool_reset_session": True,
            "charset": "utf8mb4",
            "use_unicode": True,
            "autocommit": True,
            "connection_timeout": 30,
            "read_timeout": 30,
            "write_timeout": 30
        })
        
        # 保存配置
        with open("db_config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ db_config.json 已更新!")
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")

if __name__ == "__main__":
    main()
