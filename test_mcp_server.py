#!/usr/bin/env python3
"""
测试MCP服务器
最简单的版本用于测试连接
"""

from fastmcp import FastMCP

# 创建最简单的MCP服务器
mcp = FastMCP("测试服务器")

@mcp.tool()
def hello() -> str:
    """简单的hello工具"""
    return "Hello from MCP!"

@mcp.tool()
def get_status() -> dict:
    """获取服务器状态"""
    return {
        "status": "running",
        "message": "测试服务器运行正常",
        "tools": ["hello", "get_status"]
    }

@mcp.tool()
def add_numbers(a: int, b: int) -> dict:
    """简单的加法工具"""
    result = a + b
    return {
        "operation": "addition",
        "a": a,
        "b": b,
        "result": result,
        "message": f"{a} + {b} = {result}"
    }

if __name__ == "__main__":
    print("🚀 启动测试MCP服务器...")
    print("📍 地址: http://127.0.0.1:9001/mcp/")
    print("🔧 工具: hello, get_status, add_numbers")
    print("=" * 50)
    
    try:
        mcp.run(transport="http", host="127.0.0.1", port=9001)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
