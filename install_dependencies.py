#!/usr/bin/env python3
"""
安装OpenAI GPT-4o-mini增强系统的依赖
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def install_python_packages():
    """安装Python包"""
    packages = [
        "fastmcp>=2.0.0",
        "openai>=1.0.0",
        "mysql-connector-python>=8.0.0",
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "scipy>=1.7.0",
        "plotly>=5.0.0",
        "pyttsx3>=2.90",
        "speechrecognition>=3.8.0",
        "requests>=2.25.0"
    ]
    
    print("📦 安装Python依赖包...")
    
    success_count = 0
    for package in packages:
        if run_command(f"pip install {package}", f"安装 {package}"):
            success_count += 1
    
    print(f"\n📊 安装结果: {success_count}/{len(packages)} 个包安装成功")
    return success_count == len(packages)

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("需要Python 3.8或更高版本")
        return False
    
    # 检查pip
    try:
        subprocess.run("pip --version", shell=True, check=True, capture_output=True)
        print("✅ pip可用")
    except subprocess.CalledProcessError:
        print("❌ pip不可用")
        return False
    
    return True

def create_config_files():
    """创建配置文件"""
    print("📝 创建配置文件...")
    
    # 创建数据库配置文件
    db_config = {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "",
        "database": "test",
        "pool_name": "mysql_pool",
        "pool_size": 10,
        "pool_reset_session": True,
        "charset": "utf8mb4",
        "use_unicode": True,
        "autocommit": True
    }
    
    try:
        import json
        with open("db_config.json", "w", encoding="utf-8") as f:
            json.dump(db_config, f, indent=2, ensure_ascii=False)
        print("✅ 数据库配置文件创建完成: db_config.json")
    except Exception as e:
        print(f"❌ 创建数据库配置文件失败: {e}")
        return False
    
    return True

def verify_installation():
    """验证安装"""
    print("🧪 验证安装...")
    
    test_imports = [
        ("fastmcp", "FastMCP"),
        ("openai", "OpenAI"),
        ("mysql.connector", "MySQL连接器"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
        ("matplotlib", "Matplotlib"),
        ("seaborn", "Seaborn"),
        ("plotly", "Plotly"),
        ("scipy", "SciPy")
    ]
    
    success_count = 0
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name}: {e}")
    
    print(f"\n📊 验证结果: {success_count}/{len(test_imports)} 个模块可用")
    return success_count == len(test_imports)

def main():
    """主函数"""
    print("🚀 OpenAI GPT-4o-mini增强系统依赖安装")
    print("=" * 60)
    
    # 检查系统要求
    if not check_system_requirements():
        print("❌ 系统要求检查失败")
        return False
    
    # 安装Python包
    if not install_python_packages():
        print("❌ Python包安装失败")
        return False
    
    # 创建配置文件
    if not create_config_files():
        print("❌ 配置文件创建失败")
        return False
    
    # 验证安装
    if not verify_installation():
        print("❌ 安装验证失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 安装完成!")
    print("\n📋 下一步:")
    print("1. 配置MySQL数据库连接 (编辑 db_config.json)")
    print("2. 测试OpenAI连接: python test_openai_connection.py")
    print("3. 启动系统: python start_fastmcp_system.py")
    print("4. 测试AI功能: python test_llm_features.py")
    print("\n🤖 AI模型配置:")
    print("- 模型: OpenAI GPT-4o-mini")
    print("- API密钥: 已在代码中配置")
    print("- 费用: 相对较低，按使用量计费")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
