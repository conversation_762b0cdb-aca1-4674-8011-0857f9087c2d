#!/usr/bin/env python3
"""
完整系统测试
测试所有组件：MySQL、MCP服务器、Web服务器、前端连接
"""

import requests
import mysql.connector
import socket
import json
import time

def test_mysql():
    """测试MySQL数据库"""
    print("🔍 测试MySQL数据库...")
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='realtime_data',
            charset='utf8mb4',
            connect_timeout=5
        )
        
        cursor = connection.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"✅ MySQL连接成功")
        print(f"📊 数据库: realtime_data")
        print(f"📋 表数量: {len(tables)}")
        if tables:
            print(f"📋 表列表: {', '.join(tables[:5])}{'...' if len(tables) > 5 else ''}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_port(port, name):
    """测试端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        if result == 0:
            print(f"✅ {name} (端口{port}): 运行中")
            return True
        else:
            print(f"❌ {name} (端口{port}): 未运行")
            return False
    except:
        print(f"❌ {name} (端口{port}): 测试失败")
        return False

def test_mcp_server():
    """测试MCP服务器"""
    print("🔍 测试MCP服务器...")
    
    ports_to_test = [9002, 9003]
    working_port = None
    
    for port in ports_to_test:
        if test_port(port, f"MCP服务器"):
            working_port = port
            break
    
    if not working_port:
        print("❌ 没有找到运行中的MCP服务器")
        return False
    
    # 测试MCP协议
    url = f"http://127.0.0.1:{working_port}/mcp"
    
    try:
        # 测试初始化
        init_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "测试客户端", "version": "1.0.0"}
            }
        }
        
        response = requests.post(url, 
            json=init_payload,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            },
            timeout=5
        )
        
        if response.status_code == 200:
            print(f"✅ MCP服务器初始化成功 (端口{working_port})")
            
            # 获取会话ID
            session_id = response.headers.get('mcp-session-id')
            if session_id:
                print(f"🔑 会话ID: {session_id[:20]}...")
                
                # 测试工具调用
                tool_payload = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/call",
                    "params": {
                        "name": "hello",
                        "arguments": {}
                    }
                }
                
                tool_response = requests.post(url,
                    json=tool_payload,
                    headers={
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream',
                        'mcp-session-id': session_id
                    },
                    timeout=5
                )
                
                if tool_response.status_code == 200:
                    print("✅ MCP工具调用成功")
                    return working_port
                else:
                    print(f"❌ MCP工具调用失败: {tool_response.status_code}")
            
        else:
            print(f"❌ MCP服务器初始化失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
    
    return False

def test_web_server():
    """测试Web服务器"""
    print("🔍 测试Web服务器...")
    
    if test_port(8081, "Web服务器"):
        try:
            response = requests.get("http://127.0.0.1:8081", timeout=5)
            if response.status_code == 200:
                print("✅ Web服务器响应正常")
                return True
            else:
                print(f"❌ Web服务器响应异常: {response.status_code}")
        except Exception as e:
            print(f"❌ Web服务器测试失败: {e}")
    
    return False

def main():
    """主测试函数"""
    print("🚀 完整系统测试")
    print("=" * 50)
    
    results = {}
    
    # 1. 测试MySQL
    results['mysql'] = test_mysql()
    print()
    
    # 2. 测试MCP服务器
    results['mcp'] = test_mcp_server()
    print()
    
    # 3. 测试Web服务器
    results['web'] = test_web_server()
    print()
    
    # 总结
    print("📊 测试结果总结")
    print("=" * 50)
    
    all_passed = True
    for component, passed in results.items():
        status = "✅ 正常" if passed else "❌ 异常"
        print(f"{component.upper()}: {status}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有组件测试通过！系统可以正常使用")
        print("🌐 访问地址: http://127.0.0.1:8081")
    else:
        print("⚠️  部分组件存在问题，请检查:")
        if not results['mysql']:
            print("   - 启动MySQL服务器")
        if not results['mcp']:
            print("   - 启动MCP服务器 (python working_mcp_server.py)")
        if not results['web']:
            print("   - 启动Web服务器 (python simple_web_server.py)")

if __name__ == "__main__":
    main()
