#!/usr/bin/env python3
"""
Excel数据导入MySQL工具
支持自动分析Excel结构并导入到MySQL数据库
"""

import pandas as pd
import mysql.connector
import json
import os
import re
from typing import Dict, List, Any, Tuple
from datetime import datetime

class ExcelToMySQLImporter:
    """Excel到MySQL导入器"""
    
    def __init__(self, config_file='config.json'):
        """初始化导入器"""
        self.config = self.load_config(config_file)
        self.connection = None
        
    def load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            return {}
    
    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            db_config = self.config.get('database', {})
            self.connection = mysql.connector.connect(
                host=db_config.get('host', 'localhost'),
                port=db_config.get('port', 3306),
                user=db_config.get('user', 'root'),
                password=db_config.get('password', ''),
                database=db_config.get('database', 'realtime_data'),
                charset=db_config.get('charset', 'utf8mb4')
            )
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def analyze_excel_file(self, file_path: str) -> Dict[str, Any]:
        """分析Excel文件结构"""
        print(f"🔍 分析Excel文件: {file_path}")
        
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(file_path)
            sheets = excel_file.sheet_names
            
            print(f"📊 发现 {len(sheets)} 个工作表: {sheets}")
            
            analysis_result = {
                'file_path': file_path,
                'sheets': {},
                'total_sheets': len(sheets)
            }
            
            # 分析每个工作表
            for sheet_name in sheets:
                print(f"\n📋 分析工作表: {sheet_name}")
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # 基本信息
                sheet_info = {
                    'name': sheet_name,
                    'rows': len(df),
                    'columns': len(df.columns),
                    'column_info': {},
                    'sample_data': df.head(3).to_dict('records') if len(df) > 0 else []
                }
                
                # 分析每列的数据类型
                for col in df.columns:
                    col_data = df[col].dropna()
                    if len(col_data) == 0:
                        col_type = 'VARCHAR(255)'
                    else:
                        col_type = self.infer_mysql_type(col_data)
                    
                    sheet_info['column_info'][col] = {
                        'mysql_type': col_type,
                        'pandas_type': str(df[col].dtype),
                        'null_count': df[col].isnull().sum(),
                        'unique_count': df[col].nunique()
                    }
                
                analysis_result['sheets'][sheet_name] = sheet_info
                
                print(f"  📏 尺寸: {len(df)} 行 × {len(df.columns)} 列")
                print(f"  📝 列名: {list(df.columns)}")
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ 分析Excel文件失败: {e}")
            return {}
    
    def infer_mysql_type(self, series: pd.Series) -> str:
        """推断MySQL数据类型"""
        # 检查是否为数字类型
        if pd.api.types.is_numeric_dtype(series):
            if pd.api.types.is_integer_dtype(series):
                max_val = series.max()
                if max_val <= 127:
                    return 'TINYINT'
                elif max_val <= 32767:
                    return 'SMALLINT'
                elif max_val <= 2147483647:
                    return 'INT'
                else:
                    return 'BIGINT'
            else:
                return 'DECIMAL(10,2)'
        
        # 检查是否为日期时间类型
        if pd.api.types.is_datetime64_any_dtype(series):
            return 'DATETIME'
        
        # 检查字符串长度
        if series.dtype == 'object':
            max_length = series.astype(str).str.len().max()
            if max_length <= 50:
                return f'VARCHAR({max(50, int(max_length * 1.2))})'
            elif max_length <= 255:
                return 'VARCHAR(255)'
            else:
                return 'TEXT'
        
        return 'VARCHAR(255)'
    
    def generate_create_table_sql(self, sheet_info: Dict[str, Any], table_name: str) -> str:
        """生成CREATE TABLE SQL语句"""
        columns = []
        
        # 添加自增主键
        columns.append('id INT AUTO_INCREMENT PRIMARY KEY')
        
        # 添加数据列
        for col_name, col_info in sheet_info['column_info'].items():
            # 清理列名（移除特殊字符）
            clean_col_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', col_name)
            mysql_type = col_info['mysql_type']
            
            # 如果有空值，允许NULL
            null_clause = 'NULL' if col_info['null_count'] > 0 else 'NOT NULL'
            
            columns.append(f'`{clean_col_name}` {mysql_type} {null_clause}')
        
        # 添加时间戳
        columns.append('created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        
        sql = f"""
CREATE TABLE IF NOT EXISTS `{table_name}` (
    {',\n    '.join(columns)}
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
"""
        return sql
    
    def import_sheet_to_mysql(self, file_path: str, sheet_name: str, table_name: str) -> bool:
        """导入工作表到MySQL"""
        try:
            print(f"📥 导入工作表 '{sheet_name}' 到表 '{table_name}'...")
            
            # 读取数据
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            if len(df) == 0:
                print("⚠️ 工作表为空，跳过导入")
                return True
            
            # 清理列名
            df.columns = [re.sub(r'[^\w\u4e00-\u9fff]', '_', col) for col in df.columns]
            
            # 处理空值
            df = df.fillna('')
            
            # 生成INSERT语句
            cursor = self.connection.cursor()
            
            # 准备插入数据
            columns = list(df.columns)
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"INSERT INTO `{table_name}` ({', '.join([f'`{col}`' for col in columns])}) VALUES ({placeholders})"
            
            # 批量插入
            batch_size = 1000
            total_rows = len(df)
            
            for i in range(0, total_rows, batch_size):
                batch_df = df.iloc[i:i+batch_size]
                batch_data = [tuple(row) for row in batch_df.values]
                
                cursor.executemany(insert_sql, batch_data)
                self.connection.commit()
                
                print(f"  📊 已导入 {min(i+batch_size, total_rows)}/{total_rows} 行")
            
            cursor.close()
            print(f"✅ 成功导入 {total_rows} 行数据到表 '{table_name}'")
            return True
            
        except Exception as e:
            print(f"❌ 导入失败: {e}")
            return False
    
    def import_excel_file(self, file_path: str) -> bool:
        """导入整个Excel文件"""
        print(f"🚀 开始导入Excel文件: {file_path}")
        
        # 连接数据库
        if not self.connect_database():
            return False
        
        try:
            # 分析文件
            analysis = self.analyze_excel_file(file_path)
            if not analysis:
                return False
            
            # 显示分析结果
            self.show_analysis_summary(analysis)
            
            # 确认导入
            print("\n" + "="*50)
            print("📋 导入计划:")
            for sheet_name, sheet_info in analysis['sheets'].items():
                table_name = self.suggest_table_name(sheet_name)
                print(f"  工作表 '{sheet_name}' → 表 '{table_name}' ({sheet_info['rows']} 行)")
            
            confirm = input("\n是否继续导入？(y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ 用户取消导入")
                return False
            
            # 执行导入
            success_count = 0
            for sheet_name, sheet_info in analysis['sheets'].items():
                table_name = self.suggest_table_name(sheet_name)
                
                # 创建表
                create_sql = self.generate_create_table_sql(sheet_info, table_name)
                print(f"\n🔧 创建表 '{table_name}'...")
                print(create_sql)
                
                cursor = self.connection.cursor()
                cursor.execute(create_sql)
                cursor.close()
                
                # 导入数据
                if self.import_sheet_to_mysql(file_path, sheet_name, table_name):
                    success_count += 1
            
            print(f"\n🎉 导入完成！成功导入 {success_count}/{len(analysis['sheets'])} 个工作表")
            return True
            
        except Exception as e:
            print(f"❌ 导入过程出错: {e}")
            return False
        finally:
            if self.connection:
                self.connection.close()
    
    def suggest_table_name(self, sheet_name: str) -> str:
        """建议表名"""
        # 清理表名
        table_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', sheet_name.lower())
        table_name = re.sub(r'_+', '_', table_name)
        table_name = table_name.strip('_')
        
        if not table_name:
            table_name = 'imported_data'
        
        return table_name
    
    def show_analysis_summary(self, analysis: Dict[str, Any]):
        """显示分析摘要"""
        print("\n" + "="*60)
        print("📊 Excel文件分析结果")
        print("="*60)
        
        for sheet_name, sheet_info in analysis['sheets'].items():
            print(f"\n📋 工作表: {sheet_name}")
            print(f"  📏 尺寸: {sheet_info['rows']} 行 × {sheet_info['columns']} 列")
            print(f"  📝 列信息:")
            
            for col_name, col_info in sheet_info['column_info'].items():
                print(f"    {col_name}: {col_info['mysql_type']} (空值: {col_info['null_count']})")
            
            if sheet_info['sample_data']:
                print(f"  📄 示例数据:")
                for i, row in enumerate(sheet_info['sample_data'][:2]):
                    print(f"    行{i+1}: {row}")

def main():
    """主函数"""
    print("📊 Excel到MySQL导入工具")
    print("="*40)
    
    # 检查Excel文件
    excel_file = r"date\数据库.xls"
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    # 创建导入器
    importer = ExcelToMySQLImporter()
    
    # 执行导入
    success = importer.import_excel_file(excel_file)
    
    if success:
        print("\n🎉 导入成功！")
        print("\n📋 下一步:")
        print("1. 启动MCP服务器: python local_mcp_server.py")
        print("2. 启动MCP客户端: python local_mcp_client.py")
        print("3. 使用语音或文本命令分析你的数据")
    else:
        print("\n❌ 导入失败，请检查错误信息")

if __name__ == "__main__":
    main()
