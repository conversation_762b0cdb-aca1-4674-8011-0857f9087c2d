#!/usr/bin/env python3
"""
可视化图表测试脚本 - 实际生成和显示图表
"""

import asyncio
import json
import base64
import os
import webbrowser
from datetime import datetime
import sys

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    sys.exit(1)

async def generate_and_display_charts():
    """生成并显示图表"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("🎨 MySQL数据分析图表可视化测试")
    print("=" * 60)
    
    charts_data = []
    
    try:
        async with Client(server_url) as client:
            print("✅ MCP服务器连接成功")
            
            # 1. 生成柱状图
            print("\n📊 生成柱状图...")
            result = await client.call_tool("generate_bar_chart", {
                "table": "film",
                "x_column": "rating",
                "y_column": "rental_rate",
                "title": "电影评级与租赁费用分析",
                "limit": 10
            })
            
            if result.data.get('success'):
                chart_data = result.data.get('chart_data', {})
                if 'image_base64' in chart_data:
                    # 保存图片
                    filename = save_base64_image(chart_data['image_base64'], "bar_chart")
                    charts_data.append({
                        "title": chart_data.get('title', '柱状图'),
                        "type": "柱状图",
                        "filename": filename,
                        "data": chart_data.get('data', [])
                    })
                    print(f"   ✅ 柱状图已保存: {filename}")
                else:
                    print("   ❌ 柱状图数据中没有图片")
            else:
                print(f"   ❌ 柱状图生成失败: {result.data.get('error')}")
            
            # 2. 生成饼状图
            print("\n🥧 生成饼状图...")
            result = await client.call_tool("generate_pie_chart", {
                "table": "film",
                "label_column": "rating",
                "value_column": "rental_rate",
                "title": "电影评级分布"
            })
            
            if result.data.get('success'):
                chart_data = result.data.get('chart_data', {})
                if 'image_base64' in chart_data:
                    filename = save_base64_image(chart_data['image_base64'], "pie_chart")
                    charts_data.append({
                        "title": chart_data.get('title', '饼状图'),
                        "type": "饼状图",
                        "filename": filename,
                        "data": chart_data.get('data', [])
                    })
                    print(f"   ✅ 饼状图已保存: {filename}")
                else:
                    print("   ❌ 饼状图数据中没有图片")
            else:
                print(f"   ❌ 饼状图生成失败: {result.data.get('error')}")
            
            # 3. 生成趋势图
            print("\n📈 生成趋势图...")
            result = await client.call_tool("generate_trend_chart", {
                "table": "payment",
                "x_column": "payment_date",
                "y_column": "amount",
                "title": "支付金额趋势分析",
                "time_range": "30 DAY"
            })
            
            if result.data.get('success'):
                chart_data = result.data.get('chart_data', {})
                if 'image_base64' in chart_data:
                    filename = save_base64_image(chart_data['image_base64'], "trend_chart")
                    charts_data.append({
                        "title": chart_data.get('title', '趋势图'),
                        "type": "趋势图",
                        "filename": filename,
                        "data": chart_data.get('data', [])
                    })
                    print(f"   ✅ 趋势图已保存: {filename}")
                else:
                    print("   ❌ 趋势图数据中没有图片")
            else:
                print(f"   ❌ 趋势图生成失败: {result.data.get('error')}")
            
            # 生成HTML展示页面
            if charts_data:
                html_file = create_chart_gallery(charts_data)
                print(f"\n🌐 图表展示页面已生成: {html_file}")
                print("🚀 正在浏览器中打开...")
                
                # 在浏览器中打开
                webbrowser.open(f"file://{os.path.abspath(html_file)}")
                
                print("\n📋 生成的图表:")
                for i, chart in enumerate(charts_data, 1):
                    print(f"   {i}. {chart['type']}: {chart['title']}")
                    print(f"      文件: {chart['filename']}")
                    print(f"      数据点: {len(chart['data'])} 个")
                
            else:
                print("\n❌ 没有成功生成任何图表")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

def save_base64_image(base64_data: str, prefix: str) -> str:
    """保存base64图片数据为PNG文件"""
    try:
        # 解码base64数据
        image_data = base64.b64decode(base64_data)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{prefix}_{timestamp}.png"
        
        # 保存文件
        with open(filename, 'wb') as f:
            f.write(image_data)
        
        return filename
    except Exception as e:
        print(f"保存图片失败: {e}")
        return ""

def create_chart_gallery(charts_data: list) -> str:
    """创建图表展示HTML页面"""
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL数据分析图表展示</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }}
        h1 {{
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }}
        .chart-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }}
        .chart-card {{
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: #f9f9f9;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        .chart-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }}
        .chart-title {{
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }}
        .chart-type {{
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            display: inline-block;
            margin-bottom: 15px;
        }}
        .chart-image {{
            width: 100%;
            height: auto;
            border-radius: 8px;
            border: 1px solid #ccc;
        }}
        .chart-info {{
            margin-top: 15px;
            font-size: 0.9em;
            color: #666;
        }}
        .timestamp {{
            text-align: center;
            color: #888;
            margin-top: 30px;
            font-style: italic;
        }}
        .stats {{
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 MySQL数据分析图表展示</h1>
        
        <div class="stats">
            <strong>📊 生成统计:</strong> 共生成 {len(charts_data)} 个图表 | 
            <strong>🕒 生成时间:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        </div>
        
        <div class="chart-grid">
"""
    
    for chart in charts_data:
        html_content += f"""
            <div class="chart-card">
                <div class="chart-type">{chart['type']}</div>
                <div class="chart-title">{chart['title']}</div>
                <img src="{chart['filename']}" alt="{chart['title']}" class="chart-image">
                <div class="chart-info">
                    📁 文件: {chart['filename']}<br>
                    📈 数据点: {len(chart['data'])} 个
                </div>
            </div>
"""
    
    html_content += f"""
        </div>
        
        <div class="timestamp">
            🚀 由 MySQL数据分析MCP服务器 生成 | 
            🔗 服务器地址: http://127.0.0.1:9000/mcp/
        </div>
    </div>
</body>
</html>
"""
    
    # 保存HTML文件
    html_filename = f"chart_gallery_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    with open(html_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_filename

if __name__ == "__main__":
    try:
        asyncio.run(generate_and_display_charts())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}")
