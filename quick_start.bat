@echo off
title MySQL数据库分析MCP服务器 - 快速启动
color 0A

echo.
echo     ███╗   ███╗██╗   ██╗███████╗ ██████╗ ██╗         
echo     ████╗ ████║╚██╗ ██╔╝██╔════╝██╔═══██╗██║         
echo     ██╔████╔██║ ╚████╔╝ ███████╗██║   ██║██║         
echo     ██║╚██╔╝██║  ╚██╔╝  ╚════██║██║▄▄ ██║██║         
echo     ██║ ╚═╝ ██║   ██║   ███████║╚██████╔╝███████╗    
echo     ╚═╝     ╚═╝   ╚═╝   ╚══════╝ ╚══▀▀═╝ ╚══════╝    
echo.
echo     ███╗   ███╗ ██████╗██████╗     ███████╗███████╗██████╗ ██╗   ██╗███████╗██████╗ 
echo     ████╗ ████║██╔════╝██╔══██╗    ██╔════╝██╔════╝██╔══██╗██║   ██║██╔════╝██╔══██╗
echo     ██╔████╔██║██║     ██████╔╝    ███████╗█████╗  ██████╔╝██║   ██║█████╗  ██████╔╝
echo     ██║╚██╔╝██║██║     ██╔═══╝     ╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══╝  ██╔══██╗
echo     ██║ ╚═╝ ██║╚██████╗██║         ███████║███████╗██║  ██║ ╚████╔╝ ███████╗██║  ██║
echo     ╚═╝     ╚═╝ ╚═════╝╚═╝         ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝
echo.
echo ================================================================================
echo                        MySQL数据库分析MCP服务器 - 快速启动
echo ================================================================================
echo.

:MENU
echo 请选择操作:
echo.
echo [1] 首次安装和配置
echo [2] 启动服务器
echo [3] 创建示例数据
echo [4] 测试服务器
echo [5] 查看帮助
echo [0] 退出
echo.
set /p choice="请输入选择 (0-5): "

if "%choice%"=="1" goto INSTALL
if "%choice%"=="2" goto START
if "%choice%"=="3" goto SAMPLE_DATA
if "%choice%"=="4" goto TEST
if "%choice%"=="5" goto HELP
if "%choice%"=="0" goto EXIT
echo 无效选择，请重新输入
goto MENU

:INSTALL
echo.
echo ================================================================================
echo                                首次安装和配置
echo ================================================================================
echo.
echo 步骤1: 安装依赖包...
call install.bat
if %errorlevel% neq 0 (
    echo 安装失败，请检查错误信息
    pause
    goto MENU
)

echo.
echo 步骤2: 配置数据库连接...
python setup_wizard.py
if %errorlevel% neq 0 (
    echo 配置失败，请检查错误信息
    pause
    goto MENU
)

echo.
echo ✅ 安装和配置完成！
pause
goto MENU

:START
echo.
echo ================================================================================
echo                                启动服务器
echo ================================================================================
echo.
if not exist db_config.json (
    echo ❌ 未找到配置文件，请先运行安装和配置
    pause
    goto MENU
)

if exist start_configured.bat (
    call start_configured.bat
) else (
    call start.bat
)
goto MENU

:SAMPLE_DATA
echo.
echo ================================================================================
echo                                创建示例数据
echo ================================================================================
echo.
if not exist db_config.json (
    echo ❌ 未找到配置文件，请先运行安装和配置
    pause
    goto MENU
)

echo 正在创建示例数据...
python create_sample_data.py
pause
goto MENU

:TEST
echo.
echo ================================================================================
echo                                测试服务器
echo ================================================================================
echo.
echo 正在启动测试客户端...
python test_client.py
pause
goto MENU

:HELP
echo.
echo ================================================================================
echo                                    帮助信息
echo ================================================================================
echo.
echo 📖 使用说明:
echo.
echo 1. 首次使用请选择 [1] 进行安装和配置
echo 2. 配置完成后选择 [2] 启动服务器
echo 3. 可选择 [3] 创建示例数据进行测试
echo 4. 使用 [4] 测试服务器功能
echo.
echo 🔧 手动操作:
echo - 编辑 db_config.json 修改数据库配置
echo - 编辑 server_config.json 修改服务器配置
echo - 查看 README.md 获取详细文档
echo.
echo 🌐 默认服务器地址: http://127.0.0.1:9000/mcp/
echo.
echo 📁 重要文件:
echo - mysql_analysis_mcp.py : 主服务器文件
echo - db_config.json : 数据库配置
echo - requirements.txt : 依赖包列表
echo - README.md : 详细文档
echo.
pause
goto MENU

:EXIT
echo.
echo 感谢使用 MySQL数据库分析MCP服务器！
echo 如有问题请查看 README.md 或联系技术支持
echo.
pause
exit

:ERROR
echo.
echo ❌ 发生错误，请检查:
echo 1. Python环境是否正确安装
echo 2. 数据库连接是否正常
echo 3. 依赖包是否完整安装
echo.
pause
goto MENU
