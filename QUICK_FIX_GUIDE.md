# 🚀 快速修复指南

## ❌ 问题：数据库连接失败

你遇到的错误：`Can't connect to MySQL server on 'localhost:3306' (10061)`

## 🔧 解决方案

### 方案1：快速启动（推荐）
```bash
python quick_start.py
```
这个脚本会：
- ✅ 自动检测MySQL状态
- ✅ 尝试启动MySQL服务
- ✅ 帮助配置数据库连接
- ✅ 提供演示模式选项

### 方案2：演示模式（无需数据库）
```bash
python local_mcp_server.py --demo-mode
```
演示模式功能：
- ✅ LLM功能测试
- ✅ 语音交互
- ✅ 系统状态检查
- ❌ 数据库相关功能（暂时不可用）

### 方案3：手动修复数据库

#### 3.1 检查MySQL服务状态
```bash
# Windows
net start mysql

# Linux
sudo systemctl start mysql

# macOS
brew services start mysql
```

#### 3.2 安装MySQL（如果未安装）
```bash
python setup_database.py
```

#### 3.3 修改配置文件
编辑 `config.json`：
```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "",
    "database": "realtime_data"
  }
}
```

## 🎯 推荐流程

### 1. 立即体验（演示模式）
```bash
python local_mcp_server.py --demo-mode
```

### 2. 配置数据库（完整功能）
```bash
python setup_database.py
python quick_start.py
```

### 3. 启动客户端
```bash
python local_mcp_client.py
```

## 📊 功能对比

| 功能 | 演示模式 | 完整模式 |
|------|----------|----------|
| LLM分析 | ✅ | ✅ |
| 语音交互 | ✅ | ✅ |
| 系统状态 | ✅ | ✅ |
| 统计分析 | ❌ | ✅ |
| 异常检测 | ❌ | ✅ |
| 图表生成 | ❌ | ✅ |
| 趋势分析 | ❌ | ✅ |
| 智能提醒 | ❌ | ✅ |

## 💡 常见问题

### Q: MySQL服务启动失败？
A: 
1. 检查是否已安装MySQL
2. 以管理员身份运行命令
3. 检查端口3306是否被占用

### Q: 认证失败？
A: 
1. 检查用户名密码
2. 重置MySQL root密码
3. 使用空密码（测试环境）

### Q: 想要完全离线？
A: 
1. 当前：演示模式 + OpenAI（需网络）
2. 将来：安装本地LLM（完全离线）

## 🔄 LLM切换计划

### 当前阶段（测试）
- 使用OpenAI GPT-4o-mini
- 需要网络连接
- 功能完整测试

### 将来阶段（生产）
- 安装Ollama或LM Studio
- 完全本地化
- 无需网络连接

切换方法：
```json
{
  "llm": {
    "provider": "local"  // 从 "openai" 改为 "local"
  }
}
```

## 🎉 总结

**立即开始**：
```bash
python local_mcp_server.py --demo-mode
```

**完整体验**：
```bash
python quick_start.py
```

系统设计为渐进式部署，先体验功能，再完善配置！
