<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工业数据分析系统 - MCP版</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    <!-- ECharts 企业级图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- ECharts 主题 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/theme/macarons.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/theme/dark.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🏭</span>
                    <h1>工业数据分析系统</h1>
                    <span class="version">v1.0 MCP版</span>
                </div>
                <div class="connection-status">
                    <span id="connectionStatus" class="status-indicator offline">离线</span>
                    <button id="connectBtn" class="btn btn-primary">连接服务器</button>
                    <button onclick="testConnection()" class="btn btn-secondary" style="margin-left: 10px;">测试连接</button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <nav class="nav-menu">
                    <div class="nav-section">
                        <h3>📈 数据分析</h3>
                        <ul>
                            <li><a href="#" data-tab="dashboard" class="nav-link active">仪表板</a></li>
                            <li><a href="#" data-tab="statistics" class="nav-link">统计分析</a></li>
                            <li><a href="#" data-tab="anomaly" class="nav-link">异常检测</a></li>
                            <li><a href="#" data-tab="trends" class="nav-link">趋势分析</a></li>
                        </ul>
                    </div>
                    <div class="nav-section">
                        <h3>📊 数据可视化</h3>
                        <ul>
                            <li><a href="#" data-tab="charts" class="nav-link">图表生成</a></li>
                            <li><a href="#" data-tab="gallery" class="nav-link">图表库</a></li>
                        </ul>
                    </div>
                    <div class="nav-section">
                        <h3>🔧 工具</h3>
                        <ul>
                            <li><a href="#" data-tab="sql" class="nav-link">SQL查询</a></li>
                            <li><a href="#" data-tab="alerts" class="nav-link">提醒管理</a></li>
                            <li><a href="#" data-tab="voice" class="nav-link">语音助手</a></li>
                        </ul>
                    </div>
                </nav>
            </aside>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 仪表板 -->
                <div id="dashboard" class="tab-content active">
                    <div class="page-header">
                        <h2>🏭 工业数据仪表板</h2>
                        <p>实时监控工业设备数据和系统状态</p>
                    </div>
                    
                    <div class="dashboard-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3>系统信息</h3>
                                <button id="refreshDbInfo" class="btn btn-sm">刷新</button>
                            </div>
                            <div class="card-content">
                                <div id="dbInfo">
                                    <div class="loading">加载中...</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>快速统计</h3>
                            </div>
                            <div class="card-content">
                                <div id="quickStats" class="stats-grid">
                                    <div class="stat-item">
                                        <span class="stat-label">总记录数</span>
                                        <span class="stat-value" id="totalRecords">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">平均压力1</span>
                                        <span class="stat-value" id="avgAmount">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">异常数据</span>
                                        <span class="stat-value" id="anomalyCount">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card full-width">
                            <div class="card-header">
                                <h3>最新图表</h3>
                                <button id="generateQuickChart" class="btn btn-sm">生成图表</button>
                            </div>
                            <div class="card-content">
                                <div id="latestChart" class="chart-container">
                                    <div class="empty-state">
                                        <span class="empty-icon">📈</span>
                                        <p>点击"生成图表"创建您的第一个图表</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计分析 -->
                <div id="statistics" class="tab-content">
                    <div class="page-header">
                        <h2>📈 统计分析</h2>
                        <p>对实时数据库进行深入的统计分析，支持语音交互和大数据量处理</p>
                    </div>

                    <div class="analysis-form">
                        <!-- 基础配置 -->
                        <div class="form-section">
                            <h4>📊 基础配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="statsTable">选择表</label>
                                    <select id="statsTable" class="form-control">
                                        <option value="payment">payment (支付表)</option>
                                        <option value="rental">rental (租赁表)</option>
                                        <option value="film">film (电影表)</option>
                                        <option value="customer">customer (客户表)</option>
                                        <option value="inventory">inventory (库存表)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="statsColumn">选择列</label>
                                    <select id="statsColumn" class="form-control">
                                        <option value="amount">amount (金额)</option>
                                        <option value="rental_rate">rental_rate (租赁费用)</option>
                                        <option value="length">length (时长)</option>
                                        <option value="replacement_cost">replacement_cost (替换成本)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 时间维度配置 -->
                        <div class="form-section">
                            <h4>⏰ 时间范围</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="statsStartTime">开始时间</label>
                                    <input type="datetime-local" id="statsStartTime" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="statsEndTime">结束时间</label>
                                    <input type="datetime-local" id="statsEndTime" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="statsTimeColumn">时间列</label>
                                    <select id="statsTimeColumn" class="form-control">
                                        <option value="created_at">created_at (创建时间)</option>
                                        <option value="payment_date">payment_date (支付日期)</option>
                                        <option value="rental_date">rental_date (租赁日期)</option>
                                        <option value="last_update">last_update (最后更新)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 统计方法配置 -->
                        <div class="form-section">
                            <h4>📈 统计方法</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>统计指标</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsCount" checked> 计数 (COUNT)
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsSum" checked> 求和 (SUM)
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsAvg" checked> 平均值 (AVG)
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsMin" checked> 最小值 (MIN)
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsMax" checked> 最大值 (MAX)
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsStdDev" checked> 标准差 (STDDEV)
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsVariance"> 方差 (VARIANCE)
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="statsMedian"> 中位数 (MEDIAN)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分组统计配置 -->
                        <div class="form-section">
                            <h4>📊 分组统计</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="statsGroupBy">分组列（可选）</label>
                                    <select id="statsGroupBy" class="form-control">
                                        <option value="">不分组</option>
                                        <option value="category">category (分类)</option>
                                        <option value="rating">rating (评级)</option>
                                        <option value="staff_id">staff_id (员工ID)</option>
                                        <option value="customer_id">customer_id (客户ID)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="statsLimit">结果限制</label>
                                    <select id="statsLimit" class="form-control">
                                        <option value="100">前100条</option>
                                        <option value="500">前500条</option>
                                        <option value="1000">前1000条</option>
                                        <option value="5000">前5000条</option>
                                        <option value="">全部数据</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 实时监控配置 -->
                        <div class="form-section">
                            <h4>🔄 实时监控</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="statsRealTime"> 启用实时监控
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="statsRefreshInterval">刷新间隔</label>
                                    <select id="statsRefreshInterval" class="form-control">
                                        <option value="5">5秒</option>
                                        <option value="10">10秒</option>
                                        <option value="30" selected>30秒</option>
                                        <option value="60">1分钟</option>
                                        <option value="300">5分钟</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 提醒配置 -->
                        <div class="form-section">
                            <h4>🔔 统计提醒</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="statsEnableAlert"> 启用统计提醒
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="statsAlertMetric">提醒指标</label>
                                    <select id="statsAlertMetric" class="form-control">
                                        <option value="count">记录数量</option>
                                        <option value="sum">总和</option>
                                        <option value="average">平均值</option>
                                        <option value="max">最大值</option>
                                        <option value="min">最小值</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="statsAlertOperator">比较操作</label>
                                    <select id="statsAlertOperator" class="form-control">
                                        <option value=">">大于</option>
                                        <option value="<">小于</option>
                                        <option value=">=">大于等于</option>
                                        <option value="<=">小于等于</option>
                                        <option value="=">等于</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="statsAlertThreshold">阈值</label>
                                    <input type="number" id="statsAlertThreshold" class="form-control" placeholder="输入阈值">
                                </div>
                            </div>
                        </div>

                        <!-- 语音控制 -->
                        <div class="form-section">
                            <h4>🎤 语音控制</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <button id="statsVoiceControl" class="btn btn-voice">
                                        🎤 语音设置参数
                                    </button>
                                    <div id="statsVoiceStatus" class="voice-status">点击开始语音控制</div>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="statsVoiceReport" checked> 语音播报结果
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-actions">
                            <button id="runStatistics" class="btn btn-primary">
                                📊 运行统计分析
                            </button>
                            <button id="exportStatistics" class="btn btn-secondary">
                                📁 导出结果
                            </button>
                            <button id="clearStatistics" class="btn btn-outline">
                                🗑️ 清空结果
                            </button>
                        </div>
                    </div>

                    <!-- 统计结果显示区域 -->
                    <div id="statisticsResult" class="result-area">
                        <div class="result-header">
                            <h3>📈 统计结果</h3>
                            <div class="result-controls">
                                <button id="statsToggleChart" class="btn btn-sm">📊 切换图表</button>
                                <button id="statsRefreshNow" class="btn btn-sm">🔄 立即刷新</button>
                            </div>
                        </div>
                        <div id="statisticsContent" class="result-content">
                            <div class="empty-state">
                                <div class="empty-icon">📊</div>
                                <div class="empty-text">请配置参数并运行统计分析</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 异常检测 -->
                <div id="anomaly" class="tab-content">
                    <div class="page-header">
                        <h2>🔍 异常检测</h2>
                        <p>使用统计方法检测数据中的异常值</p>
                    </div>
                    
                    <div class="analysis-form">
                        <!-- 基础配置 -->
                        <div class="form-section">
                            <h4>📊 基础配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="anomalyTable">选择表</label>
                                    <select id="anomalyTable" class="form-control">
                                        <option value="payment">payment (支付表)</option>
                                        <option value="rental">rental (租赁表)</option>
                                        <option value="film">film (电影表)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="anomalyColumn">选择列</label>
                                    <select id="anomalyColumn" class="form-control">
                                        <option value="amount">amount (金额)</option>
                                        <option value="rental_rate">rental_rate (租赁费用)</option>
                                        <option value="length">length (时长)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 时间维度配置 -->
                        <div class="form-section">
                            <h4>⏰ 时间范围</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="anomalyStartTime">开始时间</label>
                                    <input type="datetime-local" id="anomalyStartTime" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="anomalyEndTime">结束时间</label>
                                    <input type="datetime-local" id="anomalyEndTime" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="anomalyTimeColumn">时间列</label>
                                    <select id="anomalyTimeColumn" class="form-control">
                                        <option value="created_at">created_at (创建时间)</option>
                                        <option value="updated_at">updated_at (更新时间)</option>
                                        <option value="payment_date">payment_date (支付时间)</option>
                                        <option value="rental_date">rental_date (租赁时间)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 检测方法配置 -->
                        <div class="form-section">
                            <h4>🔍 检测方法</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="anomalyMethod">检测方法</label>
                                    <select id="anomalyMethod" class="form-control">
                                        <option value="zscore">Z-Score 统计方法</option>
                                        <option value="iqr">IQR 四分位数方法</option>
                                        <option value="business_rule">业务规则检测</option>
                                        <option value="threshold">阈值范围检测</option>
                                        <option value="percentile">百分位数检测</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="anomalyThreshold">阈值</label>
                                    <input type="number" id="anomalyThreshold" class="form-control" value="2.0" step="0.1" min="0.1">
                                </div>
                                <div class="form-group" id="businessRuleGroup" style="display: none;">
                                    <label for="businessRule">业务规则</label>
                                    <textarea id="businessRule" class="form-control" rows="2" placeholder="例如: amount > 1000 OR amount < 0.01"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 实时监控配置 -->
                        <div class="form-section">
                            <h4>📡 实时监控</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="realTimeMonitoring"> 启用实时监控
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="monitoringInterval">监控间隔 (秒)</label>
                                    <select id="monitoringInterval" class="form-control">
                                        <option value="30">30秒</option>
                                        <option value="60" selected>1分钟</option>
                                        <option value="300">5分钟</option>
                                        <option value="600">10分钟</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 告警配置 -->
                        <div class="form-section">
                            <h4>🚨 告警设置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="voiceAlert" checked> 语音播报
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="soundAlert" checked> 声音告警
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="desktopNotification" checked> 桌面通知
                                    </label>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="alertThreshold">告警阈值 (异常率%)</label>
                                    <input type="number" id="alertThreshold" class="form-control" value="5" min="0" max="100" step="0.1">
                                </div>
                                <div class="form-group">
                                    <label for="alertLevel">告警级别</label>
                                    <select id="alertLevel" class="form-control">
                                        <option value="info">信息 (蓝色)</option>
                                        <option value="warning" selected>警告 (黄色)</option>
                                        <option value="critical">严重 (红色)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 性能优化配置 -->
                        <div class="form-section">
                            <h4>⚡ 性能优化</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="batchSize">批处理大小</label>
                                    <select id="batchSize" class="form-control">
                                        <option value="1000" selected>1,000 条</option>
                                        <option value="5000">5,000 条</option>
                                        <option value="10000">10,000 条</option>
                                        <option value="50000">50,000 条</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="maxResults">最大结果数</label>
                                    <select id="maxResults" class="form-control">
                                        <option value="100" selected>100 条</option>
                                        <option value="500">500 条</option>
                                        <option value="1000">1,000 条</option>
                                        <option value="0">不限制</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableSampling"> 启用数据采样
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 语音控制 -->
                        <div class="form-section">
                            <h4>🎤 语音控制</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <button id="startVoiceControl" class="btn btn-voice">🎤 开始语音控制</button>
                                    <div id="voiceStatus" class="voice-status">点击开始语音控制</div>
                                </div>
                                <div class="form-group">
                                    <label for="voiceLanguage">语音语言</label>
                                    <select id="voiceLanguage" class="form-control">
                                        <option value="zh-CN" selected>中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-actions">
                            <button id="runAnomalyDetection" class="btn btn-primary">🔍 检测异常</button>
                            <button id="toggleRealTimeMonitoring" class="btn btn-secondary" disabled>📡 开始监控</button>
                            <button id="exportAnomalyResults" class="btn btn-info" disabled>📊 导出结果</button>
                            <button id="clearResults" class="btn btn-outline">🗑️ 清空结果</button>
                        </div>
                    </div>

                    <!-- 实时监控状态 -->
                    <div id="monitoringStatus" class="monitoring-status" style="display: none;">
                        <div class="status-header">
                            <h4>📡 实时监控状态</h4>
                            <div class="status-indicator">
                                <span class="status-dot"></span>
                                <span class="status-text">监控中...</span>
                            </div>
                        </div>
                        <div class="monitoring-info">
                            <div class="info-item">
                                <span class="info-label">监控间隔:</span>
                                <span class="info-value" id="currentInterval">60秒</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">下次检测:</span>
                                <span class="info-value" id="nextCheck">--</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">检测次数:</span>
                                <span class="info-value" id="checkCount">0</span>
                            </div>
                        </div>
                    </div>

                    <div id="anomalyResult" class="result-area"></div>
                </div>

                <!-- 趋势分析 -->
                <div id="trends" class="tab-content">
                    <div class="page-header">
                        <h2>📈 趋势分析</h2>
                        <p>实时分析数据的时间趋势和变化模式，支持预测和多维度分析</p>
                    </div>

                    <div class="analysis-form">
                        <!-- 基础配置 -->
                        <div class="form-section">
                            <h4>📊 基础配置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="trendTable">选择表</label>
                                    <select id="trendTable" class="form-control">
                                        <option value="payment">payment (支付表)</option>
                                        <option value="rental">rental (租赁表)</option>
                                        <option value="film">film (电影表)</option>
                                        <option value="customer">customer (客户表)</option>
                                        <option value="inventory">inventory (库存表)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="trendColumn">选择数值列</label>
                                    <select id="trendColumn" class="form-control">
                                        <option value="amount">amount (金额)</option>
                                        <option value="rental_rate">rental_rate (租赁费用)</option>
                                        <option value="length">length (时长)</option>
                                        <option value="replacement_cost">replacement_cost (替换成本)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="trendTimeColumn">时间列</label>
                                    <select id="trendTimeColumn" class="form-control">
                                        <option value="payment_date">payment_date (支付日期)</option>
                                        <option value="rental_date">rental_date (租赁日期)</option>
                                        <option value="created_at">created_at (创建时间)</option>
                                        <option value="last_update">last_update (最后更新)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 时间范围配置 -->
                        <div class="form-section">
                            <h4>⏰ 时间范围</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="trendStartTime">开始时间</label>
                                    <input type="datetime-local" id="trendStartTime" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="trendEndTime">结束时间</label>
                                    <input type="datetime-local" id="trendEndTime" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="trendPeriod">聚合周期</label>
                                    <select id="trendPeriod" class="form-control">
                                        <option value="hour">按小时</option>
                                        <option value="day" selected>按天</option>
                                        <option value="week">按周</option>
                                        <option value="month">按月</option>
                                        <option value="quarter">按季度</option>
                                        <option value="year">按年</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 趋势分析方法 -->
                        <div class="form-section">
                            <h4>📈 分析方法</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>趋势算法</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="trendLinear" checked> 线性趋势
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="trendMovingAvg" checked> 移动平均
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="trendExponential"> 指数平滑
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="trendSeasonal"> 季节性分析
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="trendSmoothingFactor">平滑因子</label>
                                    <select id="trendSmoothingFactor" class="form-control">
                                        <option value="0.1">0.1 (轻度平滑)</option>
                                        <option value="0.3" selected>0.3 (中度平滑)</option>
                                        <option value="0.5">0.5 (重度平滑)</option>
                                        <option value="0.7">0.7 (强平滑)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 预测配置 -->
                        <div class="form-section">
                            <h4>🔮 趋势预测</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="trendEnableForecast" checked> 启用趋势预测
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="trendForecastPeriods">预测周期数</label>
                                    <select id="trendForecastPeriods" class="form-control">
                                        <option value="3">3个周期</option>
                                        <option value="7" selected>7个周期</option>
                                        <option value="14">14个周期</option>
                                        <option value="30">30个周期</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="trendConfidenceLevel">置信度</label>
                                    <select id="trendConfidenceLevel" class="form-control">
                                        <option value="0.8">80%</option>
                                        <option value="0.9" selected>90%</option>
                                        <option value="0.95">95%</option>
                                        <option value="0.99">99%</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 多维度分析 -->
                        <div class="form-section">
                            <h4>📊 多维度分析</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="trendCompareColumn">对比列（可选）</label>
                                    <select id="trendCompareColumn" class="form-control">
                                        <option value="">不对比</option>
                                        <option value="amount">amount (金额)</option>
                                        <option value="rental_rate">rental_rate (租赁费用)</option>
                                        <option value="length">length (时长)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="trendGroupBy">分组维度（可选）</label>
                                    <select id="trendGroupBy" class="form-control">
                                        <option value="">不分组</option>
                                        <option value="category">category (分类)</option>
                                        <option value="rating">rating (评级)</option>
                                        <option value="staff_id">staff_id (员工ID)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 实时监控配置 -->
                        <div class="form-section">
                            <h4>🔄 实时监控</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="trendRealTime"> 启用实时趋势监控
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="trendRefreshInterval">刷新间隔</label>
                                    <select id="trendRefreshInterval" class="form-control">
                                        <option value="10">10秒</option>
                                        <option value="30">30秒</option>
                                        <option value="60" selected>1分钟</option>
                                        <option value="300">5分钟</option>
                                        <option value="900">15分钟</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 趋势告警配置 -->
                        <div class="form-section">
                            <h4>🚨 趋势告警</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="trendEnableAlert"> 启用趋势告警
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="trendAlertType">告警类型</label>
                                    <select id="trendAlertType" class="form-control">
                                        <option value="direction_change">趋势方向变化</option>
                                        <option value="slope_threshold">斜率阈值</option>
                                        <option value="value_threshold">数值阈值</option>
                                        <option value="volatility">波动性异常</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="trendAlertThreshold">告警阈值</label>
                                    <input type="number" id="trendAlertThreshold" class="form-control" placeholder="输入阈值" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- 语音控制 -->
                        <div class="form-section">
                            <h4>🎤 语音控制</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <button id="trendVoiceControl" class="btn btn-voice">
                                        🎤 语音设置参数
                                    </button>
                                    <div id="trendVoiceStatus" class="voice-status">点击开始语音控制</div>
                                </div>
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="trendVoiceReport" checked> 语音播报趋势
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-actions">
                            <button id="runTrendAnalysis" class="btn btn-primary">
                                📈 分析趋势
                            </button>
                            <button id="exportTrendData" class="btn btn-secondary">
                                📁 导出数据
                            </button>
                            <button id="clearTrendResult" class="btn btn-outline">
                                🗑️ 清空结果
                            </button>
                        </div>
                    </div>

                    <!-- 趋势分析结果显示区域 -->
                    <div id="trendResult" class="result-area">
                        <div class="result-header">
                            <h3>📈 趋势分析结果</h3>
                            <div class="result-controls">
                                <button id="trendToggleView" class="btn btn-sm">📊 切换视图</button>
                                <button id="trendRefreshNow" class="btn btn-sm">🔄 立即刷新</button>
                                <button id="trendFullscreen" class="btn btn-sm">🔍 全屏查看</button>
                            </div>
                        </div>
                        <div id="trendContent" class="result-content">
                            <div class="empty-state">
                                <div class="empty-icon">📈</div>
                                <div class="empty-text">请配置参数并运行趋势分析</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表生成 -->
                <div id="charts" class="tab-content">
                    <div class="page-header">
                        <h2>📊 图表生成</h2>
                        <p>创建各种类型的数据可视化图表</p>
                    </div>
                    
                    <div class="chart-controls">
                        <div class="chart-type-selector">
                            <button class="chart-type-btn active" data-type="bar">📊 柱状图</button>
                            <button class="chart-type-btn" data-type="pie">🥧 饼状图</button>
                            <button class="chart-type-btn" data-type="line">📈 趋势图</button>
                            <button class="chart-type-btn" data-type="scatter">🔵 散点图</button>
                            <button class="chart-type-btn" data-type="heatmap">🔥 热力图</button>
                        </div>

                        <div class="chart-theme-selector">
                            <label>主题:</label>
                            <select id="chartTheme" class="form-control">
                                <option value="light">明亮主题</option>
                                <option value="dark">深色主题</option>
                                <option value="macarons">马卡龙主题</option>
                            </select>
                        </div>

                        <div class="chart-actions">
                            <button id="exportChart" class="btn btn-secondary">📥 导出图表</button>
                            <button id="fullscreenChart" class="btn btn-secondary">🔍 全屏显示</button>
                        </div>
                    </div>

                        <div class="chart-form">
                            <div id="barChartForm" class="chart-form-section active">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>数据表</label>
                                        <select id="barTable" class="form-control">
                                            <option value="film">film (电影表)</option>
                                            <option value="payment">payment (支付表)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>X轴列</label>
                                        <select id="barXColumn" class="form-control">
                                            <option value="rating">rating (评级)</option>
                                            <option value="category">category (类别)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Y轴列</label>
                                        <select id="barYColumn" class="form-control">
                                            <option value="rental_rate">rental_rate (租赁费用)</option>
                                            <option value="amount">amount (金额)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>图表标题</label>
                                        <input type="text" id="barTitle" class="form-control" value="电影评级与租赁费用分析">
                                    </div>
                                    <div class="form-group">
                                        <label>数据限制</label>
                                        <input type="number" id="barLimit" class="form-control" value="10" min="5" max="50">
                                    </div>
                                </div>
                                <button id="generateBarChart" class="btn btn-primary">生成柱状图</button>
                            </div>

                            <div id="pieChartForm" class="chart-form-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>数据表</label>
                                        <select id="pieTable" class="form-control">
                                            <option value="film">film (电影表)</option>
                                            <option value="payment">payment (支付表)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>标签列</label>
                                        <select id="pieLabelColumn" class="form-control">
                                            <option value="rating">rating (评级)</option>
                                            <option value="category">category (类别)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>数值列</label>
                                        <select id="pieValueColumn" class="form-control">
                                            <option value="rental_rate">rental_rate (租赁费用)</option>
                                            <option value="amount">amount (金额)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>图表标题</label>
                                        <input type="text" id="pieTitle" class="form-control" value="电影评级分布">
                                    </div>
                                </div>
                                <button id="generatePieChart" class="btn btn-primary">生成饼状图</button>
                            </div>

                            <div id="lineChartForm" class="chart-form-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>数据表</label>
                                        <select id="lineTable" class="form-control">
                                            <option value="payment">payment (支付表)</option>
                                            <option value="rental">rental (租赁表)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>时间列</label>
                                        <select id="lineXColumn" class="form-control">
                                            <option value="payment_date">payment_date (支付日期)</option>
                                            <option value="rental_date">rental_date (租赁日期)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>数值列</label>
                                        <select id="lineYColumn" class="form-control">
                                            <option value="amount">amount (金额)</option>
                                            <option value="rental_rate">rental_rate (租赁费用)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>图表标题</label>
                                        <input type="text" id="lineTitle" class="form-control" value="支付金额趋势分析">
                                    </div>
                                    <div class="form-group">
                                        <label>时间范围</label>
                                        <select id="lineTimeRange" class="form-control">
                                            <option value="">全部数据</option>
                                            <option value="7 DAY">最近7天</option>
                                            <option value="30 DAY">最近30天</option>
                                            <option value="90 DAY">最近90天</option>
                                        </select>
                                    </div>
                                </div>
                                <button id="generateLineChart" class="btn btn-primary">生成趋势图</button>
                            </div>

                            <div id="scatterChartForm" class="chart-form-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>数据表</label>
                                        <select id="scatterTable" class="form-control">
                                            <option value="film">film (电影表)</option>
                                            <option value="payment">payment (支付表)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>X轴列</label>
                                        <select id="scatterXColumn" class="form-control">
                                            <option value="rental_rate">rental_rate (租赁费用)</option>
                                            <option value="length">length (时长)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Y轴列</label>
                                        <select id="scatterYColumn" class="form-control">
                                            <option value="replacement_cost">replacement_cost (替换成本)</option>
                                            <option value="amount">amount (金额)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>图表标题</label>
                                        <input type="text" id="scatterTitle" class="form-control" value="租赁费用与替换成本关系分析">
                                    </div>
                                </div>
                                <button id="generateScatterChart" class="btn btn-primary">生成散点图</button>
                            </div>

                            <div id="heatmapChartForm" class="chart-form-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>数据表</label>
                                        <select id="heatmapTable" class="form-control">
                                            <option value="payment">payment (支付表)</option>
                                            <option value="rental">rental (租赁表)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>X轴维度</label>
                                        <select id="heatmapXColumn" class="form-control">
                                            <option value="HOUR(payment_date)">支付小时</option>
                                            <option value="DAYOFWEEK(payment_date)">支付星期</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Y轴维度</label>
                                        <select id="heatmapYColumn" class="form-control">
                                            <option value="DAYOFMONTH(payment_date)">支付日期</option>
                                            <option value="MONTH(payment_date)">支付月份</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>图表标题</label>
                                        <input type="text" id="heatmapTitle" class="form-control" value="支付时间热力图分析">
                                    </div>
                                </div>
                                <button id="generateHeatmapChart" class="btn btn-primary">生成热力图</button>
                            </div>
                        </div>
                    </div>

                    <div id="chartResult" class="chart-display-area">
                        <div class="chart-container-wrapper">
                            <div id="enterpriseChart" class="enterprise-chart-container"></div>
                            <div class="chart-info-panel">
                                <div class="chart-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">数据点数:</span>
                                        <span class="stat-value" id="chartDataPoints">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">图表类型:</span>
                                        <span class="stat-value" id="chartType">-</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">生成时间:</span>
                                        <span class="stat-value" id="chartTime">-</span>
                                    </div>
                                </div>
                                <div class="chart-insights">
                                    <h4>📊 数据洞察</h4>
                                    <div id="chartInsights" class="insights-content">
                                        <p>生成图表后将显示数据洞察...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表库 -->
                <div id="gallery" class="tab-content">
                    <div class="page-header">
                        <h2>🖼️ 图表库</h2>
                        <p>查看和管理已生成的图表</p>
                    </div>

                    <div class="gallery-controls">
                        <button id="refreshGallery" class="btn btn-primary">刷新图表库</button>
                        <button id="clearGallery" class="btn btn-secondary">清空图表库</button>
                    </div>

                    <div id="galleryContent" class="gallery-grid">
                        <div class="empty-state">
                            <span class="empty-icon">🖼️</span>
                            <p>暂无图表，请先生成一些图表</p>
                        </div>
                    </div>
                </div>

                <!-- SQL查询 -->
                <div id="sql" class="tab-content">
                    <div class="page-header">
                        <h2>💻 SQL查询</h2>
                        <p>执行自定义SQL查询</p>
                    </div>
                    
                    <div class="sql-editor">
                        <div class="editor-header">
                            <span>SQL编辑器</span>
                            <div class="editor-actions">
                                <button id="formatSql" class="btn btn-sm">格式化</button>
                                <button id="clearSql" class="btn btn-sm">清空</button>
                                <button id="executeSql" class="btn btn-primary">执行查询</button>
                            </div>
                        </div>
                        <textarea id="sqlEditor" class="sql-textarea" placeholder="输入您的SQL查询...
示例:
SELECT rating, COUNT(*) as count, AVG(rental_rate) as avg_rate 
FROM film 
GROUP BY rating 
ORDER BY count DESC;"></textarea>
                    </div>

                    <div id="sqlResult" class="result-area"></div>
                </div>

                <!-- 提醒管理 -->
                <div id="alerts" class="tab-content">
                    <div class="page-header">
                        <h2>🔔 提醒管理</h2>
                        <p>设置和管理数据提醒规则</p>
                    </div>

                    <div class="alert-form">
                        <h3>创建新提醒</h3>
                        <div class="form-group">
                            <label for="alertName">提醒名称</label>
                            <input type="text" id="alertName" class="form-control" placeholder="例如：支付金额异常提醒">
                        </div>
                        <div class="form-group">
                            <label for="alertTable">监控表</label>
                            <select id="alertTable" class="form-control">
                                <option value="payment">payment (支付表)</option>
                                <option value="rental">rental (租赁表)</option>
                                <option value="film">film (电影表)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="alertColumn">监控列</label>
                            <select id="alertColumn" class="form-control">
                                <option value="amount">amount (金额)</option>
                                <option value="rental_rate">rental_rate (租赁费用)</option>
                                <option value="length">length (时长)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="alertCondition">触发条件</label>
                            <select id="alertCondition" class="form-control">
                                <option value="greater_than">大于</option>
                                <option value="less_than">小于</option>
                                <option value="equals">等于</option>
                                <option value="anomaly">异常检测</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="alertValue">阈值</label>
                            <input type="number" id="alertValue" class="form-control" placeholder="输入阈值">
                        </div>
                        <button id="createAlert" class="btn btn-primary">创建提醒</button>
                    </div>

                    <div class="alert-list">
                        <h3>现有提醒</h3>
                        <div id="alertsList">
                            <div class="empty-state">
                                <span class="empty-icon">🔔</span>
                                <p>暂无提醒规则，请创建新的提醒</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 语音助手 -->
                <div id="voice" class="tab-content">
                    <div class="page-header">
                        <h2>🎤 语音助手</h2>
                        <p>使用语音与系统交互</p>
                    </div>
                    
                    <div class="voice-interface">
                        <div class="voice-controls">
                            <button id="startVoice" class="btn btn-voice">
                                <span class="voice-icon">🎤</span>
                                开始语音输入
                            </button>
                            <button id="stopVoice" class="btn btn-voice" disabled>
                                <span class="voice-icon">⏹️</span>
                                停止录音
                            </button>
                        </div>
                        
                        <div class="voice-output">
                            <div class="voice-text">
                                <h4>语音识别结果：</h4>
                                <div id="voiceText" class="voice-result">等待语音输入...</div>
                            </div>
                            <div class="voice-response">
                                <h4>系统响应：</h4>
                                <div id="voiceResponse" class="voice-result">系统将在这里显示响应...</div>
                            </div>
                        </div>

                        <div class="voice-commands">
                            <h4>常用语音命令：</h4>
                            <ul>
                                <li>"生成支付数据的柱状图"</li>
                                <li>"检测异常数据"</li>
                                <li>"显示数据库信息"</li>
                                <li>"分析最近的趋势"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 加载遮罩 -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>处理中...</p>
            </div>
        </div>

        <!-- 通知系统 -->
        <div id="notifications" class="notifications"></div>
    </div>

    <script>
        // 调试信息
        console.log('页面加载时间:', new Date().toISOString());
        console.log('当前URL:', window.location.href);

        // 测试fetch功能
        window.testConnection = async function() {
            console.log('开始测试连接...');
            try {
                const response = await fetch('http://127.0.0.1:8080/health');
                console.log('响应状态:', response.status);
                const data = await response.json();
                console.log('响应数据:', data);
                alert('连接测试成功! 状态: ' + data.status);
            } catch (error) {
                console.error('连接测试失败:', error);
                alert('连接测试失败: ' + error.message);
            }
        };
    </script>
    <script src="enterprise-charts.js"></script>
    <script src="app.js?v=20250724-enterprise-charts"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化企业级图表生成器
            window.enterpriseCharts = new EnterpriseChartGenerator();

            // 初始化应用
            window.app = new MySQLAnalysisClient();

            // 确保字体完整性
            setTimeout(() => {
                if (window.app && typeof window.app.ensurePageFontIntegrity === 'function') {
                    window.app.ensurePageFontIntegrity();
                }
            }, 500);
        });
    </script>
</body>
</html>
