# FastMCP 文档 - 第 14 部分
# 主要内容: Timing Middleware
# 包含段落: 111 个
# 总行数: 1031

================================================================================

## Timing Middleware
类型: docs, 行数: 33

### Timing Middleware

Performance monitoring is essential for understanding your server's behavior and identifying bottlenecks. FastMCP includes timing middleware at `fastmcp.server.middleware.timing`.

Here's an example of how it works:

```python
import time
from fastmcp.server.middleware import Middleware, MiddlewareContext

class SimpleTimingMiddleware(Middleware):
    async def on_request(self, context: MiddlewareContext, call_next):
        start_time = time.perf_counter()
        
        try:
            result = await call_next(context)
            duration_ms = (time.perf_counter() - start_time) * 1000
            print(f"Request {context.method} completed in {duration_ms:.2f}ms")
            return result
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            print(f"Request {context.method} failed after {duration_ms:.2f}ms: {e}")
            raise
```

To use the full version with proper logging and configuration:

```python
from fastmcp.server.middleware.timing import (
    TimingMiddleware, 
    DetailedTimingMiddleware
)


------------------------------------------------------------

## Basic timing for all requests
类型: docs, 行数: 3

# Basic timing for all requests
mcp.add_middleware(TimingMiddleware())


------------------------------------------------------------

## Detailed per-operation timing (tools, resources, prompts)
类型: docs, 行数: 6

# Detailed per-operation timing (tools, resources, prompts)
mcp.add_middleware(DetailedTimingMiddleware())
```

The built-in versions include custom logger support, proper formatting, and **DetailedTimingMiddleware** provides operation-specific hooks like `on_call_tool` and `on_read_resource` for granular timing.


------------------------------------------------------------

## Logging Middleware
类型: docs, 行数: 30

### Logging Middleware

Request and response logging is crucial for debugging, monitoring, and understanding usage patterns in your MCP server. FastMCP provides comprehensive logging middleware at `fastmcp.server.middleware.logging`.

Here's an example of how it works:

```python
from fastmcp.server.middleware import Middleware, MiddlewareContext

class SimpleLoggingMiddleware(Middleware):
    async def on_message(self, context: MiddlewareContext, call_next):
        print(f"Processing {context.method} from {context.source}")
        
        try:
            result = await call_next(context)
            print(f"Completed {context.method}")
            return result
        except Exception as e:
            print(f"Failed {context.method}: {e}")
            raise
```

To use the full versions with advanced features:

```python
from fastmcp.server.middleware.logging import (
    LoggingMiddleware, 
    StructuredLoggingMiddleware
)


------------------------------------------------------------

## Human-readable logging with payload support
类型: docs, 行数: 6

# Human-readable logging with payload support
mcp.add_middleware(LoggingMiddleware(
    include_payloads=True,
    max_payload_length=1000
))


------------------------------------------------------------

## JSON-structured logging for log aggregation tools
类型: docs, 行数: 6

# JSON-structured logging for log aggregation tools
mcp.add_middleware(StructuredLoggingMiddleware(include_payloads=True))
```

The built-in versions include payload logging, structured JSON output, custom logger support, payload size limits, and operation-specific hooks for granular control.


------------------------------------------------------------

## Rate Limiting Middleware
类型: docs, 行数: 22

### Rate Limiting Middleware

Rate limiting is essential for protecting your server from abuse, ensuring fair resource usage, and maintaining performance under load. FastMCP includes sophisticated rate limiting middleware at `fastmcp.server.middleware.rate_limiting`.

Here's an example of how it works:

```python
import time
from collections import defaultdict
from fastmcp.server.middleware import Middleware, MiddlewareContext
from mcp import McpError
from mcp.types import ErrorData

class SimpleRateLimitMiddleware(Middleware):
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.client_requests = defaultdict(list)
    
    async def on_request(self, context: MiddlewareContext, call_next):
        current_time = time.time()
        client_id = "default"  # In practice, extract from headers or context
        

------------------------------------------------------------

## Clean old requests and check limit
类型: docs, 行数: 22

        # Clean old requests and check limit
        cutoff_time = current_time - 60
        self.client_requests[client_id] = [
            req_time for req_time in self.client_requests[client_id]
            if req_time > cutoff_time
        ]
        
        if len(self.client_requests[client_id]) >= self.requests_per_minute:
            raise McpError(ErrorData(code=-32000, message="Rate limit exceeded"))
        
        self.client_requests[client_id].append(current_time)
        return await call_next(context)
```

To use the full versions with advanced algorithms:

```python
from fastmcp.server.middleware.rate_limiting import (
    RateLimitingMiddleware, 
    SlidingWindowRateLimitingMiddleware
)


------------------------------------------------------------

## Token bucket rate limiting (allows controlled bursts)
类型: docs, 行数: 6

# Token bucket rate limiting (allows controlled bursts)
mcp.add_middleware(RateLimitingMiddleware(
    max_requests_per_second=10.0,
    burst_capacity=20
))


------------------------------------------------------------

## Sliding window rate limiting (precise time-based control)
类型: docs, 行数: 9

# Sliding window rate limiting (precise time-based control)
mcp.add_middleware(SlidingWindowRateLimitingMiddleware(
    max_requests=100,
    window_minutes=1
))
```

The built-in versions include token bucket algorithms, per-client identification, global rate limiting, and async-safe implementations with configurable client identification functions.


------------------------------------------------------------

## Error Handling Middleware
类型: docs, 行数: 19

### Error Handling Middleware

Consistent error handling and recovery is critical for robust MCP servers. FastMCP provides comprehensive error handling middleware at `fastmcp.server.middleware.error_handling`.

Here's an example of how it works:

```python
import logging
from fastmcp.server.middleware import Middleware, MiddlewareContext

class SimpleErrorHandlingMiddleware(Middleware):
    def __init__(self):
        self.logger = logging.getLogger("errors")
        self.error_counts = {}
    
    async def on_message(self, context: MiddlewareContext, call_next):
        try:
            return await call_next(context)
        except Exception as error:

------------------------------------------------------------

## Log the error and track statistics
类型: docs, 行数: 16

            # Log the error and track statistics
            error_key = f"{type(error).__name__}:{context.method}"
            self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
            
            self.logger.error(f"Error in {context.method}: {type(error).__name__}: {error}")
            raise
```

To use the full versions with advanced features:

```python
from fastmcp.server.middleware.error_handling import (
    ErrorHandlingMiddleware, 
    RetryMiddleware
)


------------------------------------------------------------

## Comprehensive error logging and transformation
类型: docs, 行数: 7

# Comprehensive error logging and transformation
mcp.add_middleware(ErrorHandlingMiddleware(
    include_traceback=True,
    transform_errors=True,
    error_callback=my_error_callback
))


------------------------------------------------------------

## Automatic retry with exponential backoff
类型: docs, 行数: 9

# Automatic retry with exponential backoff
mcp.add_middleware(RetryMiddleware(
    max_retries=3,
    retry_exceptions=(ConnectionError, TimeoutError)
))
```

The built-in versions include error transformation, custom callbacks, configurable retry logic, and proper MCP error formatting.


------------------------------------------------------------

## Combining Middleware
类型: docs, 行数: 13

### Combining Middleware

These middleware work together seamlessly:

```python
from fastmcp import FastMCP
from fastmcp.server.middleware.timing import TimingMiddleware
from fastmcp.server.middleware.logging import LoggingMiddleware
from fastmcp.server.middleware.rate_limiting import RateLimitingMiddleware
from fastmcp.server.middleware.error_handling import ErrorHandlingMiddleware

mcp = FastMCP("Production Server")


------------------------------------------------------------

## Add middleware in logical order
类型: docs, 行数: 13

# Add middleware in logical order
mcp.add_middleware(ErrorHandlingMiddleware())  # Handle errors first
mcp.add_middleware(RateLimitingMiddleware(max_requests_per_second=50))
mcp.add_middleware(TimingMiddleware())  # Time actual execution
mcp.add_middleware(LoggingMiddleware())  # Log everything

@mcp.tool
def my_tool(data: str) -> str:
    return f"Processed: {data}"
```

This configuration provides comprehensive monitoring, protection, and observability for your MCP server.


------------------------------------------------------------

## Custom Middleware Example
类型: tutorial, 行数: 9

### Custom Middleware Example

You can also create custom middleware by extending the base class:

```python
from fastmcp.server.middleware import Middleware, MiddlewareContext

class CustomHeaderMiddleware(Middleware):
    async def on_request(self, context: MiddlewareContext, call_next):

------------------------------------------------------------

## Add custom logic here
类型: docs, 行数: 12

        # Add custom logic here
        print(f"Processing {context.method}")
        
        result = await call_next(context)
        
        print(f"Completed {context.method}")
        return result

mcp.add_middleware(CustomHeaderMiddleware())
```



------------------------------------------------------------

## Progress Reporting
类型: docs, 行数: 16

# Progress Reporting
Source: https://gofastmcp.com/servers/progress

Update clients on the progress of long-running operations through the MCP context.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

Progress reporting allows MCP tools to notify clients about the progress of long-running operations. This enables clients to display progress indicators and provide better user experience during time-consuming tasks.


------------------------------------------------------------

## Why Use Progress Reporting?
类型: docs, 行数: 9

## Why Use Progress Reporting?

Progress reporting is valuable for:

* **User experience**: Keep users informed about long-running operations
* **Progress indicators**: Enable clients to show progress bars or percentages
* **Timeout prevention**: Demonstrate that operations are actively progressing
* **Debugging**: Track execution progress for performance analysis


------------------------------------------------------------

## Basic Usage
类型: docs, 行数: 17

### Basic Usage

Use `ctx.report_progress()` to send progress updates to the client:

```python {14, 21}
from fastmcp import FastMCP, Context
import asyncio

mcp = FastMCP("ProgressDemo")

@mcp.tool
async def process_items(items: list[str], ctx: Context) -> dict:
    """Process a list of items with progress updates."""
    total = len(items)
    results = []
    
    for i, item in enumerate(items):

------------------------------------------------------------

## Report progress as we process each item
类型: docs, 行数: 3

        # Report progress as we process each item
        await ctx.report_progress(progress=i, total=total)
        

------------------------------------------------------------

## Simulate processing time
类型: docs, 行数: 4

        # Simulate processing time
        await asyncio.sleep(0.1)
        results.append(item.upper())
    

------------------------------------------------------------

## Report 100% completion
类型: docs, 行数: 6

    # Report 100% completion
    await ctx.report_progress(progress=total, total=total)
    
    return {"processed": len(results), "results": results}
```


------------------------------------------------------------

## Method Signature
类型: api, 行数: 18

## Method Signature

<Card icon="code" title="Context Progress Method">
  <ResponseField name="ctx.report_progress" type="async method">
    Report progress to the client for long-running operations

    <Expandable title="Parameters">
      <ResponseField name="progress" type="float">
        Current progress value (e.g., 24, 0.75, 1500)
      </ResponseField>

      <ResponseField name="total" type="float | None" default="None">
        Optional total value (e.g., 100, 1.0, 2000). When provided, clients may interpret this as enabling percentage calculation.
      </ResponseField>
    </Expandable>
  </ResponseField>
</Card>


------------------------------------------------------------

## Progress Patterns
类型: docs, 行数: 2

## Progress Patterns


------------------------------------------------------------

## Percentage-Based Progress
类型: docs, 行数: 12

### Percentage-Based Progress

Report progress as a percentage (0-100):

```python {13-14}
@mcp.tool
async def download_file(url: str, ctx: Context) -> str:
    """Download a file with percentage progress."""
    total_size = 1000  # KB
    downloaded = 0
    
    while downloaded < total_size:

------------------------------------------------------------

## Download chunk
类型: docs, 行数: 4

        # Download chunk
        chunk_size = min(50, total_size - downloaded)
        downloaded += chunk_size
        

------------------------------------------------------------

## Report percentage progress
类型: docs, 行数: 9

        # Report percentage progress
        percentage = (downloaded / total_size) * 100
        await ctx.report_progress(progress=percentage, total=100)
        
        await asyncio.sleep(0.1)  # Simulate download time
    
    return f"Downloaded file from {url}"
```


------------------------------------------------------------

## Absolute Progress
类型: docs, 行数: 13

### Absolute Progress

Report progress with absolute values:

```python {10}
@mcp.tool
async def backup_database(ctx: Context) -> str:
    """Backup database tables with absolute progress."""
    tables = ["users", "orders", "products", "inventory", "logs"]
    
    for i, table in enumerate(tables):
        await ctx.info(f"Backing up table: {table}")
        

------------------------------------------------------------

## Report absolute progress
类型: docs, 行数: 3

        # Report absolute progress
        await ctx.report_progress(progress=i + 1, total=len(tables))
        

------------------------------------------------------------

## Simulate backup time
类型: docs, 行数: 6

        # Simulate backup time
        await asyncio.sleep(0.5)
    
    return "Database backup completed"
```


------------------------------------------------------------

## Indeterminate Progress
类型: docs, 行数: 10

### Indeterminate Progress

Report progress without a known total for operations where the endpoint is unknown:

```python {11}
@mcp.tool
async def scan_directory(directory: str, ctx: Context) -> dict:
    """Scan directory with indeterminate progress."""
    files_found = 0
    

------------------------------------------------------------

## Simulate directory scanning
类型: docs, 行数: 4

    # Simulate directory scanning
    for i in range(10):  # Unknown number of files
        files_found += 1
        

------------------------------------------------------------

## Report progress without total for indeterminate operations
类型: docs, 行数: 8

        # Report progress without total for indeterminate operations
        await ctx.report_progress(progress=files_found)
        
        await asyncio.sleep(0.2)
    
    return {"files_found": files_found, "directory": directory}
```


------------------------------------------------------------

## Multi-Stage Operations
类型: docs, 行数: 9

### Multi-Stage Operations

Break complex operations into stages with progress for each:

```python
@mcp.tool
async def data_migration(source: str, destination: str, ctx: Context) -> str:
    """Migrate data with multi-stage progress reporting."""
    

------------------------------------------------------------

## Stage 1: Validation (0-25%)
类型: docs, 行数: 6

    # Stage 1: Validation (0-25%)
    await ctx.info("Validating source data")
    for i in range(5):
        await ctx.report_progress(progress=i * 5, total=100)
        await asyncio.sleep(0.1)
    

------------------------------------------------------------

## Stage 2: Export (25-60%)
类型: docs, 行数: 7

    # Stage 2: Export (25-60%)
    await ctx.info("Exporting data from source")
    for i in range(7):
        progress = 25 + (i * 5)
        await ctx.report_progress(progress=progress, total=100)
        await asyncio.sleep(0.1)
    

------------------------------------------------------------

## Stage 3: Transform (60-80%)
类型: docs, 行数: 7

    # Stage 3: Transform (60-80%)
    await ctx.info("Transforming data format")
    for i in range(4):
        progress = 60 + (i * 5)
        await ctx.report_progress(progress=progress, total=100)
        await asyncio.sleep(0.1)
    

------------------------------------------------------------

## Stage 4: Import (80-100%)
类型: docs, 行数: 7

    # Stage 4: Import (80-100%)
    await ctx.info("Importing to destination")
    for i in range(4):
        progress = 80 + (i * 5)
        await ctx.report_progress(progress=progress, total=100)
        await asyncio.sleep(0.1)
    

------------------------------------------------------------

## Final completion
类型: docs, 行数: 6

    # Final completion
    await ctx.report_progress(progress=100, total=100)
    
    return f"Migration from {source} to {destination} completed"
```


------------------------------------------------------------

## Client Requirements
类型: docs, 行数: 9

## Client Requirements

Progress reporting requires clients to support progress handling:

* Clients must send a `progressToken` in the initial request to receive progress updates
* If no progress token is provided, progress calls will have no effect (they won't error)
* See [Client Progress](/clients/progress) for details on implementing client-side progress handling



------------------------------------------------------------

## Prompts
类型: docs, 行数: 16

# Prompts
Source: https://gofastmcp.com/servers/prompts

Create reusable, parameterized prompt templates for MCP clients.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

Prompts are reusable message templates that help LLMs generate structured, purposeful responses. FastMCP simplifies defining these templates, primarily using the `@mcp.prompt` decorator.


------------------------------------------------------------

## What Are Prompts?
类型: docs, 行数: 11

## What Are Prompts?

Prompts provide parameterized message templates for LLMs. When a client requests a prompt:

1. FastMCP finds the corresponding prompt definition.
2. If it has parameters, they are validated against your function signature.
3. Your function executes with the validated inputs.
4. The generated message(s) are returned to the LLM to guide its response.

This allows you to define consistent, reusable templates that LLMs can use across different clients and contexts.


------------------------------------------------------------

## Prompts
类型: docs, 行数: 2

## Prompts


------------------------------------------------------------

## The `@prompt` Decorator
类型: docs, 行数: 10

### The `@prompt` Decorator

The most common way to define a prompt is by decorating a Python function. The decorator uses the function name as the prompt's identifier.

```python
from fastmcp import FastMCP
from fastmcp.prompts.prompt import Message, PromptMessage, TextContent

mcp = FastMCP(name="PromptServer")


------------------------------------------------------------

## Basic prompt returning a string (converted to user message automatically)
类型: docs, 行数: 6

# Basic prompt returning a string (converted to user message automatically)
@mcp.prompt
def ask_about_topic(topic: str) -> str:
    """Generates a user message asking for an explanation of a topic."""
    return f"Can you please explain the concept of '{topic}'?"


------------------------------------------------------------

## Prompt returning a specific message type
类型: docs, 行数: 20

# Prompt returning a specific message type
@mcp.prompt
def generate_code_request(language: str, task_description: str) -> PromptMessage:
    """Generates a user message requesting code generation."""
    content = f"Write a {language} function that performs the following task: {task_description}"
    return PromptMessage(role="user", content=TextContent(type="text", text=content))
```

**Key Concepts:**

* **Name:** By default, the prompt name is taken from the function name.
* **Parameters:** The function parameters define the inputs needed to generate the prompt.
* **Inferred Metadata:** By default:
  * Prompt Name: Taken from the function name (`ask_about_topic`).
  * Prompt Description: Taken from the function's docstring.

<Tip>
  Functions with `*args` or `**kwargs` are not supported as prompts. This restriction exists because FastMCP needs to generate a complete parameter schema for the MCP protocol, which isn't possible with variable argument lists.
</Tip>


------------------------------------------------------------

## Decorator Arguments
类型: docs, 行数: 36

#### Decorator Arguments

While FastMCP infers the name and description from your function, you can override these and add additional metadata using arguments to the `@mcp.prompt` decorator:

```python
@mcp.prompt(
    name="analyze_data_request",          # Custom prompt name
    description="Creates a request to analyze data with specific parameters",  # Custom description
    tags={"analysis", "data"}             # Optional categorization tags
)
def data_analysis_prompt(
    data_uri: str = Field(description="The URI of the resource containing the data."),
    analysis_type: str = Field(default="summary", description="Type of analysis.")
) -> str:
    """This docstring is ignored when description is provided."""
    return f"Please perform a '{analysis_type}' analysis on the data found at {data_uri}."
```

<Card icon="code" title="@prompt Decorator Arguments">
  <ParamField body="name" type="str | None">
    Sets the explicit prompt name exposed via MCP. If not provided, uses the function name
  </ParamField>

  <ParamField body="description" type="str | None">
    Provides the description exposed via MCP. If set, the function's docstring is ignored for this purpose
  </ParamField>

  <ParamField body="tags" type="set[str] | None">
    A set of strings used to categorize the prompt. Clients might use tags to filter or group available prompts
  </ParamField>

  <ParamField body="enabled" type="bool" default="True">
    A boolean to enable or disable the prompt. See [Disabling Prompts](#disabling-prompts) for more information
  </ParamField>
</Card>


------------------------------------------------------------

## Argument Types
类型: docs, 行数: 63

### Argument Types

<VersionBadge version="2.9.0" />

The MCP specification requires that all prompt arguments be passed as strings, but FastMCP allows you to use typed annotations for better developer experience. When you use complex types like `list[int]` or `dict[str, str]`, FastMCP:

1. **Automatically converts** string arguments from MCP clients to the expected types
2. **Generates helpful descriptions** showing the exact JSON string format needed
3. **Preserves direct usage** - you can still call prompts with properly typed arguments

Since the MCP specification only allows string arguments, clients need to know what string format to use for complex types. FastMCP solves this by automatically enhancing the argument descriptions with JSON schema information, making it clear to both humans and LLMs how to format their arguments.

<CodeGroup>
  ```python Python Code
  @mcp.prompt
  def analyze_data(
      numbers: list[int],
      metadata: dict[str, str], 
      threshold: float
  ) -> str:
      """Analyze numerical data."""
      avg = sum(numbers) / len(numbers)
      return f"Average: {avg}, above threshold: {avg > threshold}"
  ```

  ```json Resulting MCP Prompt
  {
    "name": "analyze_data",
    "description": "Analyze numerical data.",
    "arguments": [
      {
        "name": "numbers",
        "description": "Provide as a JSON string matching the following schema: {\"items\":{\"type\":\"integer\"},\"type\":\"array\"}",
        "required": true
      },
      {
        "name": "metadata", 
        "description": "Provide as a JSON string matching the following schema: {\"additionalProperties\":{\"type\":\"string\"},\"type\":\"object\"}",
        "required": true
      },
      {
        "name": "threshold",
        "description": "Provide as a JSON string matching the following schema: {\"type\":\"number\"}",
        "required": true
      }
    ]
  }
  ```
</CodeGroup>

**MCP clients will call this prompt with string arguments:**

```json
{
  "numbers": "[1, 2, 3, 4, 5]",
  "metadata": "{\"source\": \"api\", \"version\": \"1.0\"}",
  "threshold": "2.5"
}
```

**But you can still call it directly with proper types:**

```python

------------------------------------------------------------

## This also works for direct calls
类型: docs, 行数: 15

# This also works for direct calls
result = await prompt.render({
    "numbers": [1, 2, 3, 4, 5],
    "metadata": {"source": "api", "version": "1.0"}, 
    "threshold": 2.5
})
```

<Warning>
  Keep your type annotations simple when using this feature. Complex nested types or custom classes may not convert reliably from JSON strings. The automatically generated schema descriptions are the only guidance users receive about the expected format.

  Good choices: `list[int]`, `dict[str, str]`, `float`, `bool`
  Avoid: Complex Pydantic models, deeply nested structures, custom classes
</Warning>


------------------------------------------------------------

## Return Values
类型: docs, 行数: 21

### Return Values

FastMCP intelligently handles different return types from your prompt function:

* **`str`**: Automatically converted to a single `PromptMessage`.
* **`PromptMessage`**: Used directly as provided. (Note a more user-friendly `Message` constructor is available that can accept raw strings instead of `TextContent` objects.)
* **`list[PromptMessage | str]`**: Used as a sequence of messages (a conversation).
* **`Any`**: If the return type is not one of the above, the return value is attempted to be converted to a string and used as a `PromptMessage`.

```python
from fastmcp.prompts.prompt import Message

@mcp.prompt
def roleplay_scenario(character: str, situation: str) -> list[Message]:
    """Sets up a roleplaying scenario with initial messages."""
    return [
        Message(f"Let's roleplay. You are {character}. The situation is: {situation}"),
        Message("Okay, I understand. I am ready. What happens next?", role="assistant")
    ]
```


------------------------------------------------------------

## Required vs. Optional Parameters
类型: docs, 行数: 20

### Required vs. Optional Parameters

Parameters in your function signature are considered **required** unless they have a default value.

```python
@mcp.prompt
def data_analysis_prompt(
    data_uri: str,                        # Required - no default value
    analysis_type: str = "summary",       # Optional - has default value
    include_charts: bool = False          # Optional - has default value
) -> str:
    """Creates a request to analyze data with specific parameters."""
    prompt = f"Please perform a '{analysis_type}' analysis on the data found at {data_uri}."
    if include_charts:
        prompt += " Include relevant charts and visualizations."
    return prompt
```

In this example, the client *must* provide `data_uri`. If `analysis_type` or `include_charts` are omitted, their default values will be used.


------------------------------------------------------------

## Disabling Prompts
类型: docs, 行数: 21

### Disabling Prompts

<VersionBadge version="2.8.0" />

You can control the visibility and availability of prompts by enabling or disabling them. Disabled prompts will not appear in the list of available prompts, and attempting to call a disabled prompt will result in an "Unknown prompt" error.

By default, all prompts are enabled. You can disable a prompt upon creation using the `enabled` parameter in the decorator:

```python
@mcp.prompt(enabled=False)
def experimental_prompt():
    """This prompt is not ready for use."""
    return "This is an experimental prompt."
```

You can also toggle a prompt's state programmatically after it has been created:

```python
@mcp.prompt
def seasonal_prompt(): return "Happy Holidays!"


------------------------------------------------------------

## Disable and re-enable the prompt
类型: docs, 行数: 5

# Disable and re-enable the prompt
seasonal_prompt.disable()
seasonal_prompt.enable()
```


------------------------------------------------------------

## Async Prompts
类型: docs, 行数: 5

### Async Prompts

FastMCP seamlessly supports both standard (`def`) and asynchronous (`async def`) functions as prompts.

```python

------------------------------------------------------------

## Synchronous prompt
类型: docs, 行数: 6

# Synchronous prompt
@mcp.prompt
def simple_question(question: str) -> str:
    """Generates a simple question to ask the LLM."""
    return f"Question: {question}"


------------------------------------------------------------

## Asynchronous prompt
类型: docs, 行数: 4

# Asynchronous prompt
@mcp.prompt
async def data_based_prompt(data_id: str) -> str:
    """Generates a prompt based on data that needs to be fetched."""

------------------------------------------------------------

## In a real scenario, you might fetch data from a database or API
类型: api, 行数: 9

    # In a real scenario, you might fetch data from a database or API
    async with aiohttp.ClientSession() as session:
        async with session.get(f"https://api.example.com/data/{data_id}") as response:
            data = await response.json()
            return f"Analyze this data: {data['content']}"
```

Use `async def` when your prompt function performs I/O operations like network requests, database queries, file I/O, or external service calls.


------------------------------------------------------------

## Accessing MCP Context
类型: docs, 行数: 19

### Accessing MCP Context

<VersionBadge version="2.2.5" />

Prompts can access additional MCP information and features through the `Context` object. To access it, add a parameter to your prompt function with a type annotation of `Context`:

```python {6}
from fastmcp import FastMCP, Context

mcp = FastMCP(name="PromptServer")

@mcp.prompt
async def generate_report_request(report_type: str, ctx: Context) -> str:
    """Generates a request for a report."""
    return f"Please create a {report_type} report. Request ID: {ctx.request_id}"
```

For full documentation on the Context object and all its capabilities, see the [Context documentation](/servers/context).


------------------------------------------------------------

## Notifications
类型: docs, 行数: 11

### Notifications

<VersionBadge version="2.9.1" />

FastMCP automatically sends `notifications/prompts/list_changed` notifications to connected clients when prompts are added, enabled, or disabled. This allows clients to stay up-to-date with the current prompt set without manually polling for changes.

```python
@mcp.prompt
def example_prompt() -> str:
    return "Hello!"


------------------------------------------------------------

## These operations trigger notifications:
类型: docs, 行数: 10

# These operations trigger notifications:
mcp.add_prompt(example_prompt)  # Sends prompts/list_changed notification
example_prompt.disable()        # Sends prompts/list_changed notification  
example_prompt.enable()         # Sends prompts/list_changed notification
```

Notifications are only sent when these operations occur within an active MCP request context (e.g., when called from within a tool or other MCP operation). Operations performed during server initialization do not trigger notifications.

Clients can handle these notifications using a [message handler](/clients/messages) to automatically refresh their prompt lists or update their interfaces.


------------------------------------------------------------

## Server Behavior
类型: docs, 行数: 2

## Server Behavior


------------------------------------------------------------

## Duplicate Prompts
类型: docs, 行数: 17

### Duplicate Prompts

<VersionBadge version="2.1.0" />

You can configure how the FastMCP server handles attempts to register multiple prompts with the same name. Use the `on_duplicate_prompts` setting during `FastMCP` initialization.

```python
from fastmcp import FastMCP

mcp = FastMCP(
    name="PromptServer",
    on_duplicate_prompts="error"  # Raise an error if a prompt name is duplicated
)

@mcp.prompt
def greeting(): return "Hello, how can I help you today?"


------------------------------------------------------------

## This registration attempt will raise a ValueError because
类型: docs, 行数: 1

# This registration attempt will raise a ValueError because

------------------------------------------------------------

## "greeting" is already registered and the behavior is "error".
类型: docs, 行数: 1

# "greeting" is already registered and the behavior is "error".

------------------------------------------------------------

## @mcp.prompt
类型: docs, 行数: 1

# @mcp.prompt

------------------------------------------------------------

## def greeting(): return "Hi there! What can I do for you?"
类型: docs, 行数: 11

# def greeting(): return "Hi there! What can I do for you?"
```

The duplicate behavior options are:

* `"warn"` (default): Logs a warning, and the new prompt replaces the old one.
* `"error"`: Raises a `ValueError`, preventing the duplicate registration.
* `"replace"`: Silently replaces the existing prompt with the new one.
* `"ignore"`: Keeps the original prompt and ignores the new registration attempt.



------------------------------------------------------------

## Proxy Servers
类型: docs, 行数: 18

# Proxy Servers
Source: https://gofastmcp.com/servers/proxy

Use FastMCP to act as an intermediary or change transport for other MCP servers.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

FastMCP provides a powerful proxying capability that allows one FastMCP server instance to act as a frontend for another MCP server (which could be remote, running on a different transport, or even another FastMCP instance). This is achieved using the `FastMCP.as_proxy()` class method.


------------------------------------------------------------

## What is Proxying?
类型: docs, 行数: 18

## What is Proxying?

Proxying means setting up a FastMCP server that doesn't implement its own tools or resources directly. Instead, when it receives a request (like `tools/call` or `resources/read`), it forwards that request to a *backend* MCP server, receives the response, and then relays that response back to the original client.

```mermaid
sequenceDiagram
    participant ClientApp as Your Client (e.g., Claude Desktop)
    participant FastMCPProxy as FastMCP Proxy Server
    participant BackendServer as Backend MCP Server (e.g., remote SSE)

    ClientApp->>FastMCPProxy: MCP Request (e.g. stdio)
    Note over FastMCPProxy, BackendServer: Proxy forwards the request
    FastMCPProxy->>BackendServer: MCP Request (e.g. sse)
    BackendServer-->>FastMCPProxy: MCP Response (e.g. sse)
    Note over ClientApp, FastMCPProxy: Proxy relays the response
    FastMCPProxy-->>ClientApp: MCP Response (e.g. stdio)
```


------------------------------------------------------------

## Key Benefits
类型: docs, 行数: 10

### Key Benefits

<VersionBadge version="2.10.3" />

* **Session Isolation**: Each request gets its own isolated session, ensuring safe concurrent operations
* **Transport Bridging**: Expose servers running on one transport via a different transport
* **Advanced MCP Features**: Automatic forwarding of sampling, elicitation, logging, and progress
* **Security**: Acts as a controlled gateway to backend servers
* **Simplicity**: Single endpoint even if backend location or transport changes


------------------------------------------------------------

## Quick Start
类型: docs, 行数: 10

## Quick Start

<VersionBadge version="2.10.3" />

The recommended way to create a proxy is using `ProxyClient`, which provides full MCP feature support with automatic session isolation:

```python
from fastmcp import FastMCP
from fastmcp.server.proxy import ProxyClient


------------------------------------------------------------

## Create a proxy with full MCP feature support
类型: docs, 行数: 6

# Create a proxy with full MCP feature support
proxy = FastMCP.as_proxy(
    ProxyClient("backend_server.py"),
    name="MyProxy"
)


------------------------------------------------------------

## Run the proxy (e.g., via stdio for Claude Desktop)
类型: docs, 行数: 16

# Run the proxy (e.g., via stdio for Claude Desktop)
if __name__ == "__main__":
    proxy.run()
```

This single setup gives you:

* Safe concurrent request handling
* Automatic forwarding of advanced MCP features (sampling, elicitation, etc.)
* Session isolation to prevent context mixing
* Full compatibility with all MCP clients

You can also pass a FastMCP [client transport](/clients/transports) (or parameter that can be inferred to a transport) to `as_proxy()`. This will automatically create a `ProxyClient` instance for you.

Finally, you can pass a regular FastMCP `Client` instance to `as_proxy()`. This will work for many use cases, but may break if advanced MCP features like sampling or elicitation are invoked by the server.


------------------------------------------------------------

## Session Isolation & Concurrency
类型: docs, 行数: 6

## Session Isolation & Concurrency

<VersionBadge version="2.10.3" />

FastMCP proxies provide session isolation to ensure safe concurrent operations. The session strategy depends on how the proxy is configured:


------------------------------------------------------------

## Fresh Sessions
类型: docs, 行数: 7

### Fresh Sessions

When you pass a disconnected client (which is the normal case), each request gets its own isolated backend session:

```python
from fastmcp.server.proxy import ProxyClient


------------------------------------------------------------

## Each request creates a fresh backend session (recommended)
类型: docs, 行数: 3

# Each request creates a fresh backend session (recommended)
proxy = FastMCP.as_proxy(ProxyClient("backend_server.py"))


------------------------------------------------------------

## Multiple clients can use this proxy simultaneously without interference:
类型: docs, 行数: 1

# Multiple clients can use this proxy simultaneously without interference:

------------------------------------------------------------

## - Client A calls a tool -> gets isolated backend session
类型: docs, 行数: 1

# - Client A calls a tool -> gets isolated backend session

------------------------------------------------------------

## - Client B calls a tool -> gets different isolated backend session
类型: docs, 行数: 1

# - Client B calls a tool -> gets different isolated backend session  

------------------------------------------------------------

## - No context mixing between requests
类型: docs, 行数: 3

# - No context mixing between requests
```


------------------------------------------------------------

## Session Reuse with Connected Clients
类型: docs, 行数: 7

### Session Reuse with Connected Clients

When you pass an already-connected client, the proxy will reuse that session for all requests:

```python
from fastmcp import Client


------------------------------------------------------------

## Create and connect a client
类型: docs, 行数: 2

# Create and connect a client
async with Client("backend_server.py") as connected_client:

------------------------------------------------------------

## This proxy will reuse the connected session for all requests
类型: docs, 行数: 3

    # This proxy will reuse the connected session for all requests
    proxy = FastMCP.as_proxy(connected_client)
    

------------------------------------------------------------

## ⚠️ Warning: All requests share the same backend session
类型: docs, 行数: 1

    # ⚠️ Warning: All requests share the same backend session

------------------------------------------------------------

## This may cause context mixing in concurrent scenarios
类型: docs, 行数: 5

    # This may cause context mixing in concurrent scenarios
```

**Important**: Using shared sessions with concurrent requests from multiple clients may lead to context mixing and race conditions. This approach should only be used in single-threaded scenarios or when you have explicit synchronization.


------------------------------------------------------------

## Transport Bridging
类型: docs, 行数: 8

## Transport Bridging

A common use case is bridging transports - exposing a server running on one transport via a different transport. For example, making a remote SSE server available locally via stdio:

```python
from fastmcp import FastMCP
from fastmcp.server.proxy import ProxyClient


------------------------------------------------------------

## Bridge remote SSE server to local stdio
类型: docs, 行数: 6

# Bridge remote SSE server to local stdio
remote_proxy = FastMCP.as_proxy(
    ProxyClient("http://example.com/mcp/sse"),
    name="Remote-to-Local Bridge"
)


------------------------------------------------------------

## Run locally via stdio for Claude Desktop
类型: docs, 行数: 8

# Run locally via stdio for Claude Desktop
if __name__ == "__main__":
    remote_proxy.run()  # Defaults to stdio transport
```

Or expose a local server via HTTP for remote access:

```python

------------------------------------------------------------

## Bridge local server to HTTP
类型: docs, 行数: 6

# Bridge local server to HTTP
local_proxy = FastMCP.as_proxy(
    ProxyClient("local_server.py"),
    name="Local-to-HTTP Bridge"
)


------------------------------------------------------------

## Run via HTTP for remote clients
类型: docs, 行数: 5

# Run via HTTP for remote clients
if __name__ == "__main__":
    local_proxy.run(transport="http", host="0.0.0.0", port=8080)
```


------------------------------------------------------------

## Advanced MCP Features
类型: docs, 行数: 6

## Advanced MCP Features

<VersionBadge version="2.10.3" />

`ProxyClient` automatically forwards advanced MCP protocol features between the backend server and clients connected to the proxy, ensuring full MCP compatibility.


------------------------------------------------------------

## Supported Features
类型: docs, 行数: 11

### Supported Features

* **Roots**: Forwards filesystem root access requests to the client
* **Sampling**: Forwards LLM completion requests from backend to client
* **Elicitation**: Forwards user input requests to the client
* **Logging**: Forwards log messages from backend through to client
* **Progress**: Forwards progress notifications during long operations

```python
from fastmcp.server.proxy import ProxyClient


------------------------------------------------------------

## ProxyClient automatically handles all these features
类型: docs, 行数: 4

# ProxyClient automatically handles all these features
backend = ProxyClient("advanced_backend.py")
proxy = FastMCP.as_proxy(backend)


------------------------------------------------------------

## When the backend server:
类型: docs, 行数: 1

# When the backend server:

------------------------------------------------------------

## - Requests LLM sampling -> forwarded to your client
类型: docs, 行数: 1

# - Requests LLM sampling -> forwarded to your client

------------------------------------------------------------

## - Logs messages -> appear in your client
类型: docs, 行数: 1

# - Logs messages -> appear in your client

------------------------------------------------------------

## - Reports progress -> shown in your client
类型: docs, 行数: 1

# - Reports progress -> shown in your client

------------------------------------------------------------

## - Needs user input -> prompts your client
类型: docs, 行数: 3

# - Needs user input -> prompts your client
```


------------------------------------------------------------

## Customizing Feature Support
类型: docs, 行数: 5

### Customizing Feature Support

You can selectively disable forwarding by passing `None` for specific handlers:

```python

------------------------------------------------------------

## Disable sampling but keep other features
类型: docs, 行数: 10

# Disable sampling but keep other features
backend = ProxyClient(
    "backend_server.py",
    sampling_handler=None,  # Disable LLM sampling forwarding
    log_handler=None        # Disable log forwarding
)
```

When you use a transport string directly with `FastMCP.as_proxy()`, it automatically creates a `ProxyClient` internally to ensure full feature support.


------------------------------------------------------------

## Configuration-Based Proxies
类型: setup, 行数: 9

## Configuration-Based Proxies

<VersionBadge version="2.4.0" />

You can create a proxy directly from a configuration dictionary that follows the MCPConfig schema. This is useful for quickly setting up proxies to remote servers without manually configuring each connection detail.

```python
from fastmcp import FastMCP


------------------------------------------------------------

## Create a proxy directly from a config dictionary
类型: setup, 行数: 10

# Create a proxy directly from a config dictionary
config = {
    "mcpServers": {
        "default": {  # For single server configs, 'default' is commonly used
            "url": "https://example.com/mcp",
            "transport": "http"
        }
    }
}


------------------------------------------------------------

## Create a proxy to the configured server (auto-creates ProxyClient)
类型: setup, 行数: 3

# Create a proxy to the configured server (auto-creates ProxyClient)
proxy = FastMCP.as_proxy(config, name="Config-Based Proxy")


------------------------------------------------------------

## Run the proxy with stdio transport for local access
类型: docs, 行数: 9

# Run the proxy with stdio transport for local access
if __name__ == "__main__":
    proxy.run()
```

<Note>
  The MCPConfig format follows an emerging standard for MCP server configuration and may evolve as the specification matures. While FastMCP aims to maintain compatibility with future versions, be aware that field names or structure might change.
</Note>


------------------------------------------------------------

## Multi-Server Configurations
类型: setup, 行数: 5

### Multi-Server Configurations

You can create a proxy to multiple servers by specifying multiple entries in the config. They are automatically mounted with their config names as prefixes:

```python

------------------------------------------------------------

## Multi-server configuration
类型: setup, 行数: 14

# Multi-server configuration
config = {
    "mcpServers": {
        "weather": {
            "url": "https://weather-api.example.com/mcp",
            "transport": "http"
        },
        "calendar": {
            "url": "https://calendar-api.example.com/mcp",
            "transport": "http"
        }
    }
}


------------------------------------------------------------

## Create a unified proxy to multiple servers
类型: docs, 行数: 3

# Create a unified proxy to multiple servers
composite_proxy = FastMCP.as_proxy(config, name="Composite Proxy")


------------------------------------------------------------

## Tools and resources are accessible with prefixes:
类型: docs, 行数: 1

# Tools and resources are accessible with prefixes:

------------------------------------------------------------

## - weather_get_forecast, calendar_add_event
类型: docs, 行数: 1

# - weather_get_forecast, calendar_add_event 

------------------------------------------------------------

## - weather://weather/icons/sunny, calendar://calendar/events/today
类型: docs, 行数: 3

# - weather://weather/icons/sunny, calendar://calendar/events/today
```


------------------------------------------------------------

