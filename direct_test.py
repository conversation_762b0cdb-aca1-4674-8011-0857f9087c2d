#!/usr/bin/env python3
"""
直接测试MCP服务器
"""

import asyncio
from fastmcp import FastMCP, Client

# 创建简单的测试服务器
mcp = FastMCP("测试服务器")

@mcp.tool()
def hello() -> str:
    """简单的hello工具"""
    return "Hello from MCP!"

@mcp.tool()
def test_database() -> dict:
    """测试数据库连接"""
    try:
        import mysql.connector
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='realtime_data',
            connect_timeout=5
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM industrial_monitoring")
        count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        return {
            "status": "success",
            "message": "数据库连接成功",
            "record_count": count
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

async def test_client():
    """测试客户端"""
    print("🧪 测试MCP客户端连接...")
    
    # 使用内存中的服务器进行测试
    client = Client(mcp)
    
    try:
        async with client:
            print("✅ 客户端连接成功")
            
            # 测试hello工具
            result = await client.call_tool("hello", {})
            print(f"📞 hello工具结果: {result}")
            
            # 测试数据库工具
            result = await client.call_tool("test_database", {})
            print(f"📞 数据库测试结果: {result}")
            
            # 列出所有工具
            tools = await client.list_tools()
            print(f"🔧 可用工具: {[tool.name for tool in tools]}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 直接测试MCP系统")
    print("=" * 40)
    
    # 运行测试
    asyncio.run(test_client())
