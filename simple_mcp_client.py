#!/usr/bin/env python3
"""
简化的MCP客户端
用于测试和基本交互
"""

import asyncio
import json
from typing import Dict, Any
from fastmcp import Client

class SimpleMCPClient:
    """简化的MCP客户端"""
    
    def __init__(self, server_url: str = "http://127.0.0.1:9000/mcp/"):
        self.server_url = server_url
        self.client = None
    
    async def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            print(f"🔗 正在连接到: {self.server_url}")
            self.client = Client(self.server_url)
            print("✅ 连接成功")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def list_tools(self):
        """列出可用工具"""
        try:
            async with self.client:
                tools = await self.client.list_tools()
                print("\n📋 可用工具:")
                for tool in tools:
                    print(f"  🔧 {tool.name}: {tool.description}")
                return tools
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
            return []
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None):
        """调用工具"""
        try:
            if arguments is None:
                arguments = {}

            print(f"🔄 调用工具: {tool_name}")
            print(f"📝 参数: {arguments}")

            async with self.client:
                result = await self.client.call_tool(tool_name, arguments)

            print("✅ 调用成功")
            print(f"📊 结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result

        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    async def test_system_status(self):
        """测试系统状态"""
        print("\n🧪 测试系统状态...")
        await self.call_tool("get_system_status")
    
    async def test_statistics(self):
        """测试统计分析"""
        print("\n🧪 测试统计分析...")
        await self.call_tool("calculate_statistics", {
            "table": "industrial_monitoring",
            "column": "pressure_1",
            "operation": "avg"
        })
    
    async def test_anomaly_detection(self):
        """测试异常检测"""
        print("\n🧪 测试异常检测...")
        await self.call_tool("detect_anomalies_with_ai", {
            "table": "industrial_monitoring",
            "column": "pressure_1",
            "method": "zscore",
            "threshold": 2.0,
            "enable_ai_analysis": True
        })
    
    async def test_chart_generation(self):
        """测试图表生成"""
        print("\n🧪 测试图表生成...")
        await self.call_tool("generate_chart", {
            "table": "industrial_monitoring",
            "chart_type": "line",
            "x_column": "record_time",
            "y_column": "pressure_1",
            "title": "压力1趋势图"
        })
    
    async def test_trend_analysis(self):
        """测试趋势分析"""
        print("\n🧪 测试趋势分析...")
        await self.call_tool("analyze_trend_with_ai", {
            "table": "industrial_monitoring",
            "time_column": "record_time",
            "value_column": "pressure_1",
            "period": "hour",
            "enable_ai_insights": True
        })
    
    async def interactive_mode(self):
        """交互模式"""
        print("🎯 简化MCP客户端")
        print("=" * 40)
        
        if not await self.connect():
            return
        
        await self.list_tools()
        
        print("\n💡 可用命令:")
        print("  1. status - 系统状态")
        print("  2. stats - 统计分析测试")
        print("  3. anomaly - 异常检测测试")
        print("  4. chart - 图表生成测试")
        print("  5. trend - 趋势分析测试")
        print("  6. tools - 列出工具")
        print("  7. custom - 自定义工具调用")
        print("  8. quit - 退出")
        
        while True:
            try:
                command = input("\n📝 请输入命令: ").strip().lower()
                
                if command in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                elif command == 'status':
                    await self.test_system_status()
                
                elif command == 'stats':
                    await self.test_statistics()
                
                elif command == 'anomaly':
                    await self.test_anomaly_detection()
                
                elif command == 'chart':
                    await self.test_chart_generation()
                
                elif command == 'trend':
                    await self.test_trend_analysis()
                
                elif command == 'tools':
                    await self.list_tools()
                
                elif command == 'custom':
                    await self.custom_tool_call()
                
                else:
                    print("❌ 未知命令，请输入有效命令")
            
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    async def custom_tool_call(self):
        """自定义工具调用"""
        try:
            tool_name = input("工具名称: ").strip()
            if not tool_name:
                print("❌ 工具名称不能为空")
                return
            
            print("参数 (JSON格式，留空表示无参数):")
            args_input = input().strip()
            
            if args_input:
                try:
                    arguments = json.loads(args_input)
                except json.JSONDecodeError:
                    print("❌ 参数格式错误，请使用有效的JSON")
                    return
            else:
                arguments = {}
            
            await self.call_tool(tool_name, arguments)
            
        except Exception as e:
            print(f"❌ 自定义调用失败: {e}")
    
    async def close(self):
        """关闭连接"""
        if self.client:
            await self.client.close()

async def main():
    """主函数"""
    client = SimpleMCPClient()
    try:
        await client.interactive_mode()
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(main())
