#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化启动脚本 - MySQL数据分析系统
避免编码问题的简化版本
"""

import os
import sys
import time
import subprocess
import threading
import signal
import webbrowser
from pathlib import Path

class SimpleSystemManager:
    """简化系统管理器"""
    
    def __init__(self):
        self.mcp_process = None
        self.web_process = None
        self.is_running = False
        self.base_dir = Path(__file__).parent
    
    def print_banner(self):
        """打印启动横幅 - 简化版"""
        print("=" * 60)
        print("    MySQL数据分析系统 - 完整本地化解决方案")
        print("    MCP服务器 + Web客户端")
        print("    完全离线运行，无需互联网连接")
        print("=" * 60)
    
    def check_requirements(self):
        """检查系统要求"""
        print("检查系统要求...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            return False
        
        # 检查必要文件
        required_files = [
            'mysql_analysis_mcp.py',
            'web_server.py',
            'db_config.json',
            'web_client/index.html',
            'web_client/styles.css',
            'web_client/app.js'
        ]
        
        missing_files = []
        for file in required_files:
            if not (self.base_dir / file).exists():
                missing_files.append(file)
        
        if missing_files:
            print("错误: 缺少必要文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        
        print("系统要求检查通过")
        return True
    
    def start_mcp_server(self):
        """启动MCP服务器"""
        print("启动MCP服务器...")
        
        try:
            # 启动MCP服务器进程
            self.mcp_process = subprocess.Popen(
                [sys.executable, 'mysql_analysis_mcp.py'],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # 等待服务器启动
            print("等待MCP服务器启动...")
            time.sleep(5)
            
            # 检查进程是否还在运行
            if self.mcp_process.poll() is None:
                print("MCP服务器启动成功")
                return True
            else:
                stdout, stderr = self.mcp_process.communicate()
                print("MCP服务器启动失败:")
                if stderr:
                    print(f"错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"启动MCP服务器失败: {e}")
            return False
    
    def start_web_server(self):
        """启动Web服务器"""
        print("启动Web服务器...")
        
        try:
            # 启动Web服务器进程
            self.web_process = subprocess.Popen(
                [sys.executable, 'web_server.py', '8080'],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # 等待Web服务器启动
            print("等待Web服务器启动...")
            time.sleep(3)
            
            # 检查进程是否还在运行
            if self.web_process.poll() is None:
                print("Web服务器启动成功")
                return True
            else:
                stdout, stderr = self.web_process.communicate()
                print("Web服务器启动失败:")
                if stderr:
                    print(f"错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"启动Web服务器失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        print("打开Web界面...")
        
        try:
            web_url = "http://127.0.0.1:8080"
            webbrowser.open(web_url)
            print(f"浏览器已打开: {web_url}")
        except Exception as e:
            print(f"无法自动打开浏览器: {e}")
            print("请手动在浏览器中打开: http://127.0.0.1:8080")
    
    def print_status(self):
        """打印系统状态"""
        print("\n" + "=" * 60)
        print("MySQL数据分析系统已启动")
        print("=" * 60)
        print("\n服务状态:")
        print("   MCP服务器: http://127.0.0.1:9000/mcp/ (运行中)")
        print("   Web界面: http://127.0.0.1:8080 (运行中)")
        
        print("\n使用说明:")
        print("   1. Web界面已在浏览器中打开")
        print("   2. 点击'连接服务器'连接到MCP服务器")
        print("   3. 开始使用数据分析功能")
        
        print("\n功能特性:")
        print("   - 实时数据统计分析")
        print("   - 智能异常检测")
        print("   - 多种图表生成")
        print("   - 自定义SQL查询")
        print("   - 语音交互功能")
        print("   - 智能提醒系统")
        
        print("\n安全特性:")
        print("   - 完全本地部署，无需互联网连接")
        print("   - 数据不会离开本地环境")
        print("   - 支持大数据量实时处理")
        
        print("\n停止系统: 按 Ctrl+C")
        print("=" * 60)
    
    def stop_system(self):
        """停止整个系统"""
        print("\n正在停止系统...")
        
        # 停止Web服务器
        if self.web_process and self.web_process.poll() is None:
            print("停止Web服务器...")
            self.web_process.terminate()
            try:
                self.web_process.wait(timeout=5)
                print("Web服务器已停止")
            except subprocess.TimeoutExpired:
                self.web_process.kill()
                print("强制停止Web服务器")
        
        # 停止MCP服务器
        if self.mcp_process and self.mcp_process.poll() is None:
            print("停止MCP服务器...")
            self.mcp_process.terminate()
            try:
                self.mcp_process.wait(timeout=5)
                print("MCP服务器已停止")
            except subprocess.TimeoutExpired:
                self.mcp_process.kill()
                print("强制停止MCP服务器")
        
        self.is_running = False
        print("MySQL数据分析系统已完全停止")
    
    def run(self):
        """运行整个系统"""
        self.print_banner()
        
        # 检查系统要求
        if not self.check_requirements():
            print("\n系统要求检查失败，无法启动")
            return False
        
        # 启动MCP服务器
        if not self.start_mcp_server():
            print("\nMCP服务器启动失败，无法继续")
            return False
        
        # 启动Web服务器
        if not self.start_web_server():
            print("\nWeb服务器启动失败，停止MCP服务器")
            self.stop_system()
            return False
        
        # 打开浏览器
        self.open_browser()
        
        # 打印状态
        self.print_status()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, lambda sig, frame: self.stop_system())
        
        self.is_running = True
        
        try:
            # 保持系统运行
            while self.is_running:
                time.sleep(1)
                
                # 检查进程状态
                if self.mcp_process and self.mcp_process.poll() is not None:
                    print("警告: MCP服务器意外停止")
                    break
                
                if self.web_process and self.web_process.poll() is not None:
                    print("警告: Web服务器意外停止")
                    break
                    
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_system()
        
        return True

def main():
    """主函数"""
    try:
        system_manager = SimpleSystemManager()
        success = system_manager.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"系统启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
