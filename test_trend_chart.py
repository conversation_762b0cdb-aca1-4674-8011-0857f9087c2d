#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试趋势图API
"""

import requests
import json

def test_trend_chart():
    """测试趋势图生成"""
    base_url = "http://127.0.0.1:8080"
    
    print("=" * 60)
    print("测试趋势图API")
    print("=" * 60)
    
    # 测试趋势图生成
    print("\n测试趋势图生成 API")
    try:
        payload = {
            "chart_type": "line",
            "table": "payment",
            "x_column": "payment_date",
            "y_column": "amount",
            "title": "支付金额趋势分析",
            "time_range": "全部数据"
        }
        
        print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post(f"{base_url}/api/generate-chart", json=payload)
        data = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"响应格式: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if data.get("success") and data.get("chart_data"):
            print("✅ 趋势图API测试成功！")
        else:
            print("❌ 趋势图API测试失败！")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    """主函数"""
    test_trend_chart()

if __name__ == "__main__":
    main()
