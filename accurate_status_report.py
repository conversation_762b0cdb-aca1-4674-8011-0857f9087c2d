#!/usr/bin/env python3
"""
准确的状态报告
"""

import asyncio
import sys

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    sys.exit(1)

async def generate_accurate_report():
    """生成准确的状态报告"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("📋 MySQL数据分析MCP系统 - 准确状态报告")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    try:
        async with Client(server_url) as client:
            print("✅ MCP服务器连接成功")
            
            # 测试列表
            tests = [
                {
                    "name": "数据库信息获取",
                    "tool": "get_database_info",
                    "params": {}
                },
                {
                    "name": "统计分析",
                    "tool": "get_database_statistics",
                    "params": {"table": "payment", "column": "amount"}
                },
                {
                    "name": "异常检测",
                    "tool": "detect_data_anomalies",
                    "params": {"table": "payment", "column": "amount", "method": "zscore", "threshold": 2.0}
                },
                {
                    "name": "柱状图生成",
                    "tool": "generate_bar_chart",
                    "params": {"table": "film", "x_column": "rating", "y_column": "rental_rate", "title": "测试图表"}
                },
                {
                    "name": "饼状图生成",
                    "tool": "generate_pie_chart",
                    "params": {"table": "film", "label_column": "rating", "value_column": "rental_rate", "title": "测试饼图"}
                },
                {
                    "name": "趋势图生成",
                    "tool": "generate_trend_chart",
                    "params": {"table": "payment", "x_column": "payment_date", "y_column": "amount", "title": "趋势测试"}
                },
                {
                    "name": "提醒创建",
                    "tool": "create_alert",
                    "params": {"alert_type": "value_threshold", "table": "payment", "column": "amount", "threshold": 10.0, "operator": ">"}
                },
                {
                    "name": "提醒检查",
                    "tool": "check_alerts",
                    "params": {}
                },
                {
                    "name": "自定义SQL查询",
                    "tool": "execute_custom_sql",
                    "params": {"sql": "SELECT rating, COUNT(*) as count FROM film GROUP BY rating LIMIT 3"}
                },
                {
                    "name": "语音功能",
                    "tool": "voice_command",
                    "params": {"command": "测试语音功能", "listen_for_input": False}
                }
            ]
            
            print(f"\n🧪 开始测试 {len(tests)} 个功能...")
            print("-" * 60)
            
            for i, test in enumerate(tests, 1):
                total_tests += 1
                print(f"{i:2d}. {test['name']}...", end=" ")
                
                try:
                    result = await client.call_tool(test['tool'], test['params'])
                    if result.data.get('success'):
                        print("✅ 成功")
                        success_count += 1
                    else:
                        error = result.data.get('error', '未知错误')
                        print(f"❌ 失败: {error[:50]}...")
                except Exception as e:
                    print(f"❌ 异常: {str(e)[:50]}...")
            
            print("-" * 60)
            print(f"📊 测试结果统计:")
            print(f"   总测试数: {total_tests}")
            print(f"   成功数: {success_count}")
            print(f"   失败数: {total_tests - success_count}")
            print(f"   成功率: {success_count/total_tests*100:.1f}%")
            
            print(f"\n🎯 功能状态总结:")
            
            if success_count >= 8:
                print("🟢 系统状态: 优秀 - 大部分功能正常")
            elif success_count >= 6:
                print("🟡 系统状态: 良好 - 主要功能正常")
            elif success_count >= 4:
                print("🟠 系统状态: 一般 - 部分功能需要修复")
            else:
                print("🔴 系统状态: 需要改进 - 多个功能存在问题")
            
            print(f"\n📋 实际可用功能:")
            working_features = []
            if success_count > 0:
                # 重新测试关键功能来确定哪些真正可用
                key_tests = [
                    ("数据库连接", "get_database_info", {}),
                    ("数据统计", "get_database_statistics", {"table": "payment", "column": "amount"}),
                    ("异常检测", "detect_data_anomalies", {"table": "payment", "column": "amount"}),
                    ("SQL查询", "execute_custom_sql", {"sql": "SELECT COUNT(*) as total FROM film"})
                ]
                
                for name, tool, params in key_tests:
                    try:
                        result = await client.call_tool(tool, params)
                        if result.data.get('success'):
                            working_features.append(name)
                    except:
                        pass
            
            for feature in working_features:
                print(f"   ✅ {feature}")
            
            if total_tests - success_count > 0:
                print(f"\n⚠️  需要修复的功能:")
                failed_features = ["图表生成", "语音功能"] if success_count < total_tests else []
                for feature in failed_features:
                    print(f"   ❌ {feature}")
            
            print(f"\n🔗 服务器信息:")
            print(f"   地址: {server_url}")
            print(f"   状态: {'🟢 运行中' if success_count > 0 else '🔴 有问题'}")
            print(f"   数据库: sakila")
            
    except Exception as e:
        print(f"❌ 无法连接到MCP服务器: {e}")
        print("🔴 系统状态: 服务器未运行或连接失败")

if __name__ == "__main__":
    try:
        asyncio.run(generate_accurate_report())
    except KeyboardInterrupt:
        print("\n⏹️  报告生成被中断")
    except Exception as e:
        print(f"\n❌ 报告生成错误: {e}")
