#!/usr/bin/env python3
"""
测试MCP连接
"""

import requests
import json

def test_mcp_server():
    """测试MCP服务器连接"""
    print("🔍 测试MCP服务器连接")
    print("=" * 40)
    
    # 测试端口
    ports = [9002, 9003]
    for port in ports:
        url = f"http://127.0.0.1:{port}/mcp"
        print(f"\n📍 测试端口 {port}: {url}")
        
        try:
            # 测试基本连接
            response = requests.get(url, timeout=5)
            print(f"✅ GET请求成功: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            if response.text:
                print(f"   响应内容: {response.text[:200]}...")
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败: 端口 {port} 无法连接")
        except requests.exceptions.Timeout:
            print(f"❌ 连接超时: 端口 {port}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        
        try:
            # 测试MCP初始化
            init_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = requests.post(
                url,
                json=init_payload,
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            print(f"✅ MCP初始化请求成功: {response.status_code}")
            if response.text:
                print(f"   响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ MCP初始化失败: {e}")

if __name__ == "__main__":
    test_mcp_server()
