#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新架构的简化脚本
"""

import asyncio
import json
import subprocess
import sys
from pathlib import Path

async def test_mcp_call():
    """测试MCP工具调用"""
    print("测试MCP工具调用...")
    
    # 创建临时的Python脚本来调用MCP工具
    script_content = '''
import asyncio
import json
import sys
from fastmcp import Client

async def call_tool():
    try:
        client = Client("mysql_analysis_mcp.py")
        async with client:
            result = await client.call_tool("get_database_info", {})
            print(json.dumps({"success": True, "data": result.data if hasattr(result, "data") else result}))
    except Exception as e:
        print(json.dumps({"success": False, "error": str(e)}))

if __name__ == "__main__":
    asyncio.run(call_tool())
'''
    
    try:
        # 执行脚本
        process = await asyncio.create_subprocess_exec(
            sys.executable, "-c", script_content,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=Path.cwd()
        )
        
        stdout, stderr = await process.communicate()
        
        print(f"返回码: {process.returncode}")
        print(f"标准输出: {stdout.decode()}")
        print(f"标准错误: {stderr.decode()}")
        
        if process.returncode == 0:
            result = json.loads(stdout.decode())
            if result.get("success"):
                print("✓ MCP工具调用成功!")
                print(f"结果: {result.get('data')}")
                return True
            else:
                print(f"✗ MCP工具调用失败: {result.get('error')}")
                return False
        else:
            print(f"✗ 进程执行失败: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_file_existence():
    """测试必要文件是否存在"""
    print("检查必要文件...")
    
    required_files = [
        'mysql_analysis_mcp.py',
        'mcp_http_bridge.py',
        'web_client/index.html',
        'web_client/app.js',
        'db_config.json'
    ]
    
    all_exist = True
    for file in required_files:
        if Path(file).exists():
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - 文件不存在")
            all_exist = False
    
    return all_exist

async def main():
    """主测试函数"""
    print("=" * 60)
    print("    新架构测试")
    print("    MCP服务器 -> HTTP桥接器 -> Web客户端")
    print("=" * 60)
    
    # 测试文件存在性
    if not test_file_existence():
        print("\n文件检查失败，无法继续测试")
        return False
    
    print("\n" + "=" * 60)
    
    # 测试MCP调用
    if await test_mcp_call():
        print("\n✓ 新架构测试通过!")
        print("\n建议:")
        print("1. 运行 'python mcp_http_bridge.py' 启动HTTP桥接器")
        print("2. 运行 'python web_server.py 8081' 启动Web服务器")
        print("3. 在浏览器中打开 http://127.0.0.1:8081")
        return True
    else:
        print("\n✗ 新架构测试失败")
        print("\n可能的问题:")
        print("1. FastMCP依赖未安装: pip install fastmcp")
        print("2. MySQL数据库未运行")
        print("3. 数据库配置错误")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试出错: {e}")
        sys.exit(1)
