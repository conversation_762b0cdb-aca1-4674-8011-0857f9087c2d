#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP HTTP Bridge - FastMCP客户端到HTTP API的桥接器
将Web HTTP请求转换为MCP协议调用
"""

import asyncio
import json
import logging
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mcp_bridge.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# Pydantic模型
class ToolCallRequest(BaseModel):
    tool_name: str
    arguments: Dict[str, Any]

class StatisticsRequest(BaseModel):
    table: str
    column: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None

class AnomalyDetectionRequest(BaseModel):
    table: str
    column: str
    method: str = "zscore"
    threshold: float = 2.0

class ChartRequest(BaseModel):
    chart_type: str
    table: str
    x_column: Optional[str] = None
    y_column: Optional[str] = None
    label_column: Optional[str] = None
    value_column: Optional[str] = None
    time_column: Optional[str] = None
    title: Optional[str] = None

class SQLRequest(BaseModel):
    query: str

class MCPHttpBridge:
    """MCP HTTP桥接器 - 使用子进程调用MCP工具"""

    def __init__(self, mcp_server_path: str = "mysql_analysis_mcp.py"):
        self.app = FastAPI(
            title="MySQL数据分析系统 - HTTP API",
            description="FastMCP服务器的HTTP API桥接器",
            version="1.0.0"
        )
        self.mcp_server_path = mcp_server_path
        self.setup_middleware()
        self.setup_routes()

    async def _call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """调用MCP工具的内部方法"""
        print(f"\n[MCP调用] 开始调用工具: {tool_name}")
        print(f"[MCP调用] 参数: {arguments}")
        logger.debug(f"开始调用MCP工具: {tool_name}, 参数: {arguments}")

        try:
            # 检查MCP服务器文件
            mcp_path = Path(self.mcp_server_path)
            print(f"[MCP调用] 检查MCP服务器文件: {mcp_path.absolute()}")
            if not mcp_path.exists():
                print(f"[MCP调用] 文件不存在!")
                raise Exception(f"MCP服务器文件不存在: {mcp_path.absolute()}")

            print(f"[MCP调用] 文件存在")
            logger.debug(f"MCP服务器文件路径: {mcp_path.absolute()}")

            # 创建临时的Python脚本来调用MCP工具
            script_content = f'''
import asyncio
import json
import sys
import traceback
from pathlib import Path

# 添加调试信息
print("DEBUG: 开始执行MCP工具调用脚本", file=sys.stderr)
print(f"DEBUG: Python版本: {{sys.version}}", file=sys.stderr)
print(f"DEBUG: 当前工作目录: {{Path.cwd()}}", file=sys.stderr)

try:
    from fastmcp import Client
    print("DEBUG: FastMCP导入成功", file=sys.stderr)
except ImportError as e:
    print(f"ERROR: FastMCP导入失败: {{e}}", file=sys.stderr)
    print(json.dumps({{"success": False, "error": f"FastMCP导入失败: {{e}}"}}))
    sys.exit(1)

async def call_tool():
    try:
        print("DEBUG: 创建FastMCP客户端", file=sys.stderr)
        client = Client("{self.mcp_server_path}")
        print("DEBUG: 客户端创建成功", file=sys.stderr)

        print("DEBUG: 开始连接MCP服务器", file=sys.stderr)
        async with client:
            print("DEBUG: 连接成功，调用工具: {tool_name}", file=sys.stderr)
            result = await client.call_tool("{tool_name}", {json.dumps(arguments)})
            print("DEBUG: 工具调用成功", file=sys.stderr)

            # 处理结果
            if hasattr(result, "data"):
                data = result.data
            elif hasattr(result, "content"):
                # 处理MCP内容块
                if result.content and len(result.content) > 0:
                    first_content = result.content[0]
                    if hasattr(first_content, "text"):
                        try:
                            data = json.loads(first_content.text)
                        except:
                            data = first_content.text
                    else:
                        data = str(first_content)
                else:
                    data = "无内容"
            else:
                data = result

            print(json.dumps({{"success": True, "data": data}}))

    except Exception as e:
        print(f"ERROR: 工具调用异常: {{e}}", file=sys.stderr)
        print(f"ERROR: 异常详情: {{traceback.format_exc()}}", file=sys.stderr)
        print(json.dumps({{"success": False, "error": str(e)}}))

if __name__ == "__main__":
    print("DEBUG: 启动异步事件循环", file=sys.stderr)
    asyncio.run(call_tool())
'''

            print(f"[MCP调用] 创建子进程执行MCP工具调用")
            print(f"[MCP调用] 工作目录: {mcp_path.parent}")
            logger.debug("创建子进程执行MCP工具调用")

            # 执行脚本
            process = await asyncio.create_subprocess_exec(
                sys.executable, "-c", script_content,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=mcp_path.parent
            )

            print(f"[MCP调用] 等待子进程完成 (PID: {process.pid})")
            logger.debug("等待子进程完成")
            stdout, stderr = await process.communicate()

            # 记录详细的输出信息
            stdout_text = stdout.decode('utf-8', errors='replace')
            stderr_text = stderr.decode('utf-8', errors='replace')

            print(f"[MCP调用] 子进程完成，返回码: {process.returncode}")
            print(f"[MCP调用] 标准输出长度: {len(stdout_text)} 字符")
            print(f"[MCP调用] 标准错误长度: {len(stderr_text)} 字符")

            if stdout_text:
                print(f"[MCP调用] 标准输出: {stdout_text[:200]}...")
            if stderr_text:
                print(f"[MCP调用] 标准错误: {stderr_text[:200]}...")

            logger.debug(f"子进程返回码: {process.returncode}")
            logger.debug(f"子进程标准输出: {stdout_text}")
            logger.debug(f"子进程标准错误: {stderr_text}")

            if process.returncode != 0:
                error_msg = f"MCP工具调用进程失败 (返回码: {process.returncode})"
                if stderr_text:
                    error_msg += f", 错误信息: {stderr_text}"
                logger.error(error_msg)
                raise Exception(error_msg)

            if not stdout_text.strip():
                raise Exception("MCP工具调用没有返回数据")

            try:
                result = json.loads(stdout_text)
                logger.debug(f"解析结果: {result}")
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}, 原始输出: {stdout_text}")
                raise Exception(f"JSON解析失败: {e}")

            if not result.get("success"):
                error = result.get("error", "未知错误")
                logger.error(f"MCP工具返回错误: {error}")
                raise Exception(error)

            data = result.get("data")
            print(f"[MCP调用] 工具调用成功!")
            print(f"[MCP调用] 返回数据类型: {type(data)}")
            if isinstance(data, dict):
                print(f"[MCP调用] 数据键: {list(data.keys())}")
            logger.debug(f"MCP工具调用成功，返回数据: {data}")
            return data

        except Exception as e:
            print(f"[MCP调用] 工具调用失败: {e}")
            logger.error(f"调用MCP工具失败: {e}")
            raise
    
    def setup_middleware(self):
        """设置中间件"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # 添加请求日志中间件
        @self.app.middleware("http")
        async def log_requests(request, call_next):
            start_time = asyncio.get_event_loop().time()
            logger.info(f"收到请求: {request.method} {request.url}")
            logger.debug(f"请求头: {dict(request.headers)}")

            try:
                response = await call_next(request)
                process_time = asyncio.get_event_loop().time() - start_time
                logger.info(f"请求完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.3f}s")
                return response
            except Exception as e:
                process_time = asyncio.get_event_loop().time() - start_time
                logger.error(f"请求失败: {request.method} {request.url} - 错误: {e} - 耗时: {process_time:.3f}s")
                raise
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.on_event("startup")
        async def startup_event():
            """启动时检查MCP服务器文件"""
            try:
                if not Path(self.mcp_server_path).exists():
                    raise FileNotFoundError(f"MCP服务器文件不存在: {self.mcp_server_path}")
                logger.info("HTTP桥接器启动成功")
            except Exception as e:
                logger.error(f"HTTP桥接器启动失败: {e}")
                raise

        @self.app.on_event("shutdown")
        async def shutdown_event():
            """关闭时清理资源"""
            logger.info("HTTP桥接器已关闭")
        
        @self.app.get("/")
        async def root():
            """根路径"""
            logger.info("访问根路径")
            return {
                "message": "MySQL数据分析系统 - HTTP API桥接器",
                "version": "1.0.0",
                "mcp_server": self.mcp_server_path,
                "status": "running",
                "endpoints": {
                    "health": "/health",
                    "tools": "/mcp/tools",
                    "call_tool": "/mcp/call-tool",
                    "database_info": "/api/database-info",
                    "statistics": "/api/statistics",
                    "anomaly_detection": "/api/anomaly-detection",
                    "generate_chart": "/api/generate-chart",
                    "execute_sql": "/api/execute-sql"
                }
            }
        
        @self.app.get("/health")
        async def health_check():
            """健康检查（简化版本）"""
            logger.info("开始健康检查")

            health_info = {
                "status": "healthy",
                "mcp_server_file": self.mcp_server_path,
                "file_exists": False,
                "bridge_status": "running",
                "timestamp": datetime.now().isoformat()
            }

            try:
                # 检查MCP服务器文件是否存在
                mcp_path = Path(self.mcp_server_path)
                health_info["file_exists"] = mcp_path.exists()
                health_info["mcp_server_file"] = str(mcp_path.absolute())

                logger.debug(f"MCP服务器文件检查: {health_info['mcp_server_file']}, 存在: {health_info['file_exists']}")

                if not health_info["file_exists"]:
                    health_info["status"] = "warning"
                    health_info["message"] = "MCP服务器文件不存在，但桥接器正常运行"
                    logger.warning(f"MCP服务器文件不存在: {health_info['mcp_server_file']}")
                else:
                    health_info["message"] = "桥接器和MCP服务器文件都正常"

                return health_info

            except Exception as e:
                health_info["status"] = "error"
                health_info["error"] = str(e)
                health_info["message"] = "健康检查过程中发生错误"
                logger.error(f"健康检查失败: {e}")
                return health_info
        
        @self.app.get("/mcp/tools")
        async def list_tools():
            """列出可用工具"""
            try:
                # 返回硬编码的工具列表，因为我们知道MCP服务器提供哪些工具
                tools = [
                    {"name": "get_database_info", "description": "获取数据库信息"},
                    {"name": "analyze_statistics", "description": "分析统计数据"},
                    {"name": "detect_anomalies", "description": "检测异常数据"},
                    {"name": "generate_chart", "description": "生成图表"},
                    {"name": "execute_sql", "description": "执行SQL查询"}
                ]
                return {"tools": tools}
            except Exception as e:
                logger.error(f"列出工具失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/mcp/call-tool")
        async def call_tool(request: ToolCallRequest):
            """调用MCP工具"""
            try:
                result = await self._call_mcp_tool(request.tool_name, request.arguments)
                return {"result": result}
            except Exception as e:
                logger.error(f"调用工具失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/database-info")
        async def get_database_info():
            """获取数据库信息"""
            try:
                result = await self._call_mcp_tool("get_database_info", {})
                return result
            except Exception as e:
                logger.error(f"获取数据库信息失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/statistics")
        async def run_statistics(request: StatisticsRequest):
            """运行统计分析"""
            try:
                args = {
                    "table": request.table,
                    "column": request.column
                }
                if request.start_date:
                    args["start_date"] = request.start_date
                if request.end_date:
                    args["end_date"] = request.end_date

                result = await self._call_mcp_tool("analyze_statistics", args)
                return result
            except Exception as e:
                logger.error(f"统计分析失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/anomaly-detection")
        async def detect_anomalies(request: AnomalyDetectionRequest):
            """异常检测"""
            try:
                args = {
                    "table": request.table,
                    "column": request.column,
                    "method": request.method,
                    "threshold": request.threshold
                }

                result = await self._call_mcp_tool("detect_anomalies", args)
                return result
            except Exception as e:
                logger.error(f"异常检测失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/generate-chart")
        async def generate_chart(request: ChartRequest):
            """生成图表"""
            try:
                args = {
                    "chart_type": request.chart_type,
                    "table": request.table
                }
                
                # 根据图表类型调用不同的MCP工具
                if request.chart_type == "bar":
                    tool_name = "generate_bar_chart"
                    args.update({
                        "x_column": request.x_column,
                        "y_column": request.y_column,
                        "title": request.title or f"{request.table}柱状图"
                    })
                elif request.chart_type == "pie":
                    tool_name = "generate_pie_chart"
                    args.update({
                        "label_column": request.label_column,
                        "value_column": request.value_column,
                        "title": request.title or f"{request.table}饼状图"
                    })
                elif request.chart_type == "line" or request.chart_type == "trend":
                    tool_name = "generate_trend_chart"
                    args.update({
                        "x_column": request.time_column or request.x_column,
                        "y_column": request.value_column or request.y_column,
                        "title": request.title or f"{request.table}趋势图"
                    })
                elif request.chart_type == "scatter":
                    tool_name = "generate_scatter_chart"
                    args.update({
                        "x_column": request.x_column,
                        "y_column": request.y_column,
                        "title": request.title or f"{request.table}散点图"
                    })
                elif request.chart_type == "heatmap":
                    tool_name = "generate_heatmap_chart"
                    args.update({
                        "x_column": request.x_column,
                        "y_column": request.y_column,
                        "title": request.title or f"{request.table}热力图",
                        "value_column": request.value_column or "COUNT(*)"
                    })
                else:
                    raise ValueError(f"不支持的图表类型: {request.chart_type}")

                result = await self._call_mcp_tool(tool_name, args)
                return result
            except Exception as e:
                logger.error(f"生成图表失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/execute-sql")
        async def execute_sql(request: SQLRequest):
            """执行SQL查询"""
            try:
                args = {"query": request.query}
                
                result = await self._call_mcp_tool("execute_sql", args)
                return result
            except Exception as e:
                logger.error(f"SQL执行失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    def run(self, host: str = "127.0.0.1", port: int = 8082):
        """运行HTTP服务器"""
        print("=" * 60)
        print("    HTTP API桥接器启动")
        print("=" * 60)
        print(f"监听地址: http://{host}:{port}")
        print(f"MCP服务器路径: {self.mcp_server_path}")
        print("日志级别: DEBUG")
        print("=" * 60)

        logger.info(f"启动HTTP API桥接器: http://{host}:{port}")
        logger.info(f"MCP服务器路径: {self.mcp_server_path}")

        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="debug",  # 改为debug级别
            access_log=True     # 启用访问日志
        )

def main():
    """主函数"""
    print("=" * 60)
    print("    MySQL数据分析系统 - MCP HTTP桥接器")
    print("    将FastMCP服务器桥接为HTTP API")
    print("=" * 60)
    
    # 检查MCP服务器文件是否存在
    mcp_server_path = "mysql_analysis_mcp.py"
    if not Path(mcp_server_path).exists():
        print(f"错误: MCP服务器文件不存在: {mcp_server_path}")
        return
    
    # 创建并运行桥接器
    bridge = MCPHttpBridge(mcp_server_path)
    
    try:
        bridge.run(host="127.0.0.1", port=8082)  # 使用新端口
    except KeyboardInterrupt:
        print("\n桥接器已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
