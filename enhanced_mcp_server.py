#!/usr/bin/env python3
"""
增强的MCP服务器 - 支持多种LLM源
优先使用MCP客户端，回退到OpenAI API
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

# OpenAI导入
try:
    import openai
    from openai import AsyncOpenAI
except ImportError:
    print("❌ OpenAI未安装，请运行: pip install openai")
    openai = None

# FastMCP导入
try:
    from fastmcp import FastMCP
    from fastmcp.server.context import Context
except ImportError:
    print("❌ FastMCP未安装，请运行: pip install fastmcp")
    exit(1)

# 导入现有的分析模块
from mysql_analysis_mcp import (
    db_manager, data_analyzer, alert_manager, voice_manager,
    logger
)

# OpenAI配置
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_MODEL = "gpt-4o-mini"

class EnhancedContext:
    """增强的Context类，支持多种LLM源"""
    
    def __init__(self, original_ctx: Context = None):
        self.original_ctx = original_ctx
        self.openai_client = None
        if openai and OPENAI_API_KEY:
            self.openai_client = AsyncOpenAI(api_key=OPENAI_API_KEY)
    
    async def sample(self, messages: str, **kwargs) -> Any:
        """智能LLM调用：优先MCP客户端，回退OpenAI"""
        
        # 方案1：尝试使用MCP客户端
        if self.original_ctx:
            try:
                await self.info("🔄 尝试使用MCP客户端LLM...")
                response = await self.original_ctx.sample(messages, **kwargs)
                await self.info("✅ MCP客户端LLM调用成功")
                return response
            except Exception as mcp_error:
                await self.warning(f"⚠️ MCP客户端LLM调用失败: {mcp_error}")
        
        # 方案2：回退到OpenAI API
        if self.openai_client:
            try:
                await self.info("🔄 回退到OpenAI GPT-4o-mini...")
                
                temperature = kwargs.get('temperature', 0.3)
                max_tokens = kwargs.get('max_tokens', 800)
                
                response = await self.openai_client.chat.completions.create(
                    model=OPENAI_MODEL,
                    messages=[
                        {
                            "role": "system", 
                            "content": "你是一位专业的数据分析专家，擅长MySQL数据库分析、异常检测、趋势分析和业务洞察。请用简洁专业的中文回答，重点关注业务价值和可操作性。"
                        },
                        {
                            "role": "user", 
                            "content": messages
                        }
                    ],
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                
                # 模拟MCP响应格式
                class MockResponse:
                    def __init__(self, text):
                        self.text = text
                
                result = MockResponse(response.choices[0].message.content.strip())
                await self.info("✅ OpenAI API调用成功")
                return result
                
            except Exception as openai_error:
                await self.error(f"❌ OpenAI API调用失败: {openai_error}")
        
        # 方案3：都失败了
        raise Exception("所有LLM源都不可用：MCP客户端和OpenAI API都失败")
    
    async def info(self, message: str):
        """信息日志"""
        if self.original_ctx:
            await self.original_ctx.info(message)
        else:
            logger.info(message)
    
    async def warning(self, message: str):
        """警告日志"""
        if self.original_ctx:
            await self.original_ctx.warning(message)
        else:
            logger.warning(message)
    
    async def error(self, message: str):
        """错误日志"""
        if self.original_ctx:
            await self.original_ctx.error(message)
        else:
            logger.error(message)

# 创建FastMCP应用
app = FastMCP("MySQL数据分析系统-增强版")

@app.tool
async def detect_data_anomalies_enhanced(
    table: str,
    column: str,
    method: str = "zscore",
    threshold: float = 2.0,
    enable_ai_analysis: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """增强的异常检测 - 支持多种LLM源"""
    try:
        # 创建增强Context
        enhanced_ctx = EnhancedContext(ctx)
        
        await enhanced_ctx.info(f"🔍 正在检测表 {table} 列 {column} 的异常数据...")

        # 基础异常检测
        result = data_analyzer.detect_anomalies(
            table=table,
            column=column,
            threshold_multiplier=threshold,
            method=method
        )

        # AI异常原因分析
        ai_analysis = None
        if enable_ai_analysis and result.get('anomalies'):
            try:
                await enhanced_ctx.info("🤖 正在进行AI异常原因分析...")
                
                anomalies_sample = result['anomalies'][:5]
                ai_prompt = f"""作为数据分析专家，请分析以下数据库异常检测结果：

数据表: {table}
字段: {column}
检测方法: {method}
异常总数: {len(result['anomalies'])}
异常率: {result.get('anomaly_rate', 0):.2f}%

异常样本:
{json.dumps(anomalies_sample, indent=2, ensure_ascii=False)}

请从以下角度分析：
1. 可能的业务原因（如促销活动、系统故障、数据录入错误等）
2. 异常模式特征
3. 建议的后续行动
4. 风险评估

请用简洁专业的中文回答，重点关注实际业务场景。"""

                # 使用增强Context的智能sample方法
                ai_response = await enhanced_ctx.sample(
                    messages=ai_prompt,
                    temperature=0.3,
                    max_tokens=800
                )
                
                ai_analysis = ai_response.text
                await enhanced_ctx.info("✅ AI异常分析完成")
                    
            except Exception as ai_error:
                await enhanced_ctx.error(f"❌ AI分析失败: {ai_error}")
                ai_analysis = f"AI分析暂时不可用: {str(ai_error)}"

        # 构建返回结果
        final_result = {
            "success": True,
            "data": result,
            "method": method,
            "threshold": threshold,
            "llm_source": "hybrid"  # 标识使用了混合LLM源
        }
        
        if ai_analysis:
            final_result["ai_analysis"] = ai_analysis
            
        return final_result

    except Exception as e:
        error_msg = f"异常检测失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@app.tool
async def analyze_data_trend_enhanced(
    table: str,
    time_column: str,
    value_column: str,
    period: str = "day",
    enable_ai_insights: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """增强的趋势分析 - 支持多种LLM源"""
    try:
        # 创建增强Context
        enhanced_ctx = EnhancedContext(ctx)
        
        await enhanced_ctx.info(f"📈 正在分析表 {table} 的数据趋势...")

        # 基础趋势分析（简化实现）
        import pandas as pd
        import numpy as np
        
        query = f"""
        SELECT 
            DATE({time_column}) as date,
            AVG({value_column}) as avg_value
        FROM {table}
        WHERE {time_column} >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE({time_column})
        ORDER BY date
        """
        
        df = db_manager.execute_query(query)
        
        if df.empty:
            return {"success": False, "error": "没有找到数据"}

        values = df['avg_value'].values
        trend_slope = np.polyfit(range(len(values)), values, 1)[0]
        
        trend_direction = "上升" if trend_slope > 0 else "下降" if trend_slope < 0 else "平稳"
        growth_rate = ((values[-1] - values[0]) / values[0] * 100) if values[0] != 0 else 0

        # AI洞察分析
        ai_insights = None
        if enable_ai_insights:
            try:
                await enhanced_ctx.info("🤖 正在进行AI趋势洞察分析...")
                
                ai_prompt = f"""对以下数据趋势进行深度洞察分析：

数据表: {table}
字段: {value_column}
分析周期: {period}
数据点数量: {len(values)}
趋势方向: {trend_direction}
增长率: {growth_rate:.2f}%

最近数据趋势:
{json.dumps(df.tail(7).to_dict('records'), indent=2, ensure_ascii=False)}

请从以下角度提供洞察：
1. 趋势模式分析（季节性、周期性、异常波动）
2. 业务含义解读（可能的业务驱动因素）
3. 风险与机会识别
4. 预测可信度评估
5. 建议的监控重点和行动建议

请用专业但易懂的中文回答，重点关注业务价值和可操作性。"""

                # 使用增强Context的智能sample方法
                ai_response = await enhanced_ctx.sample(
                    messages=ai_prompt,
                    temperature=0.4,
                    max_tokens=1000
                )
                
                ai_insights = ai_response.text
                await enhanced_ctx.info("✅ AI趋势洞察分析完成")
                    
            except Exception as ai_error:
                await enhanced_ctx.error(f"❌ AI洞察分析失败: {ai_error}")
                ai_insights = f"AI洞察分析暂时不可用: {str(ai_error)}"

        result = {
            "success": True,
            "trend_analysis": {
                "direction": trend_direction,
                "slope": float(trend_slope),
                "growth_rate": float(growth_rate),
                "data_points": len(values)
            },
            "historical_data": df.to_dict('records'),
            "period": period,
            "llm_source": "hybrid"
        }
        
        if ai_insights:
            result["ai_insights"] = ai_insights
            
        return result

    except Exception as e:
        error_msg = f"趋势分析失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

if __name__ == "__main__":
    print("🚀 启动增强MCP MySQL数据分析服务器...")
    print("🔧 支持多种LLM源：")
    print("  1. 优先使用MCP客户端LLM")
    print("  2. 回退到OpenAI GPT-4o-mini")
    print(f"🤖 OpenAI模型: {OPENAI_MODEL}")
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行服务器
    app.run(transport="http", host="127.0.0.1", port=8085)
