#!/usr/bin/env python3
"""
最小化MCP服务器测试
"""

from fastmcp import FastMCP

# 创建最简单的MCP服务器
mcp = FastMCP("最小测试服务器")

@mcp.tool()
def hello() -> str:
    """简单的hello工具"""
    return "Hello from MCP!"

@mcp.tool()
def add_numbers(a: int, b: int) -> str:
    """简单的加法工具"""
    result = a + b
    return f"{a} + {b} = {result}"

if __name__ == "__main__":
    print("启动最小化MCP测试服务器...")
    print("如果看到FastMCP横幅，说明服务器启动成功")
    try:
        mcp.run(transport="http", host="127.0.0.1", port=9001)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
