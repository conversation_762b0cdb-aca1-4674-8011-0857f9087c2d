#!/usr/bin/env python3
"""
基于OpenAI GPT-4o-mini的MySQL数据分析服务器
支持LLM增强功能
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import json
from datetime import datetime
import os

# OpenAI导入
try:
    import openai
    from openai import AsyncOpenAI
except ImportError:
    print("❌ OpenAI未安装，请运行: pip install openai")
    exit(1)

# FastMCP导入（用于基础框架）
try:
    from fastmcp import FastMCP
    from fastmcp.server.context import Context
except ImportError:
    print("❌ FastMCP未安装，请运行: pip install fastmcp")
    exit(1)

# 导入现有的分析模块
from mysql_analysis_mcp import (
    db_manager, data_analyzer, alert_manager, voice_manager,
    logger
)

# OpenAI配置
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_MODEL = "gpt-4o-mini"

class AIAssistant:
    """OpenAI GPT-4o-mini AI助手"""

    def __init__(self):
        self.client = AsyncOpenAI(api_key=OPENAI_API_KEY)
        self.model = OPENAI_MODEL

    async def analyze(self, prompt: str, temperature: float = 0.3, max_tokens: int = 800) -> str:
        """使用GPT-4o-mini进行分析"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的数据分析专家，擅长MySQL数据库分析、异常检测、趋势分析和业务洞察。请用简洁专业的中文回答，重点关注业务价值和可操作性。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            return f"AI分析暂时不可用: {str(e)}"

# 创建AI助手实例
ai_assistant = AIAssistant()

# 创建FastMCP应用
app = FastMCP("MySQL数据分析系统")

@app.tool
async def get_database_info(ctx: Context = None) -> Dict[str, Any]:
    """获取数据库信息"""
    try:
        if ctx:
            await ctx.info("正在获取数据库信息...")
        
        # 获取表信息
        tables_query = "SHOW TABLES"
        tables_df = db_manager.execute_query(tables_query)
        tables = tables_df.iloc[:, 0].tolist() if not tables_df.empty else []
        
        # 获取数据库统计
        db_stats = {}
        for table in tables[:5]:  # 限制前5个表
            try:
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                count_df = db_manager.execute_query(count_query)
                db_stats[table] = int(count_df.iloc[0]['count']) if not count_df.empty else 0
            except Exception as e:
                logger.warning(f"获取表 {table} 统计失败: {e}")
                db_stats[table] = 0
        
        result = {
            "database_name": "mysql_analysis",
            "tables": tables,
            "table_stats": db_stats,
            "total_tables": len(tables)
        }
        
        if ctx:
            await ctx.info(f"数据库信息获取完成，共 {len(tables)} 个表")
        
        return {"success": True, "database_info": result}
        
    except Exception as e:
        error_msg = f"获取数据库信息失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@app.tool
async def get_database_statistics(
    table: str,
    column: str,
    ctx: Context = None
) -> Dict[str, Any]:
    """获取数据库统计信息"""
    try:
        if ctx:
            await ctx.info(f"正在统计表 {table} 列 {column} 的数据...")
        
        result = data_analyzer.get_statistics(table, column)
        
        if ctx:
            await ctx.info("统计分析完成")
        
        return {"success": True, "data": result}
        
    except Exception as e:
        error_msg = f"统计分析失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@app.tool
async def detect_data_anomalies(
    table: str,
    column: str,
    method: str = "zscore",
    threshold: float = 2.0,
    enable_ai_analysis: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """检测数据异常并提供AI分析"""
    try:
        if ctx:
            await ctx.info(f"正在检测表 {table} 列 {column} 的异常数据...")

        # 基础异常检测
        result = data_analyzer.detect_anomalies(
            table=table,
            column=column,
            threshold_multiplier=threshold,
            method=method
        )

        # AI异常原因分析
        ai_analysis = None
        if enable_ai_analysis and result.get('anomalies'):
            try:
                if ctx:
                    await ctx.info("正在进行AI异常原因分析...")

                anomalies_sample = result['anomalies'][:5]
                ai_prompt = f"""作为数据分析专家，请分析以下数据库异常检测结果：

数据表: {table}
字段: {column}
检测方法: {method}
异常总数: {len(result['anomalies'])}
异常率: {result.get('anomaly_rate', 0):.2f}%

异常样本:
{json.dumps(anomalies_sample, indent=2, ensure_ascii=False)}

请从以下角度分析：
1. 可能的业务原因（如促销活动、系统故障、数据录入错误等）
2. 异常模式特征
3. 建议的后续行动
4. 风险评估

请用简洁专业的中文回答，重点关注实际业务场景。"""

                ai_analysis = await ai_assistant.analyze(ai_prompt, temperature=0.3, max_tokens=800)

                if ctx:
                    await ctx.info("AI异常分析完成")

            except Exception as ai_error:
                logger.warning(f"AI分析失败: {ai_error}")
                ai_analysis = f"AI分析暂时不可用: {str(ai_error)}"

        final_result = {
            "success": True,
            "data": result,
            "method": method,
            "threshold": threshold
        }
        
        if ai_analysis:
            final_result["ai_analysis"] = ai_analysis
            
        return final_result

    except Exception as e:
        error_msg = f"异常检测失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@app.tool
async def analyze_data_trend(
    table: str,
    time_column: str,
    value_column: str,
    period: str = "day",
    enable_ai_insights: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """分析数据趋势并提供AI洞察"""
    try:
        if ctx:
            await ctx.info(f"正在分析表 {table} 的数据趋势...")

        # 基础趋势分析（这里需要实现具体的趋势分析逻辑）
        # 为了演示，我们使用简化的实现
        import pandas as pd
        import numpy as np
        
        query = f"""
        SELECT 
            DATE({time_column}) as date,
            AVG({value_column}) as avg_value
        FROM {table}
        WHERE {time_column} >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE({time_column})
        ORDER BY date
        """
        
        df = db_manager.execute_query(query)
        
        if df.empty:
            return {"success": False, "error": "没有找到数据"}

        values = df['avg_value'].values
        trend_slope = np.polyfit(range(len(values)), values, 1)[0]
        
        trend_direction = "上升" if trend_slope > 0 else "下降" if trend_slope < 0 else "平稳"
        growth_rate = ((values[-1] - values[0]) / values[0] * 100) if values[0] != 0 else 0

        # AI洞察分析
        ai_insights = None
        if enable_ai_insights:
            try:
                if ctx:
                    await ctx.info("正在进行AI趋势洞察分析...")

                ai_prompt = f"""对以下数据趋势进行深度洞察分析：

数据表: {table}
字段: {value_column}
分析周期: {period}
数据点数量: {len(values)}
趋势方向: {trend_direction}
增长率: {growth_rate:.2f}%
平均值: {np.mean(values):.2f}
波动性: {np.std(values):.2f}

最近数据趋势:
{json.dumps(df.tail(7).to_dict('records'), indent=2, ensure_ascii=False)}

请从以下角度提供洞察：
1. 趋势模式分析（季节性、周期性、异常波动）
2. 业务含义解读（可能的业务驱动因素）
3. 风险与机会识别
4. 预测可信度评估
5. 建议的监控重点和行动建议

请用专业但易懂的中文回答，重点关注业务价值和可操作性。"""

                ai_insights = await ai_assistant.analyze(ai_prompt, temperature=0.4, max_tokens=1000)

                if ctx:
                    await ctx.info("AI趋势洞察分析完成")

            except Exception as ai_error:
                logger.warning(f"AI洞察分析失败: {ai_error}")
                ai_insights = f"AI洞察分析暂时不可用: {str(ai_error)}"

        result = {
            "success": True,
            "trend_analysis": {
                "direction": trend_direction,
                "slope": float(trend_slope),
                "growth_rate": float(growth_rate),
                "data_points": len(values)
            },
            "historical_data": df.to_dict('records'),
            "period": period
        }
        
        if ai_insights:
            result["ai_insights"] = ai_insights
            
        return result

    except Exception as e:
        error_msg = f"趋势分析失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@app.tool
async def create_smart_alert(
    alert_type: str,
    table: str,
    column: str = None,
    threshold: float = None,
    description: str = "",
    enable_ai_priority: bool = True,
    ctx: Context = None
) -> Dict[str, Any]:
    """创建智能提醒"""
    try:
        if ctx:
            await ctx.info(f"正在创建智能提醒...")

        alert_config = {
            "type": alert_type,
            "table": table,
            "column": column,
            "threshold": threshold,
            "description": description
        }

        # AI优先级评估
        ai_priority = None
        if enable_ai_priority:
            try:
                if ctx:
                    await ctx.info("正在进行AI优先级评估...")

                # 获取相关数据统计信息
                context_data = {}
                if alert_type == "value_threshold" and column:
                    try:
                        stats_query = f"""
                        SELECT
                            AVG({column}) as avg_value,
                            STDDEV({column}) as std_dev,
                            MIN({column}) as min_value,
                            MAX({column}) as max_value,
                            COUNT(*) as total_records
                        FROM {table}
                        WHERE {column} IS NOT NULL
                        """
                        stats_df = db_manager.execute_query(stats_query)
                        if not stats_df.empty:
                            context_data = stats_df.iloc[0].to_dict()
                    except Exception as stats_error:
                        logger.warning(f"获取统计信息失败: {stats_error}")

                ai_prompt = f"""作为数据监控专家，请评估以下数据提醒的优先级和重要性：

提醒配置:
- 类型: {alert_type}
- 数据表: {table}
- 字段: {column or '无'}
- 阈值: {threshold or '无'}
- 描述: {description or '无描述'}

数据背景信息:
{json.dumps(context_data, indent=2, ensure_ascii=False) if context_data else '暂无统计信息'}

请从以下角度评估：
1. 紧急程度 (高/中/低)
2. 业务影响程度 (高/中/低)
3. 误报风险评估
4. 建议的响应时间
5. 监控频率建议
6. 潜在的业务风险

请提供简洁的评估结果，包含具体的优先级建议和理由。"""

                ai_priority = await ai_assistant.analyze(ai_prompt, temperature=0.2, max_tokens=600)

                if ctx:
                    await ctx.info("AI优先级评估完成")

            except Exception as ai_error:
                logger.warning(f"AI优先级评估失败: {ai_error}")
                ai_priority = f"AI优先级评估暂时不可用: {str(ai_error)}"

        # 这里应该调用实际的提醒管理器
        alert_id = f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        result = {
            "success": True,
            "alert_id": alert_id,
            "config": alert_config
        }
        
        if ai_priority:
            result["ai_priority_assessment"] = ai_priority
            
        return result

    except Exception as e:
        error_msg = f"创建智能提醒失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

@app.tool
async def comprehensive_ai_analysis(
    table: str,
    columns: List[str] = None,
    analysis_type: str = "full",
    time_range_days: int = 30,
    ctx: Context = None
) -> Dict[str, Any]:
    """综合AI数据分析"""
    try:
        if ctx:
            await ctx.info(f"正在进行表 {table} 的综合AI分析...")

        # 获取表结构信息
        table_info_query = f"DESCRIBE {table}"
        table_structure = db_manager.execute_query(table_info_query)

        # 获取数据概览
        overview_query = f"""
        SELECT
            COUNT(*) as total_records,
            COUNT(DISTINCT DATE(created_at)) as date_range_days
        FROM {table}
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL {time_range_days} DAY)
        """
        overview_data = db_manager.execute_query(overview_query)

        # 如果没有指定列，自动识别数值列
        if not columns:
            numeric_columns = []
            for _, row in table_structure.iterrows():
                column_type = row['Type'].lower()
                if any(t in column_type for t in ['int', 'decimal', 'float', 'double', 'numeric']):
                    numeric_columns.append(row['Field'])
            columns = numeric_columns[:5]  # 限制最多5列

        # 收集各列的统计信息
        analysis_data = {
            "table": table,
            "time_range_days": time_range_days,
            "total_records": int(overview_data.iloc[0]['total_records']) if not overview_data.empty else 0,
            "columns_analyzed": columns,
            "column_statistics": {}
        }

        for column in columns:
            try:
                stats_query = f"""
                SELECT
                    AVG({column}) as avg_value,
                    STDDEV({column}) as std_dev,
                    MIN({column}) as min_value,
                    MAX({column}) as max_value,
                    COUNT(DISTINCT {column}) as unique_values,
                    COUNT(CASE WHEN {column} IS NULL THEN 1 END) as null_count
                FROM {table}
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL {time_range_days} DAY)
                """
                stats_df = db_manager.execute_query(stats_query)
                if not stats_df.empty:
                    analysis_data["column_statistics"][column] = stats_df.iloc[0].to_dict()
            except Exception as col_error:
                logger.warning(f"分析列 {column} 失败: {col_error}")

        # 使用OpenAI进行综合分析
        if ctx:
            await ctx.info("正在生成AI综合分析报告...")

        ai_prompt = f"""作为高级数据分析师，请对以下数据库表进行综合分析：

数据概览:
{json.dumps(analysis_data, indent=2, ensure_ascii=False, default=str)}

请根据分析类型 "{analysis_type}" 提供以下内容：

1. 数据质量评估
   - 数据完整性分析
   - 异常值识别
   - 数据分布特征

2. 业务洞察
   - 关键指标表现
   - 趋势模式识别
   - 潜在业务机会

3. 风险识别
   - 数据异常风险
   - 业务风险点
   - 监控建议

4. 行动建议
   - 优化建议
   - 监控重点
   - 进一步分析方向

请提供专业、可操作的分析报告，重点关注业务价值和实际应用。"""

        ai_analysis_report = await ai_assistant.analyze(ai_prompt, temperature=0.4, max_tokens=1500)

        if ctx:
            await ctx.info("综合AI分析完成")

        return {
            "success": True,
            "analysis_data": analysis_data,
            "ai_analysis_report": ai_analysis_report,
            "analysis_type": analysis_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        error_msg = f"综合AI分析失败: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        return {"success": False, "error": error_msg}

if __name__ == "__main__":
    print("🚀 启动OpenAI GPT-4o-mini MySQL数据分析服务器...")
    print(f"🤖 使用模型: {OPENAI_MODEL}")

    # 配置日志
    logging.basicConfig(level=logging.INFO)

    # 运行服务器
    app.run(transport="http", host="127.0.0.1", port=8084)
