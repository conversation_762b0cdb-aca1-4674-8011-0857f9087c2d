{"system": {"name": "本地MySQL数据分析MCP系统", "version": "1.0.0", "offline_mode": true, "description": "完全本地化的MySQL数据分析系统，支持语音交互"}, "llm": {"provider": "openai", "fallback_enabled": true, "providers": {"openai": {"enabled": true, "api_key": "********************************************************************************************************************************************************************", "model": "gpt-4o-mini", "base_url": "https://api.openai.com/v1", "timeout": 30, "max_retries": 3, "temperature": 0.3, "max_tokens": 1000}, "local": {"enabled": false, "base_url": "http://127.0.0.1:11434", "model": "llama3.1:8b", "timeout": 60, "max_retries": 2, "temperature": 0.3, "max_tokens": 1000, "api_type": "ollama"}, "lm_studio": {"enabled": false, "base_url": "http://127.0.0.1:1234/v1", "model": "local-model", "api_key": "lm-studio", "timeout": 60, "max_retries": 2, "temperature": 0.3, "max_tokens": 1000}}}, "database": {"host": "localhost", "port": 3306, "user": "root", "password": "123456", "database": "realtime_data", "charset": "utf8mb4", "pool_size": 20, "pool_max_overflow": 30, "pool_timeout": 30, "pool_recycle": 3600, "query_timeout": 60, "batch_size": 1000, "cache_enabled": true, "cache_ttl": 300}, "mcp_server": {"host": "127.0.0.1", "port": 9000, "transport": "http", "max_connections": 100, "request_timeout": 120, "enable_cors": true, "log_level": "INFO"}, "voice": {"enabled": true, "tts": {"engine": "pyttsx3", "voice_id": 0, "rate": 150, "volume": 0.8, "language": "zh-CN"}, "asr": {"engine": "speech_recognition", "microphone_index": null, "timeout": 5, "phrase_timeout": 1, "language": "zh-CN", "energy_threshold": 4000}, "wake_word": "数据助手", "response_audio": true}, "performance": {"enable_caching": true, "cache_size": 1000, "enable_query_optimization": true, "enable_connection_pooling": true, "max_query_time": 30, "enable_async_processing": true, "batch_processing": true, "enable_indexing_suggestions": true}, "features": {"statistics": {"enabled": true, "default_time_range": "7d", "max_records": 1000000}, "anomaly_detection": {"enabled": true, "methods": ["zscore", "iqr", "isolation_forest", "business_rules"], "default_method": "zscore", "default_threshold": 2.0, "ai_analysis": true}, "alerts": {"enabled": true, "max_alerts": 1000, "check_interval": 60, "voice_alerts": true, "ai_priority": true}, "charts": {"enabled": true, "types": ["bar", "pie", "line", "scatter", "heatmap"], "default_format": "plotly", "max_data_points": 10000}, "trend_analysis": {"enabled": true, "default_period": "day", "forecast_days": 7, "ai_insights": true}}, "logging": {"level": "INFO", "file": "local_mcp_system.log", "max_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}