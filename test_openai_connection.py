#!/usr/bin/env python3
"""
测试OpenAI连接
"""

import asyncio
import sys

try:
    from openai import AsyncOpenAI
except ImportError:
    print("❌ OpenAI库未安装，请运行: pip install openai")
    sys.exit(1)

# OpenAI配置
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
OPENAI_MODEL = "gpt-4o-mini"

async def test_openai_connection():
    """测试OpenAI连接"""
    print("🧪 测试OpenAI GPT-4o-mini连接...")
    
    try:
        client = AsyncOpenAI(api_key=OPENAI_API_KEY)
        
        response = await client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {
                    "role": "system", 
                    "content": "你是一位专业的数据分析专家。"
                },
                {
                    "role": "user", 
                    "content": "请简单介绍一下数据异常检测的常用方法。"
                }
            ],
            temperature=0.3,
            max_tokens=200
        )
        
        result = response.choices[0].message.content.strip()
        
        print("✅ OpenAI连接成功!")
        print(f"🤖 模型: {OPENAI_MODEL}")
        print(f"📝 测试响应:")
        print(f"{result}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI连接失败: {e}")
        return False

async def test_ai_assistant():
    """测试AI助手类"""
    print("\n🧪 测试AI助手类...")
    
    try:
        # 导入AI助手
        sys.path.append('.')
        from fastmcp_server import ai_assistant
        
        result = await ai_assistant.analyze(
            "请分析一下MySQL数据库中支付金额字段出现异常值的可能原因。",
            temperature=0.3,
            max_tokens=300
        )
        
        print("✅ AI助手测试成功!")
        print(f"📝 分析结果:")
        print(f"{result}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI助手测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 OpenAI GPT-4o-mini连接测试")
    print("=" * 50)
    
    results = []
    
    # 测试基础连接
    results.append(("OpenAI基础连接", await test_openai_connection()))
    
    # 测试AI助手类
    results.append(("AI助手类", await test_ai_assistant()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 OpenAI GPT-4o-mini配置正确，可以正常使用！")
    else:
        print("⚠️ 部分测试失败，请检查配置")
        print("💡 提示：")
        print("  1. 检查API密钥是否正确")
        print("  2. 检查网络连接")
        print("  3. 检查OpenAI账户余额")

if __name__ == "__main__":
    asyncio.run(main())
