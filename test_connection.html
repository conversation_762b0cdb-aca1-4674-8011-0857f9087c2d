<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>HTTP桥接器连接测试</h1>
    
    <div>
        <button onclick="testConnection()">测试连接</button>
        <button onclick="testDatabaseInfo()">测试数据库信息</button>
        <button onclick="testAllEndpoints()">测试所有端点</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        const serverUrl = 'http://127.0.0.1:8080';
        
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testConnection() {
            addResult('开始测试连接...');
            
            try {
                const response = await fetch(`${serverUrl}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`连接成功! 状态: ${data.status}, 消息: ${data.message}`, true);
                } else {
                    addResult(`连接失败: HTTP ${response.status}`, false);
                }
            } catch (error) {
                addResult(`连接错误: ${error.message}`, false);
            }
        }
        
        async function testDatabaseInfo() {
            addResult('开始测试数据库信息...');
            
            try {
                const response = await fetch(`${serverUrl}/api/database-info`);
                
                if (response.ok) {
                    const data = await response.json();
                    const dbInfo = data.database_info;
                    addResult(`数据库信息获取成功! 数据库: ${dbInfo.database}, 表数量: ${dbInfo.table_count}`, true);
                } else {
                    addResult(`数据库信息获取失败: HTTP ${response.status}`, false);
                }
            } catch (error) {
                addResult(`数据库信息获取错误: ${error.message}`, false);
            }
        }
        
        async function testAllEndpoints() {
            addResult('开始测试所有端点...');
            
            // 测试健康检查
            await testConnection();
            
            // 测试数据库信息
            await testDatabaseInfo();
            
            // 测试统计分析
            try {
                const response = await fetch(`${serverUrl}/api/statistics`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        table: 'users',
                        column: 'age'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`统计分析成功! 平均值: ${data.data.average}`, true);
                } else {
                    addResult(`统计分析失败: HTTP ${response.status}`, false);
                }
            } catch (error) {
                addResult(`统计分析错误: ${error.message}`, false);
            }
            
            // 测试异常检测
            try {
                const response = await fetch(`${serverUrl}/api/anomaly-detection`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        table: 'orders',
                        column: 'amount',
                        method: 'zscore',
                        threshold: 2.0
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`异常检测成功! 异常数量: ${data.data.anomaly_count}`, true);
                } else {
                    addResult(`异常检测失败: HTTP ${response.status}`, false);
                }
            } catch (error) {
                addResult(`异常检测错误: ${error.message}`, false);
            }
            
            addResult('所有端点测试完成!', true);
        }
        
        // 页面加载时自动测试连接
        window.onload = function() {
            addResult('页面加载完成，开始自动测试连接...');
            testConnection();
        };
    </script>
</body>
</html>
