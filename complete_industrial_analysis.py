#!/usr/bin/env python3
"""
完整的工业数据分析示例
展示所有功能
"""

import asyncio
import json
from fastmcp import FastMCP, Client
import mysql.connector
import pandas as pd
import numpy as np

# 创建工业数据分析服务器
mcp = FastMCP("工业数据分析系统")

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'realtime_data',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return mysql.connector.connect(**DB_CONFIG)

@mcp.tool()
def get_system_status() -> dict:
    """获取系统状态"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM industrial_monitoring")
        record_count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        return {
            "status": "运行正常",
            "database": "已连接",
            "total_records": record_count,
            "message": "工业数据分析系统运行正常"
        }
    except Exception as e:
        return {
            "status": "错误",
            "error": str(e)
        }

@mcp.tool()
def analyze_pressure_data() -> dict:
    """分析压力数据"""
    try:
        connection = get_db_connection()
        
        query = """
        SELECT 
            record_time,
            pressure_1, pressure_2, pressure_3, pressure_4, pressure_5, pressure_7
        FROM industrial_monitoring 
        ORDER BY record_time
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        # 计算统计信息
        pressure_cols = ['pressure_1', 'pressure_2', 'pressure_3', 'pressure_4', 'pressure_5', 'pressure_7']
        
        analysis = {}
        for col in pressure_cols:
            if col in df.columns:
                analysis[col] = {
                    "平均值": float(df[col].mean()),
                    "最大值": float(df[col].max()),
                    "最小值": float(df[col].min()),
                    "标准差": float(df[col].std())
                }
        
        return {
            "success": True,
            "message": "压力数据分析完成",
            "total_records": len(df),
            "analysis": analysis
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def analyze_temperature_data() -> dict:
    """分析温度数据"""
    try:
        connection = get_db_connection()
        
        query = """
        SELECT 
            record_time,
            temperature_1, temperature_2, temperature_3, temperature_4, temperature_5, temperature_6
        FROM industrial_monitoring 
        ORDER BY record_time
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        # 计算统计信息
        temp_cols = ['temperature_1', 'temperature_2', 'temperature_3', 'temperature_4', 'temperature_5', 'temperature_6']
        
        analysis = {}
        for col in temp_cols:
            if col in df.columns:
                analysis[col] = {
                    "平均值": float(df[col].mean()),
                    "最大值": float(df[col].max()),
                    "最小值": float(df[col].min()),
                    "标准差": float(df[col].std())
                }
        
        return {
            "success": True,
            "message": "温度数据分析完成",
            "total_records": len(df),
            "analysis": analysis
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def detect_pressure_anomalies(threshold: float = 2.0) -> dict:
    """检测压力异常"""
    try:
        connection = get_db_connection()
        
        query = """
        SELECT id, record_time, pressure_1
        FROM industrial_monitoring 
        WHERE pressure_1 IS NOT NULL
        ORDER BY record_time
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        if len(df) == 0:
            return {"success": False, "error": "没有压力数据"}
        
        # 计算Z-score
        mean_val = df['pressure_1'].mean()
        std_val = df['pressure_1'].std()
        
        if std_val == 0:
            return {
                "success": True,
                "message": "压力数据无变化，无异常",
                "anomalies": []
            }
        
        df['z_score'] = (df['pressure_1'] - mean_val) / std_val
        anomalies = df[abs(df['z_score']) > threshold]
        
        anomaly_list = []
        for _, row in anomalies.iterrows():
            anomaly_list.append({
                "时间": str(row['record_time']),
                "压力值": float(row['pressure_1']),
                "Z分数": float(row['z_score']),
                "异常程度": "高" if abs(row['z_score']) > 3 else "中"
            })
        
        return {
            "success": True,
            "message": f"检测到 {len(anomalies)} 个压力异常",
            "总记录数": len(df),
            "异常数量": len(anomalies),
            "异常列表": anomaly_list,
            "统计信息": {
                "平均值": float(mean_val),
                "标准差": float(std_val)
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def get_latest_readings(count: int = 5) -> dict:
    """获取最新的监控读数"""
    try:
        if count > 20:
            count = 20  # 限制最大数量
        
        connection = get_db_connection()
        
        query = f"""
        SELECT 
            record_time,
            pressure_1, temperature_1, oxygen_content, flow_rate_1
        FROM industrial_monitoring 
        ORDER BY record_time DESC 
        LIMIT {count}
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        readings = []
        for _, row in df.iterrows():
            readings.append({
                "时间": str(row['record_time']),
                "压力1": float(row['pressure_1']) if pd.notna(row['pressure_1']) else None,
                "温度1": float(row['temperature_1']) if pd.notna(row['temperature_1']) else None,
                "含氧量": float(row['oxygen_content']) if pd.notna(row['oxygen_content']) else None,
                "流量1": float(row['flow_rate_1']) if pd.notna(row['flow_rate_1']) else None
            })
        
        return {
            "success": True,
            "message": f"获取最新 {len(readings)} 条监控读数",
            "readings": readings
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

async def run_complete_analysis():
    """运行完整的数据分析"""
    print("🏭 工业数据分析系统演示")
    print("=" * 50)
    
    # 创建客户端
    client = Client(mcp)
    
    try:
        async with client:
            print("✅ 系统连接成功")
            
            # 1. 获取系统状态
            print("\n📊 1. 系统状态检查")
            result = await client.call_tool("get_system_status", {})
            print(f"   {json.dumps(result.data, indent=2, ensure_ascii=False)}")
            
            # 2. 获取最新读数
            print("\n📈 2. 最新监控读数")
            result = await client.call_tool("get_latest_readings", {"count": 3})
            print(f"   {json.dumps(result.data, indent=2, ensure_ascii=False)}")
            
            # 3. 分析压力数据
            print("\n🔧 3. 压力数据分析")
            result = await client.call_tool("analyze_pressure_data", {})
            print(f"   {json.dumps(result.data, indent=2, ensure_ascii=False)}")
            
            # 4. 分析温度数据
            print("\n🌡️ 4. 温度数据分析")
            result = await client.call_tool("analyze_temperature_data", {})
            print(f"   {json.dumps(result.data, indent=2, ensure_ascii=False)}")
            
            # 5. 检测压力异常
            print("\n⚠️ 5. 压力异常检测")
            result = await client.call_tool("detect_pressure_anomalies", {"threshold": 2.0})
            print(f"   {json.dumps(result.data, indent=2, ensure_ascii=False)}")
            
            print("\n🎉 完整分析完成！")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_complete_analysis())
