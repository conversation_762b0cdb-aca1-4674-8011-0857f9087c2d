#!/usr/bin/env python3
"""
检查payment表的日期范围
"""

import asyncio
import sys

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    sys.exit(1)

async def check_payment_dates():
    """检查payment表的日期范围"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("📅 检查payment表的日期范围")
    print("=" * 40)
    
    try:
        async with Client(server_url) as client:
            
            # 检查payment表的日期范围
            result = await client.call_tool("execute_custom_sql", {
                "sql": "SELECT MIN(payment_date) as min_date, MAX(payment_date) as max_date, COUNT(*) as total_count FROM payment"
            })
            
            if result.data.get('success'):
                data = result.data.get('data', {})
                rows = data.get('rows', [])
                if rows:
                    row = rows[0]
                    print(f"最早日期: {row.get('min_date')}")
                    print(f"最晚日期: {row.get('max_date')}")
                    print(f"总记录数: {row.get('total_count')}")
                    
                    # 检查最近30天的数据
                    result2 = await client.call_tool("execute_custom_sql", {
                        "sql": "SELECT COUNT(*) as recent_count FROM payment WHERE payment_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
                    })
                    
                    if result2.data.get('success'):
                        data2 = result2.data.get('data', {})
                        rows2 = data2.get('rows', [])
                        if rows2:
                            recent_count = rows2[0].get('recent_count', 0)
                            print(f"最近30天记录数: {recent_count}")
                            
                            if recent_count == 0:
                                print("❌ 最近30天没有数据，这就是趋势图为空的原因")
                                print("💡 建议使用更大的时间范围或者不使用时间过滤")
                                
                                # 测试不使用时间过滤的查询
                                result3 = await client.call_tool("execute_custom_sql", {
                                    "sql": "SELECT DATE(payment_date) as date_group, AVG(amount) as avg_value FROM payment GROUP BY DATE(payment_date) ORDER BY DATE(payment_date) LIMIT 10"
                                })
                                
                                if result3.data.get('success'):
                                    data3 = result3.data.get('data', {})
                                    rows3 = data3.get('rows', [])
                                    print(f"\n📊 不使用时间过滤的查询结果 (前10条):")
                                    for i, row in enumerate(rows3[:5], 1):
                                        print(f"   {i}. {row.get('date_group')}: ${row.get('avg_value', 0):.2f}")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(check_payment_dates())
    except KeyboardInterrupt:
        print("\n⏹️  检查被用户中断")
    except Exception as e:
        print(f"\n❌ 检查执行错误: {e}")
