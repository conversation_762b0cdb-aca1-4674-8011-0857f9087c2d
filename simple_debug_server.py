#!/usr/bin/env python3
"""
简化的调试服务器
用于测试MCP连接问题
"""

from fastmcp import FastMCP
import json

# 创建MCP应用
mcp = FastMCP("简化调试服务器")

@mcp.tool()
def hello() -> str:
    """简单的问候工具"""
    print("🔧 [MCP DEBUG] hello工具被调用")
    return "Hello from Debug Server!"

@mcp.tool()
def debug_info() -> dict:
    """返回调试信息"""
    print("🔧 [MCP DEBUG] debug_info工具被调用")
    import datetime
    return {
        "server": "简化调试服务器",
        "time": datetime.datetime.now().isoformat(),
        "status": "running"
    }

if __name__ == "__main__":
    print("🚀 启动简化调试服务器")
    print("=" * 40)
    print("📍 服务器地址: http://127.0.0.1:9002/mcp")
    print("🔧 可用工具:")
    print("  - hello")
    print("  - debug_info")
    print("=" * 40)
    print("🎯 等待客户端连接...")
    
    try:
        print("🔧 [DEBUG] 启动参数:")
        print(f"  transport: http")
        print(f"  host: 127.0.0.1")
        print(f"  port: 9002")
        print(f"  path: /mcp")
        
        mcp.run(transport="http", host="127.0.0.1", port=9002, path="/mcp")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
