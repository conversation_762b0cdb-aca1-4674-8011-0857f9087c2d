#!/usr/bin/env python3
"""
简单测试
"""

print("🔍 Python环境测试")
print("=" * 30)

try:
    import fastmcp
    print(f"✅ FastMCP版本: {fastmcp.__version__}")
except Exception as e:
    print(f"❌ FastMCP导入失败: {e}")

try:
    from fastmcp import FastMCP
    print("✅ FastMCP类导入成功")
    
    mcp = FastMCP("测试")
    print("✅ FastMCP实例创建成功")
    
except Exception as e:
    print(f"❌ FastMCP创建失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🔍 数据库连接测试")
try:
    import mysql.connector
    print("✅ MySQL连接器可用")
    
    # 测试连接
    connection = mysql.connector.connect(
        host='localhost',
        port=3306,
        user='root',
        password='123456',
        database='realtime_data',
        connect_timeout=5
    )
    print("✅ 数据库连接成功")
    connection.close()
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")

print("\n🎉 测试完成")
