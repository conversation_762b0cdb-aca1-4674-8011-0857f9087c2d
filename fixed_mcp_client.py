#!/usr/bin/env python3
"""
修复的MCP客户端
使用正确的FastMCP Client API
"""

import asyncio
import json
from typing import Dict, Any
from fastmcp import Client

class FixedMCPClient:
    """修复的MCP客户端"""
    
    def __init__(self, server_url: str = "http://127.0.0.1:9000/mcp/"):
        self.server_url = server_url
        self.client = None
    
    def create_client(self):
        """创建客户端"""
        try:
            print(f"🔗 创建客户端连接到: {self.server_url}")
            self.client = Client(self.server_url)
            print("✅ 客户端创建成功")
            return True
        except Exception as e:
            print(f"❌ 客户端创建失败: {e}")
            return False
    
    async def test_connection(self):
        """测试连接"""
        if not self.client:
            print("❌ 客户端未创建")
            return False
        
        try:
            async with self.client:
                print("✅ 连接成功")
                
                # 测试ping
                await self.client.ping()
                print("✅ Ping成功")
                
                # 列出工具
                tools = await self.client.list_tools()
                print(f"📋 可用工具: {[tool.name for tool in tools]}")
                
                return True
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False
    
    async def call_tool_safe(self, tool_name: str, arguments: Dict[str, Any] = None):
        """安全调用工具"""
        if not self.client:
            print("❌ 客户端未创建")
            return None
        
        if arguments is None:
            arguments = {}
        
        try:
            async with self.client:
                print(f"🔄 调用工具: {tool_name}")
                print(f"📝 参数: {arguments}")
                
                result = await self.client.call_tool(tool_name, arguments)
                
                print("✅ 调用成功")
                print(f"📊 结果: {json.dumps(result.data, indent=2, ensure_ascii=False)}")
                return result.data
                
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    async def interactive_mode(self):
        """交互模式"""
        print("🎯 修复的MCP客户端")
        print("=" * 40)
        
        if not self.create_client():
            return
        
        # 测试连接
        if not await self.test_connection():
            print("❌ 连接测试失败")
            return
        
        print("\n💡 可用命令:")
        print("  1. status - 系统状态")
        print("  2. summary - 数据摘要")
        print("  3. avg <column> - 计算平均值")
        print("  4. anomaly <column> - 检测异常")
        print("  5. recent <count> - 最近数据")
        print("  6. tools - 列出工具")
        print("  7. quit - 退出")
        
        while True:
            try:
                command = input("\n📝 请输入命令: ").strip().lower()
                
                if command in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                elif command == 'status':
                    await self.call_tool_safe("get_system_status")
                
                elif command == 'summary':
                    await self.call_tool_safe("get_data_summary")
                
                elif command.startswith('avg '):
                    column = command.split(' ', 1)[1]
                    await self.call_tool_safe("calculate_average", {"column": column})
                
                elif command.startswith('anomaly '):
                    column = command.split(' ', 1)[1]
                    await self.call_tool_safe("detect_anomalies", {"column": column, "threshold": 2.0})
                
                elif command.startswith('recent '):
                    try:
                        count = int(command.split(' ', 1)[1])
                        await self.call_tool_safe("get_recent_data", {"limit": count})
                    except ValueError:
                        print("❌ 请输入有效数字")
                
                elif command == 'tools':
                    await self.list_tools()
                
                else:
                    print("❌ 未知命令，请输入有效命令")
            
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    async def list_tools(self):
        """列出工具"""
        if not self.client:
            print("❌ 客户端未创建")
            return
        
        try:
            async with self.client:
                tools = await self.client.list_tools()
                print("\n📋 可用工具:")
                for tool in tools:
                    print(f"  🔧 {tool.name}: {tool.description}")
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
    
    async def demo_analysis(self):
        """演示分析"""
        print("🎯 工业数据分析演示")
        print("=" * 40)
        
        if not self.create_client():
            return
        
        # 测试连接
        if not await self.test_connection():
            return
        
        print("\n📊 开始演示分析...")
        
        # 1. 系统状态
        print("\n1️⃣ 系统状态检查")
        await self.call_tool_safe("get_system_status")
        
        # 2. 数据摘要
        print("\n2️⃣ 数据摘要")
        await self.call_tool_safe("get_data_summary")
        
        # 3. 压力分析
        print("\n3️⃣ 压力1平均值")
        await self.call_tool_safe("calculate_average", {"column": "pressure_1"})
        
        # 4. 异常检测
        print("\n4️⃣ 压力1异常检测")
        await self.call_tool_safe("detect_anomalies", {"column": "pressure_1", "threshold": 2.0})
        
        # 5. 最近数据
        print("\n5️⃣ 最近5条数据")
        await self.call_tool_safe("get_recent_data", {"limit": 5})
        
        print("\n🎉 演示完成！")

async def main():
    """主函数"""
    import sys
    
    client = FixedMCPClient()
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        await client.demo_analysis()
    else:
        await client.interactive_mode()

if __name__ == "__main__":
    asyncio.run(main())
