#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API响应格式
"""

import requests
import json

def test_api_responses():
    """测试所有API端点的响应格式"""
    base_url = "http://127.0.0.1:8080"
    
    print("=" * 60)
    print("测试API响应格式")
    print("=" * 60)
    
    # 1. 测试数据库信息
    print("\n1. 测试数据库信息 API")
    try:
        response = requests.get(f"{base_url}/api/database-info")
        data = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   响应格式: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 2. 测试统计分析
    print("\n2. 测试统计分析 API")
    try:
        payload = {"table": "payment", "column": "amount"}
        response = requests.post(f"{base_url}/api/statistics", json=payload)
        data = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   响应格式: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 3. 测试异常检测
    print("\n3. 测试异常检测 API")
    try:
        payload = {
            "table": "payment", 
            "column": "amount", 
            "method": "zscore", 
            "threshold": 2.0
        }
        response = requests.post(f"{base_url}/api/anomaly-detection", json=payload)
        data = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   响应格式: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 4. 测试图表生成
    print("\n4. 测试图表生成 API")
    try:
        payload = {
            "chart_type": "bar",
            "table": "film",
            "x_column": "rating",
            "y_column": "rental_rate",
            "title": "测试图表"
        }
        response = requests.post(f"{base_url}/api/generate-chart", json=payload)
        data = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   响应格式: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 5. 测试SQL执行
    print("\n5. 测试SQL执行 API")
    try:
        payload = {"query": "SELECT * FROM users LIMIT 3"}
        response = requests.post(f"{base_url}/api/execute-sql", json=payload)
        data = response.json()
        print(f"   状态码: {response.status_code}")
        print(f"   响应格式: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   错误: {e}")

def main():
    """主函数"""
    test_api_responses()
    
    print("\n" + "=" * 60)
    print("API格式测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
