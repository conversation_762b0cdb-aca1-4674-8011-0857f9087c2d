#!/bin/bash

echo "========================================"
echo "MySQL数据库分析MCP服务器安装脚本"
echo "========================================"

# 检查Python环境
echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3环境，请先安装Python 3.8+"
    exit 1
fi

python3 --version

# 创建虚拟环境
echo ""
echo "创建虚拟环境..."
python3 -m venv venv
if [ $? -ne 0 ]; then
    echo "错误: 创建虚拟环境失败"
    exit 1
fi

# 激活虚拟环境
echo ""
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo ""
echo "升级pip..."
python -m pip install --upgrade pip

# 安装依赖包
echo ""
echo "安装依赖包..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "错误: 安装依赖包失败"
    exit 1
fi

# 检查配置文件
echo ""
echo "检查数据库配置..."
if [ ! -f "db_config.json" ]; then
    echo "警告: 未找到db_config.json，请配置数据库连接信息"
fi

echo ""
echo "========================================"
echo "安装完成！"
echo "========================================"
echo ""
echo "使用说明:"
echo "1. 编辑 db_config.json 配置数据库连接"
echo "2. 运行 ./start.sh 启动服务器"
echo "3. 或者直接运行: python mysql_analysis_mcp.py"
echo ""
