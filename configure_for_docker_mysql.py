#!/usr/bin/env python3
"""
为Docker MySQL配置系统
"""

import json
import os

def update_config_for_docker_mysql():
    """更新配置文件以连接Docker MySQL"""
    print("🐳 配置系统连接到Docker MySQL...")
    
    # 读取现有配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("❌ 配置文件不存在，请先运行: python install_local_system.py")
        return False
    
    # 更新数据库配置
    config['database'] = {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "123456",
        "database": "realtime_data",
        "charset": "utf8mb4",
        "pool_size": 20,
        "pool_max_overflow": 30,
        "pool_timeout": 30,
        "pool_recycle": 3600,
        "query_timeout": 60,
        "batch_size": 1000,
        "cache_enabled": True,
        "cache_ttl": 300
    }
    
    # 保存配置
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print("✅ 配置文件已更新")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def test_connection():
    """测试数据库连接"""
    print("🧪 测试数据库连接...")
    
    try:
        import mysql.connector
        
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='realtime_data',
            charset='utf8mb4',
            connect_timeout=5
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM payment")
        count = cursor.fetchone()[0]
        
        cursor.close()
        connection.close()
        
        print(f"✅ 数据库连接成功！支付表中有 {count} 条记录")
        return True
        
    except mysql.connector.Error as e:
        if e.errno == 1049:  # Unknown database
            print("⚠️ 数据库 'realtime_data' 不存在")
            print("💡 请在MySQL Workbench中执行 setup_sample_data.sql 脚本")
            return False
        else:
            print(f"❌ 数据库连接失败: {e}")
            return False
    except ImportError:
        print("❌ mysql-connector-python 未安装")
        print("💡 请运行: pip install mysql-connector-python")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*50)
    print("🎉 Docker MySQL配置完成！")
    print("="*50)
    
    print("\n📋 下一步:")
    print("1. 确保已在MySQL Workbench中执行了 setup_sample_data.sql")
    print("2. 启动MCP服务器:")
    print("   python local_mcp_server.py")
    print("\n3. 启动MCP客户端:")
    print("   python local_mcp_client.py")
    
    print("\n🧪 测试功能:")
    print("  - 统计分析: 计算payment表amount字段的总和")
    print("  - 异常检测: 检测payment表中的异常金额")
    print("  - 图表生成: 生成支付金额的柱状图")
    print("  - 趋势分析: 分析支付趋势")
    print("  - 语音交互: 使用语音命令")
    
    print("\n📊 示例数据:")
    print("  - payment: 支付记录（包含异常值）")
    print("  - users: 用户信息")
    print("  - orders: 订单数据")
    print("  - sales_data: 销售趋势数据")
    
    print("\n🎤 语音命令示例:")
    print("  '统计payment表amount字段的总和'")
    print("  '检测payment表的异常数据'")
    print("  '生成payment表的图表'")
    print("  '分析销售趋势'")

def main():
    """主函数"""
    print("🐳 Docker MySQL配置助手")
    print("=" * 40)
    
    print("📋 你的Docker MySQL配置:")
    print("  主机: localhost")
    print("  端口: 3306")
    print("  用户: root")
    print("  密码: 123456")
    print("  数据库: realtime_data (将创建)")
    
    # 更新配置
    if not update_config_for_docker_mysql():
        return
    
    # 测试连接
    if test_connection():
        show_next_steps()
    else:
        print("\n💡 解决方案:")
        print("1. 确保Docker MySQL正在运行")
        print("2. 在MySQL Workbench中执行以下步骤:")
        print("   - 打开 setup_sample_data.sql 文件")
        print("   - 点击执行按钮（闪电图标）")
        print("   - 等待执行完成")
        print("3. 重新运行此脚本测试连接")
        
        print("\n🔧 或者手动创建数据库:")
        print("   CREATE DATABASE realtime_data;")

if __name__ == "__main__":
    main()
