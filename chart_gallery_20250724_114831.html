
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL数据分析图表展示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        .chart-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: #f9f9f9;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }
        .chart-type {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            display: inline-block;
            margin-bottom: 15px;
        }
        .chart-image {
            width: 100%;
            height: auto;
            border-radius: 8px;
            border: 1px solid #ccc;
        }
        .chart-info {
            margin-top: 15px;
            font-size: 0.9em;
            color: #666;
        }
        .timestamp {
            text-align: center;
            color: #888;
            margin-top: 30px;
            font-style: italic;
        }
        .stats {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 MySQL数据分析图表展示</h1>
        
        <div class="stats">
            <strong>📊 生成统计:</strong> 共生成 2 个图表 | 
            <strong>🕒 生成时间:</strong> 2025-07-24 11:48:31
        </div>
        
        <div class="chart-grid">

            <div class="chart-card">
                <div class="chart-type">柱状图</div>
                <div class="chart-title">电影评级与租赁费用分析</div>
                <img src="bar_chart_20250724_114830.png" alt="电影评级与租赁费用分析" class="chart-image">
                <div class="chart-info">
                    📁 文件: bar_chart_20250724_114830.png<br>
                    📈 数据点: 10 个
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-type">饼状图</div>
                <div class="chart-title">电影评级分布</div>
                <img src="pie_chart_20250724_114831.png" alt="电影评级分布" class="chart-image">
                <div class="chart-info">
                    📁 文件: pie_chart_20250724_114831.png<br>
                    📈 数据点: 5 个
                </div>
            </div>

        </div>
        
        <div class="timestamp">
            🚀 由 MySQL数据分析MCP服务器 生成 | 
            🔗 服务器地址: http://127.0.0.1:9000/mcp/
        </div>
    </div>
</body>
</html>
