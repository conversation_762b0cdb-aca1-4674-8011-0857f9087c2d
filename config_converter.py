#!/usr/bin/env python3
"""
配置转换工具
将开源mcp-mysql-server的YAML配置转换为我们项目的JSON配置
"""

import json
import yaml
import os
from typing import Dict, Any

def load_yaml_config(yaml_file: str) -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        with open(yaml_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载YAML配置失败: {e}")
        return {}

def convert_datasource_config(yaml_config: Dict[str, Any]) -> Dict[str, Any]:
    """转换数据源配置"""
    if 'datasource' not in yaml_config or 'datasources' not in yaml_config['datasource']:
        return {}
    
    datasources = yaml_config['datasource']['datasources']
    
    # 找到默认数据源
    default_ds = None
    for name, config in datasources.items():
        if config.get('default', False):
            default_ds = config
            break
    
    # 如果没有标记默认的，使用第一个
    if not default_ds and datasources:
        default_ds = list(datasources.values())[0]
    
    if not default_ds:
        return {}
    
    # 解析JDBC URL
    url = default_ds.get('url', '')
    host = 'localhost'
    port = 3306
    database = ''
    
    if url.startswith('jdbc:mysql://'):
        # 解析 *******************************
        url_part = url.replace('jdbc:mysql://', '')
        if '/' in url_part:
            host_port, database = url_part.split('/', 1)
            if '?' in database:
                database = database.split('?')[0]
            
            if ':' in host_port:
                host, port_str = host_port.split(':')
                try:
                    port = int(port_str)
                except ValueError:
                    port = 3306
            else:
                host = host_port
    
    # 转换为我们的配置格式
    converted_config = {
        "host": host,
        "port": port,
        "user": default_ds.get('username', 'root'),
        "password": default_ds.get('password', ''),
        "database": database,
        "pool_name": "mysql_analysis_pool",
        "pool_size": 10,
        "pool_reset_session": True,
        "charset": "utf8mb4",
        "use_unicode": True,
        "autocommit": True,
        "connection_timeout": 30,
        "read_timeout": 30,
        "write_timeout": 30
    }
    
    return converted_config

def main():
    """主函数"""
    print("=" * 60)
    print("配置转换工具 - 开源mcp-mysql-server -> 我们的分析服务器")
    print("=" * 60)
    
    # 检查开源项目的配置文件
    yaml_file = "mcp-mysql-server/src/main/resources/datasource.yml"
    
    if not os.path.exists(yaml_file):
        print(f"❌ 未找到开源项目配置文件: {yaml_file}")
        print("\n请确保:")
        print("1. 开源项目在正确的位置")
        print("2. 配置文件存在且可读")
        return
    
    print(f"📁 找到配置文件: {yaml_file}")
    
    # 加载YAML配置
    yaml_config = load_yaml_config(yaml_file)
    if not yaml_config:
        print("❌ 配置文件为空或格式错误")
        return
    
    print("✅ YAML配置加载成功")
    
    # 转换配置
    json_config = convert_datasource_config(yaml_config)
    if not json_config:
        print("❌ 配置转换失败，请检查YAML格式")
        return
    
    print("✅ 配置转换成功")
    
    # 显示转换结果
    print("\n📊 转换结果:")
    print(f"  数据库主机: {json_config['host']}")
    print(f"  端口: {json_config['port']}")
    print(f"  用户名: {json_config['user']}")
    print(f"  数据库: {json_config['database']}")
    
    # 保存转换后的配置
    output_file = "db_config_converted.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_config, f, indent=2, ensure_ascii=False)
        print(f"\n💾 配置已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return
    
    # 询问是否替换现有配置
    if os.path.exists("db_config.json"):
        replace = input("\n❓ 发现现有配置文件 db_config.json，是否替换? [y/N]: ").strip().lower()
        if replace in ['y', 'yes', '是']:
            try:
                os.rename(output_file, "db_config.json")
                print("✅ 配置文件已替换")
            except Exception as e:
                print(f"❌ 替换失败: {e}")
        else:
            print(f"📝 转换后的配置保存在: {output_file}")
    else:
        try:
            os.rename(output_file, "db_config.json")
            print("✅ 配置文件已创建: db_config.json")
        except Exception as e:
            print(f"❌ 重命名失败: {e}")
    
    print("\n🎉 配置转换完成！")
    print("\n📋 后续步骤:")
    print("1. 检查转换后的配置是否正确")
    print("2. 运行 python mysql_analysis_mcp.py 启动分析服务器")
    print("3. 两个服务器可以同时运行，提供不同的功能")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n转换已取消")
    except Exception as e:
        print(f"\n❌ 转换过程中发生错误: {e}")
