#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP桥接器调试测试脚本
"""

import asyncio
import json
import requests
import time
from pathlib import Path

def test_bridge_endpoints():
    """测试HTTP桥接器的各个端点"""
    base_url = "http://127.0.0.1:8080"
    
    print("=" * 60)
    print("HTTP桥接器端点测试")
    print("=" * 60)
    
    # 测试根路径
    print("\n1. 测试根路径 (/)")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 测试健康检查
    print("\n2. 测试健康检查 (/health)")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误响应: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 测试工具列表
    print("\n3. 测试工具列表 (/mcp/tools)")
    try:
        response = requests.get(f"{base_url}/mcp/tools", timeout=10)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 测试数据库信息
    print("\n4. 测试数据库信息 (/api/database-info)")
    try:
        response = requests.get(f"{base_url}/api/database-info", timeout=30)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")

def check_bridge_process():
    """检查HTTP桥接器进程是否运行"""
    print("\n检查HTTP桥接器进程...")
    
    try:
        response = requests.get("http://127.0.0.1:8080/", timeout=5)
        if response.status_code == 200:
            print("✓ HTTP桥接器正在运行")
            return True
        else:
            print(f"✗ HTTP桥接器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ HTTP桥接器未运行或无法连接")
        return False
    except Exception as e:
        print(f"✗ 检查HTTP桥接器时出错: {e}")
        return False

def check_files():
    """检查必要文件"""
    print("检查必要文件...")
    
    files = [
        "mysql_analysis_mcp.py",
        "mcp_http_bridge.py",
        "db_config.json"
    ]
    
    all_exist = True
    for file in files:
        if Path(file).exists():
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - 文件不存在")
            all_exist = False
    
    return all_exist

def check_mcp_server():
    """检查MCP服务器是否可以直接调用"""
    print("\n检查MCP服务器直接调用...")
    
    try:
        import subprocess
        import sys
        
        # 创建测试脚本
        test_script = '''
import asyncio
import json
from fastmcp import Client

async def test():
    try:
        client = Client("mysql_analysis_mcp.py")
        async with client:
            result = await client.call_tool("get_database_info", {})
            print("SUCCESS: MCP服务器可以直接调用")
            return True
    except Exception as e:
        print(f"ERROR: MCP服务器调用失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test())
'''
        
        # 执行测试
        process = subprocess.run(
            [sys.executable, "-c", test_script],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print(f"返回码: {process.returncode}")
        print(f"输出: {process.stdout}")
        if process.stderr:
            print(f"错误: {process.stderr}")
        
        return process.returncode == 0
        
    except Exception as e:
        print(f"测试MCP服务器时出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("HTTP桥接器调试工具")
    print("=" * 60)
    
    # 检查文件
    if not check_files():
        print("\n文件检查失败，请确保所有必要文件存在")
        return
    
    # 检查MCP服务器
    print("\n" + "=" * 60)
    if not check_mcp_server():
        print("\nMCP服务器测试失败，请检查:")
        print("1. FastMCP是否正确安装")
        print("2. MySQL数据库是否运行")
        print("3. 数据库配置是否正确")
        return
    
    # 检查HTTP桥接器
    print("\n" + "=" * 60)
    if not check_bridge_process():
        print("\nHTTP桥接器未运行，请先启动:")
        print("python mcp_http_bridge.py")
        return
    
    # 测试端点
    print("\n" + "=" * 60)
    test_bridge_endpoints()
    
    print("\n" + "=" * 60)
    print("调试测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
