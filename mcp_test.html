<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 MCP连接测试</h1>
        
        <div class="status info">
            <strong>MCP服务器:</strong> http://127.0.0.1:9003/mcp
        </div>
        
        <div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="testHello()">测试Hello工具</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <h3>连接日志:</h3>
        <div id="log"></div>
    </div>

    <script>
        const mcpUrl = 'http://127.0.0.1:9003/mcp';
        let sessionId = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function testConnection() {
            log('🔄 开始连接测试...');
            
            try {
                // 1. 初始化MCP
                const initPayload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": { "tools": {} },
                        "clientInfo": { "name": "测试客户端", "version": "1.0.0" }
                    }
                };

                log('📤 发送初始化请求...');
                const response = await fetch(mcpUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream'
                    },
                    body: JSON.stringify(initPayload)
                });

                log(`📥 初始化响应: ${response.status} ${response.statusText}`);
                
                // 获取会话ID
                sessionId = response.headers.get('mcp-session-id');
                if (sessionId) {
                    log(`🔑 会话ID: ${sessionId}`);
                }

                if (response.ok) {
                    const text = await response.text();
                    log(`📥 响应内容: ${text.substring(0, 200)}...`);
                    
                    // 2. 发送initialized通知
                    log('📤 发送initialized通知...');
                    const notifyResponse = await fetch(mcpUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json, text/event-stream',
                            'mcp-session-id': sessionId
                        },
                        body: JSON.stringify({
                            "jsonrpc": "2.0",
                            "method": "notifications/initialized"
                        })
                    });
                    
                    log(`📥 通知响应: ${notifyResponse.status} ${notifyResponse.statusText}`);
                    log('✅ 连接测试成功！');
                } else {
                    const errorText = await response.text();
                    log(`❌ 连接失败: ${errorText}`);
                }
            } catch (error) {
                log(`❌ 连接错误: ${error.message}`);
            }
        }

        async function testHello() {
            if (!sessionId) {
                log('❌ 请先测试连接获取会话ID');
                return;
            }

            log('🔄 测试Hello工具...');
            
            try {
                const toolPayload = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/call",
                    "params": {
                        "name": "hello",
                        "arguments": {}
                    }
                };

                log('📤 调用Hello工具...');
                const response = await fetch(mcpUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream',
                        'mcp-session-id': sessionId
                    },
                    body: JSON.stringify(toolPayload)
                });

                log(`📥 工具响应: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const text = await response.text();
                    log(`📥 响应内容: ${text}`);
                    
                    // 解析SSE格式
                    const lines = text.split('\n');
                    const dataLine = lines.find(line => line.startsWith('data: '));
                    if (dataLine) {
                        const jsonData = JSON.parse(dataLine.substring(6));
                        if (jsonData.result && jsonData.result.content) {
                            const content = jsonData.result.content[0];
                            log(`✅ Hello工具结果: ${content.text}`);
                        }
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ 工具调用失败: ${errorText}`);
                }
            } catch (error) {
                log(`❌ 工具调用错误: ${error.message}`);
            }
        }

        // 页面加载时显示初始信息
        window.onload = function() {
            log('🚀 MCP连接测试页面已加载');
            log('📍 目标服务器: ' + mcpUrl);
            log('💡 点击"测试连接"开始测试');
        };
    </script>
</body>
</html>
