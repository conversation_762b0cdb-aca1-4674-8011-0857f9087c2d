#!/usr/bin/env python3
"""
测试Web系统
"""

import requests
import json
import time

def test_mcp_server():
    """测试MCP服务器"""
    print("🔍 测试MCP服务器...")

    try:
        url = "http://127.0.0.1:9002/mcp"  # 移除末尾的斜杠

        # 首先测试初始化请求
        init_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream"
        }

        print(f"📊 发送初始化请求到: {url}")
        print(f"📊 请求头: {headers}")
        print(f"📊 请求体: {json.dumps(init_payload, indent=2, ensure_ascii=False)}")

        response = requests.post(url, json=init_payload, headers=headers, timeout=10)
        print(f"📊 MCP响应状态: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"📊 MCP响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            try:
                error_text = response.text
                print(f"❌ MCP服务器响应错误: {response.status_code}")
                print(f"❌ 错误内容: {error_text}")
            except:
                print(f"❌ MCP服务器响应错误: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ MCP服务器连接失败: {e}")
        return False

def test_web_server():
    """测试Web服务器"""
    print("\n🔍 测试Web服务器...")
    
    try:
        url = "http://127.0.0.1:8080"
        response = requests.get(url, timeout=10)
        
        print(f"🌐 Web响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print(f"🌐 Web内容长度: {len(response.text)} 字符")
            return True
        else:
            print(f"❌ Web服务器响应错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web服务器连接失败: {e}")
        return False

def test_complete_flow():
    """测试完整的MCP流程"""
    print("\n🔍 测试完整的MCP流程...")

    try:
        url = "http://127.0.0.1:9002/mcp"
        session_id = None

        # 步骤1：初始化连接
        print("📊 步骤1：初始化MCP连接...")
        init_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream"
        }

        response = requests.post(url, json=init_payload, headers=headers, timeout=10)
        print(f"📊 初始化响应状态: {response.status_code}")

        if response.status_code == 200:
            session_id = response.headers.get('mcp-session-id')
            print(f"📊 获取到会话ID: {session_id}")

            # 发送initialized通知
            print("📊 发送initialized通知...")
            initialized_payload = {
                "jsonrpc": "2.0",
                "method": "notifications/initialized"
            }

            headers_with_session = headers.copy()
            if session_id:
                headers_with_session['mcp-session-id'] = session_id

            init_response = requests.post(url, json=initialized_payload, headers=headers_with_session, timeout=10)
            print(f"📊 Initialized通知响应状态: {init_response.status_code}")
        else:
            print(f"❌ 初始化失败: {response.status_code}")
            return False

        # 步骤2：列出可用工具
        print("\n📊 步骤2：列出可用工具...")
        list_tools_payload = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }

        headers_with_session = headers.copy()
        if session_id:
            headers_with_session['mcp-session-id'] = session_id

        response = requests.post(url, json=list_tools_payload, headers=headers_with_session, timeout=10)
        print(f"📊 列出工具响应状态: {response.status_code}")

        if response.status_code == 200:
            try:
                # 处理事件流响应
                text = response.text
                print(f"📊 工具列表响应: {text[:500]}...")
            except:
                print(f"📊 工具列表响应（非JSON）: {response.text[:200]}...")
        else:
            print(f"❌ 列出工具失败: {response.status_code}")
            print(f"❌ 错误内容: {response.text}")

        # 步骤3：调用hello工具
        print("\n📊 步骤3：调用hello工具...")
        hello_payload = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "hello",
                "arguments": {}
            }
        }

        headers_with_session = headers.copy()
        if session_id:
            headers_with_session['mcp-session-id'] = session_id

        response = requests.post(url, json=hello_payload, headers=headers_with_session, timeout=10)
        print(f"📊 Hello工具响应状态: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                print(f"📊 Hello工具响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"📊 Hello工具响应（非JSON）: {response.text[:200]}...")
            return True
        else:
            print(f"❌ Hello工具调用失败: {response.status_code}")
            print(f"❌ 错误内容: {response.text}")
            return False

    except Exception as e:
        print(f"❌ 完整流程测试错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 工业数据分析系统测试")
    print("=" * 40)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试MCP服务器
    mcp_ok = test_mcp_server()
    
    # 测试Web服务器
    web_ok = test_web_server()
    
    # 测试完整流程
    flow_ok = test_complete_flow()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"   MCP服务器: {'✅ 正常' if mcp_ok else '❌ 异常'}")
    print(f"   Web服务器: {'✅ 正常' if web_ok else '❌ 异常'}")
    print(f"   完整流程: {'✅ 正常' if flow_ok else '❌ 异常'}")

    if mcp_ok and web_ok and flow_ok:
        print("\n🎉 系统测试通过！")
        print("💡 可以在浏览器中打开: http://127.0.0.1:8080")
    else:
        print("\n❌ 系统测试失败！")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
