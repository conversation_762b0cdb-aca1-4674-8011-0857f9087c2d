# MySQL数据库分析MCP服务器

一个基于FastMCP框架的MySQL数据库分析服务，提供数据统计、异常检测、提醒、图表生成和趋势分析功能，支持语音交互。

## 功能特性

### 1. 数据统计分析
- 按时间段进行数据求和、求平均值
- 支持多种统计指标：计数、最大值、最小值、标准差、方差
- 灵活的时间范围查询

### 2. 异常数据检测
- Z-score方法检测异常值
- IQR（四分位距）方法检测异常值
- 自定义阈值设置
- 异常原因分析

### 3. 智能提醒系统
- 数值阈值提醒
- 时间基础提醒
- 实时监控数据变化
- 语音提醒功能

### 4. 图表生成
- 柱状图生成
- 饼状图生成
- 趋势线图生成
- 交互式图表（Plotly）
- 静态图片导出

### 5. 趋势分析
- 数据走势分析
- 简单线性预测
- 多周期聚合分析
- 趋势方向判断

### 6. 语音交互
- 文本转语音播报
- 语音识别输入
- 中文语音支持
- 实时语音反馈

## 安装部署

### 系统要求
- Python 3.8+
- MySQL 5.7+ 或 MariaDB 10.3+
- 音频设备（用于语音功能）

### Windows安装
```bash
# 1. 克隆或下载项目文件
# 2. 运行安装脚本
install.bat

# 3. 配置数据库连接
# 编辑 db_config.json 文件

# 4. 启动服务器
start.bat
```

### Linux/Mac安装
```bash
# 1. 克隆或下载项目文件
# 2. 给脚本执行权限
chmod +x install.sh start.sh

# 3. 运行安装脚本
./install.sh

# 4. 配置数据库连接
# 编辑 db_config.json 文件

# 5. 启动服务器
./start.sh
```

### 手动安装
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows: venv\Scripts\activate
# Linux/Mac: source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置数据库
# 编辑 db_config.json

# 启动服务器
python mysql_analysis_mcp.py
```

## 配置说明

### 数据库配置 (db_config.json)
```json
{
  "host": "localhost",
  "port": 3306,
  "user": "your_username",
  "password": "your_password",
  "database": "your_database",
  "pool_size": 10,
  "charset": "utf8mb4"
}
```

### 服务器配置
- 默认端口: 9000
- 默认路径: /mcp/
- 访问地址: http://127.0.0.1:9000/mcp/

## MCP工具使用

### 1. 获取统计信息
```python
# 工具名: get_database_statistics
# 参数:
{
  "table": "your_table",
  "column": "your_column",
  "start_time": "2024-01-01 00:00:00",  # 可选
  "end_time": "2024-01-31 23:59:59",    # 可选
  "time_column": "created_at"           # 可选，默认created_at
}
```

### 2. 检测异常数据
```python
# 工具名: detect_data_anomalies
# 参数:
{
  "table": "your_table",
  "column": "your_column",
  "method": "zscore",      # zscore 或 iqr
  "threshold": 2.0         # 阈值倍数
}
```

### 3. 创建提醒
```python
# 数值阈值提醒
{
  "alert_type": "value_threshold",
  "table": "your_table",
  "column": "your_column",
  "threshold": 100,
  "operator": ">",
  "description": "数值超过100时提醒"
}

# 时间提醒
{
  "alert_type": "time_based",
  "alert_time": "2024-07-17 15:30:00",
  "description": "定时提醒"
}
```

### 4. 生成图表
```python
# 柱状图
{
  "table": "your_table",
  "x_column": "category",
  "y_column": "value",
  "title": "销售数据柱状图",
  "limit": 20
}

# 饼状图
{
  "table": "your_table",
  "label_column": "category",
  "value_column": "amount",
  "title": "分类占比饼状图"
}

# 趋势图
{
  "table": "your_table",
  "x_column": "date",
  "y_column": "value",
  "title": "数据趋势图",
  "time_range": "7 DAY"
}
```

### 5. 趋势分析
```python
# 工具名: analyze_data_trend
{
  "table": "your_table",
  "time_column": "created_at",
  "value_column": "amount",
  "period": "day",          # hour, day, week, month
  "forecast_days": 7        # 预测天数
}
```

### 6. 语音交互
```python
# 语音播报
{
  "command": "数据分析完成",
  "listen_for_input": false
}

# 语音输入
{
  "command": "",
  "listen_for_input": true
}
```

## 使用示例

### 客户端连接示例
```python
import asyncio
from fastmcp import Client

async def main():
    async with Client("http://127.0.0.1:9000/mcp/") as client:
        # 获取数据库信息
        result = await client.call_tool("get_database_info", {})
        print(result)
        
        # 获取统计信息
        stats = await client.call_tool("get_database_statistics", {
            "table": "sales",
            "column": "amount",
            "start_time": "2024-01-01 00:00:00",
            "end_time": "2024-01-31 23:59:59"
        })
        print(stats)

if __name__ == "__main__":
    asyncio.run(main())
```

## 注意事项

1. **数据库权限**: 确保数据库用户有足够的查询权限
2. **大数据量**: 对于大数据量表，建议添加适当的索引和限制条件
3. **语音功能**: 需要音频设备和网络连接（用于语音识别）
4. **安全性**: 生产环境请修改默认配置，使用安全的数据库连接
5. **性能优化**: 可根据需要调整连接池大小和查询超时时间

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查db_config.json配置和数据库服务状态
2. **语音功能不工作**: 检查音频设备和相关依赖包安装
3. **图表生成失败**: 确保安装了kaleido包用于图片导出
4. **内存占用过高**: 调整连接池大小和查询数据量限制

### 日志查看
服务器运行时会输出详细日志，包括：
- 数据库连接状态
- 工具调用记录
- 错误信息
- 语音交互状态

## 扩展开发

### 添加新的分析功能
1. 在DataAnalyzer类中添加新方法
2. 使用@mcp.tool装饰器创建MCP工具
3. 添加相应的错误处理和日志记录

### 自定义图表类型
1. 在ChartGenerator类中添加新的图表生成方法
2. 支持更多的Plotly图表类型
3. 添加图表样式自定义选项

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
