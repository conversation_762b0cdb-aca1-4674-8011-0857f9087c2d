#!/usr/bin/env python3
import requests
import json
import time

# 等待系统启动
time.sleep(3)

url = 'http://127.0.0.1:8083/mcp/call-tool'
payload = {
    'tool_name': 'analyze_data_trend',
    'arguments': {
        'table': 'payment',
        'column': 'amount',
        'time_column': 'payment_date',
        'period': 'month'
    }
}

try:
    response = requests.post(url, json=payload, timeout=10)
    print(f'状态码: {response.status_code}')
    if response.status_code == 200:
        result = response.json()
        print('✅ 趋势分析成功!')
        analysis = result.get('result', {}).get('analysis', {})
        print(f'趋势方向: {analysis.get("trend_direction", "未知")}')
        print(f'增长率: {analysis.get("growth_rate", 0)}%')
        print(f'平均值: {analysis.get("avg_value", 0)}')
    else:
        print(f'❌ 失败: {response.text}')
except Exception as e:
    print(f'❌ 错误: {e}')
