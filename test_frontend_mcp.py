#!/usr/bin/env python3
"""
测试前端MCP连接 - 模拟前端请求
"""

import requests
import json

def test_frontend_mcp():
    """测试前端MCP连接"""
    print("🔍 测试前端MCP连接")
    print("=" * 40)
    
    url = "http://127.0.0.1:9003/mcp"
    print(f"📍 MCP服务器: {url}")
    
    # 1. 测试基本连接（模拟前端ping）
    print("\n1️⃣ 测试基本网络连接...")
    try:
        response = requests.get(url, headers={
            'Accept': 'application/json, text/event-stream',
            'Content-Type': 'application/json'
        }, timeout=5)
        print(f"✅ GET请求: {response.status_code} {response.reason}")
        print(f"   响应头: {dict(response.headers)}")
        if response.text:
            print(f"   响应内容: {response.text}")
    except Exception as e:
        print(f"❌ GET请求失败: {e}")
        return
    
    # 2. 测试MCP初始化（模拟前端初始化）
    print("\n2️⃣ 测试MCP初始化...")
    init_payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "工业数据分析系统",
                "version": "1.0.0"
            }
        }
    }
    
    try:
        response = requests.post(url, 
            json=init_payload,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            },
            timeout=5
        )
        print(f"✅ 初始化请求: {response.status_code} {response.reason}")
        print(f"   响应头: {dict(response.headers)}")
        if response.text:
            print(f"   响应内容: {response.text}")
            
        # 提取会话ID
        session_id = response.headers.get('mcp-session-id')
        if session_id:
            print(f"📝 会话ID: {session_id}")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 3. 发送initialized通知
    print("\n3️⃣ 发送initialized通知...")
    notification_payload = {
        "jsonrpc": "2.0",
        "method": "notifications/initialized"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream'
    }
    if session_id:
        headers['mcp-session-id'] = session_id
    
    try:
        response = requests.post(url,
            json=notification_payload,
            headers=headers,
            timeout=5
        )
        print(f"✅ 通知请求: {response.status_code} {response.reason}")
        print(f"   响应头: {dict(response.headers)}")
        if response.text:
            print(f"   响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 通知失败: {e}")
        return
    
    # 4. 测试hello工具调用
    print("\n4️⃣ 测试hello工具调用...")
    tool_payload = {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "tools/call",
        "params": {
            "name": "hello",
            "arguments": {}
        }
    }
    
    try:
        response = requests.post(url,
            json=tool_payload,
            headers=headers,
            timeout=5
        )
        print(f"✅ 工具调用: {response.status_code} {response.reason}")
        print(f"   响应头: {dict(response.headers)}")
        if response.text:
            print(f"   响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 工具调用失败: {e}")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    test_frontend_mcp()
