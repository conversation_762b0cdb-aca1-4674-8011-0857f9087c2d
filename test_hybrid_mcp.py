#!/usr/bin/env python3
"""
测试混合MCP架构
演示如何在有/无MCP客户端的情况下工作
"""

import asyncio
import requests
import json
import time

def test_enhanced_anomaly_detection():
    """测试增强的异常检测"""
    print("🧪 测试增强异常检测（混合LLM源）...")
    
    url = "http://127.0.0.1:8085/mcp/call-tool"
    payload = {
        "tool_name": "detect_data_anomalies_enhanced",
        "arguments": {
            "table": "payment",
            "column": "amount",
            "method": "zscore",
            "threshold": 2.0,
            "enable_ai_analysis": True
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 增强异常检测成功")
            
            # 检查LLM源
            llm_source = result.get('result', {}).get('llm_source', 'unknown')
            print(f"🤖 LLM源: {llm_source}")
            
            # 检查AI分析
            if 'ai_analysis' in result.get('result', {}):
                print("🧠 AI分析结果:")
                print(result['result']['ai_analysis'][:200] + "...")
            else:
                print("⚠️ 未找到AI分析结果")
                
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_enhanced_trend_analysis():
    """测试增强的趋势分析"""
    print("\n🧪 测试增强趋势分析（混合LLM源）...")
    
    url = "http://127.0.0.1:8085/mcp/call-tool"
    payload = {
        "tool_name": "analyze_data_trend_enhanced",
        "arguments": {
            "table": "payment",
            "time_column": "payment_date",
            "value_column": "amount",
            "period": "day",
            "enable_ai_insights": True
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 增强趋势分析成功")
            
            # 检查LLM源
            llm_source = result.get('result', {}).get('llm_source', 'unknown')
            print(f"🤖 LLM源: {llm_source}")
            
            # 检查AI洞察
            if 'ai_insights' in result.get('result', {}):
                print("🧠 AI洞察结果:")
                print(result['result']['ai_insights'][:200] + "...")
            else:
                print("⚠️ 未找到AI洞察结果")
                
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_server_status():
    """测试服务器状态"""
    print("🔍 检查增强MCP服务器状态...")
    
    try:
        # 尝试连接服务器
        response = requests.get("http://127.0.0.1:8085", timeout=5)
        print(f"✅ 增强MCP服务器运行中 (端口8085)")
        return True
    except requests.exceptions.ConnectionError:
        print(f"❌ 增强MCP服务器未运行 (端口8085)")
        print("💡 请先运行: python enhanced_mcp_server.py")
        return False
    except Exception as e:
        print(f"❌ 服务器检查失败: {e}")
        return False

def explain_hybrid_architecture():
    """解释混合架构"""
    print("\n" + "=" * 60)
    print("🏗️ 混合MCP架构说明")
    print("=" * 60)
    print("""
🔄 LLM调用流程：

1️⃣ 优先尝试MCP客户端
   ┌─────────────────┐    ctx.sample()    ┌─────────────────┐
   │   MCP客户端     │ ←---------------→  │   MCP服务器     │
   │ (Claude/ChatGPT)│                    │  (我们的代码)   │
   └─────────────────┘                    └─────────────────┘

2️⃣ 如果失败，回退到OpenAI API
   ┌─────────────────┐    直接API调用     ┌─────────────────┐
   │   OpenAI API    │ ←---------------→  │   MCP服务器     │
   │  (GPT-4o-mini)  │                    │  (我们的代码)   │
   └─────────────────┘                    └─────────────────┘

✅ 优点：
- 保持MCP架构完整性
- 支持标准MCP客户端
- 有OpenAI备选方案
- 工具代码无需修改
- 自动故障恢复

🎯 使用场景：
- 有MCP客户端时：使用客户端LLM
- 无MCP客户端时：自动使用OpenAI
- 客户端失败时：自动回退OpenAI
""")

def main():
    """主函数"""
    print("🚀 混合MCP架构测试")
    print("🤖 支持多种LLM源：MCP客户端 + OpenAI API")
    print("=" * 60)
    
    # 解释架构
    explain_hybrid_architecture()
    
    # 检查服务器状态
    if not test_server_status():
        return
    
    print("\n⏳ 等待服务器完全启动...")
    time.sleep(3)
    
    results = []
    
    # 测试增强功能
    results.append(("增强异常检测", test_enhanced_anomaly_detection()))
    results.append(("增强趋势分析", test_enhanced_trend_analysis()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 混合MCP架构测试结果:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 混合MCP架构工作正常！")
        print("\n💡 说明：")
        print("- 如果有MCP客户端连接，会优先使用客户端LLM")
        print("- 如果没有或失败，会自动回退到OpenAI API")
        print("- 这样既保持了MCP架构，又有可靠的备选方案")
    else:
        print("⚠️ 部分测试失败")
        print("💡 检查项目：")
        print("1. 增强MCP服务器是否运行 (python enhanced_mcp_server.py)")
        print("2. OpenAI API密钥是否有效")
        print("3. 网络连接是否正常")
        print("4. MySQL数据库是否可访问")

if __name__ == "__main__":
    main()
