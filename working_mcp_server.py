#!/usr/bin/env python3
"""
工作的MCP服务器
简化版本，专注于核心功能
"""

import mysql.connector
import pandas as pd
import json
from typing import Dict, Any
from fastmcp import FastMCP

# 创建MCP服务器
mcp = FastMCP("工业数据分析系统")

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'realtime_data',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return mysql.connector.connect(**DB_CONFIG)

@mcp.tool()
def get_system_status() -> Dict[str, Any]:
    """获取系统状态"""
    try:
        # 测试数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM industrial_monitoring")
        record_count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        return {
            "status": "运行正常",
            "database": "已连接",
            "records": record_count,
            "tools": ["get_system_status", "get_data_summary", "calculate_average", "detect_anomalies"]
        }
    except Exception as e:
        return {
            "status": "错误",
            "error": str(e)
        }

@mcp.tool()
def get_data_summary() -> Dict[str, Any]:
    """获取数据摘要"""
    try:
        connection = get_db_connection()
        
        # 获取基本统计
        query = """
        SELECT 
            COUNT(*) as total_records,
            MIN(record_time) as earliest_time,
            MAX(record_time) as latest_time,
            AVG(pressure_1) as avg_pressure_1,
            AVG(temperature_1) as avg_temperature_1,
            AVG(oxygen_content) as avg_oxygen
        FROM industrial_monitoring
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        result = df.iloc[0].to_dict()
        
        # 转换时间格式
        if result['earliest_time']:
            result['earliest_time'] = str(result['earliest_time'])
        if result['latest_time']:
            result['latest_time'] = str(result['latest_time'])
        
        return {
            "success": True,
            "summary": result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def calculate_average(column: str) -> Dict[str, Any]:
    """计算指定列的平均值"""
    try:
        # 验证列名安全性
        allowed_columns = [
            'pressure_1', 'temperature_1', 'pressure_2', 'temperature_2',
            'pressure_3', 'temperature_3', 'pressure_4', 'pressure_5',
            'temperature_4', 'temperature_5', 'temperature_6',
            'oxygen_content', 'flow_rate_1', 'load_1', 'load_2', 'load_3',
            'pressure_7', 'flow_rate_2'
        ]
        
        if column not in allowed_columns:
            return {
                "success": False,
                "error": f"列名 '{column}' 不允许。允许的列: {allowed_columns}"
            }
        
        connection = get_db_connection()
        
        query = f"""
        SELECT 
            AVG({column}) as average_value,
            MIN({column}) as min_value,
            MAX({column}) as max_value,
            COUNT({column}) as count_value
        FROM industrial_monitoring 
        WHERE {column} IS NOT NULL
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        result = df.iloc[0].to_dict()
        
        return {
            "success": True,
            "column": column,
            "statistics": result
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def detect_anomalies(column: str, threshold: float = 2.0) -> Dict[str, Any]:
    """检测异常值（使用Z-score方法）"""
    try:
        # 验证列名安全性
        allowed_columns = [
            'pressure_1', 'temperature_1', 'pressure_2', 'temperature_2',
            'pressure_3', 'temperature_3', 'pressure_4', 'pressure_5',
            'temperature_4', 'temperature_5', 'temperature_6',
            'oxygen_content', 'flow_rate_1', 'load_1', 'load_2', 'load_3',
            'pressure_7', 'flow_rate_2'
        ]
        
        if column not in allowed_columns:
            return {
                "success": False,
                "error": f"列名 '{column}' 不允许"
            }
        
        connection = get_db_connection()
        
        query = f"""
        SELECT id, record_time, {column}
        FROM industrial_monitoring 
        WHERE {column} IS NOT NULL
        ORDER BY record_time
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        if len(df) == 0:
            return {
                "success": False,
                "error": "没有数据"
            }
        
        # 计算Z-score
        mean_val = df[column].mean()
        std_val = df[column].std()
        
        if std_val == 0:
            return {
                "success": True,
                "anomalies": [],
                "message": "数据无变化，无异常"
            }
        
        df['z_score'] = (df[column] - mean_val) / std_val
        anomalies = df[abs(df['z_score']) > threshold]
        
        anomaly_list = []
        for _, row in anomalies.iterrows():
            anomaly_list.append({
                "id": int(row['id']),
                "time": str(row['record_time']),
                "value": float(row[column]),
                "z_score": float(row['z_score'])
            })
        
        return {
            "success": True,
            "column": column,
            "threshold": threshold,
            "total_records": len(df),
            "anomaly_count": len(anomalies),
            "anomalies": anomaly_list,
            "statistics": {
                "mean": float(mean_val),
                "std": float(std_val)
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
def get_recent_data(limit: int = 10) -> Dict[str, Any]:
    """获取最近的数据记录"""
    try:
        if limit > 100:
            limit = 100  # 限制最大返回数量
        
        connection = get_db_connection()
        
        query = f"""
        SELECT * FROM industrial_monitoring 
        ORDER BY record_time DESC 
        LIMIT {limit}
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        # 转换为字典列表
        records = []
        for _, row in df.iterrows():
            record = row.to_dict()
            # 转换时间格式
            if 'record_time' in record and record['record_time']:
                record['record_time'] = str(record['record_time'])
            records.append(record)
        
        return {
            "success": True,
            "count": len(records),
            "records": records
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    print("🚀 启动工业数据分析MCP服务器")
    print("=" * 50)
    print("📍 地址: http://127.0.0.1:9000/mcp/")
    print("🔧 工具:")
    print("  - get_system_status: 获取系统状态")
    print("  - get_data_summary: 获取数据摘要")
    print("  - calculate_average: 计算平均值")
    print("  - detect_anomalies: 检测异常值")
    print("  - get_recent_data: 获取最近数据")
    print("=" * 50)
    
    try:
        mcp.run(transport="http", host="127.0.0.1", port=9000)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
