#!/usr/bin/env python3
"""
MySQL数据库分析MCP服务器配置向导
"""

import json
import os
import sys
import getpass
from typing import Dict, Any

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("    MySQL数据库分析MCP服务器配置向导")
    print("=" * 60)
    print()

def get_database_config() -> Dict[str, Any]:
    """获取数据库配置"""
    print("📊 数据库配置")
    print("-" * 30)
    
    config = {}
    
    # 数据库主机
    config['host'] = input("数据库主机 [localhost]: ").strip() or "localhost"
    
    # 数据库端口
    port_input = input("数据库端口 [3306]: ").strip()
    try:
        config['port'] = int(port_input) if port_input else 3306
    except ValueError:
        print("⚠️  端口格式错误，使用默认端口 3306")
        config['port'] = 3306
    
    # 数据库用户名
    config['user'] = input("数据库用户名 [root]: ").strip() or "root"
    
    # 数据库密码
    config['password'] = getpass.getpass("数据库密码: ")
    
    # 数据库名称
    config['database'] = input("数据库名称: ").strip()
    if not config['database']:
        print("❌ 数据库名称不能为空")
        return None
    
    return config

def get_server_config() -> Dict[str, Any]:
    """获取服务器配置"""
    print("\n🚀 服务器配置")
    print("-" * 30)
    
    config = {}
    
    # 服务器端口
    port_input = input("MCP服务器端口 [9000]: ").strip()
    try:
        config['server_port'] = int(port_input) if port_input else 9000
    except ValueError:
        print("⚠️  端口格式错误，使用默认端口 9000")
        config['server_port'] = 9000
    
    # 服务器主机
    config['server_host'] = input("MCP服务器主机 [127.0.0.1]: ").strip() or "127.0.0.1"
    
    # 连接池大小
    pool_input = input("数据库连接池大小 [10]: ").strip()
    try:
        config['pool_size'] = int(pool_input) if pool_input else 10
    except ValueError:
        print("⚠️  连接池大小格式错误，使用默认值 10")
        config['pool_size'] = 10
    
    return config

def get_feature_config() -> Dict[str, Any]:
    """获取功能配置"""
    print("\n🎛️  功能配置")
    print("-" * 30)
    
    config = {}
    
    # 语音功能
    voice_input = input("启用语音功能? [y/N]: ").strip().lower()
    config['enable_voice'] = voice_input in ['y', 'yes', '是']
    
    # 图表功能
    chart_input = input("启用图表生成? [Y/n]: ").strip().lower()
    config['enable_charts'] = chart_input not in ['n', 'no', '否']
    
    # 自动提醒检查间隔
    interval_input = input("提醒检查间隔(秒) [60]: ").strip()
    try:
        config['alert_check_interval'] = int(interval_input) if interval_input else 60
    except ValueError:
        print("⚠️  间隔格式错误，使用默认值 60秒")
        config['alert_check_interval'] = 60
    
    return config

def test_database_connection(db_config: Dict[str, Any]) -> bool:
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    try:
        import mysql.connector
        
        conn = mysql.connector.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            connection_timeout=10
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        conn.close()
        
        print("✅ 数据库连接成功！")
        return True
        
    except ImportError:
        print("❌ 未安装 mysql-connector-python，请先运行安装脚本")
        return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def save_config(db_config: Dict[str, Any], server_config: Dict[str, Any], 
                feature_config: Dict[str, Any]):
    """保存配置文件"""
    print("\n💾 保存配置...")
    
    # 合并数据库配置
    full_config = {
        **db_config,
        'pool_name': 'mysql_analysis_pool',
        'pool_size': server_config['pool_size'],
        'pool_reset_session': True,
        'charset': 'utf8mb4',
        'use_unicode': True,
        'autocommit': True,
        'connection_timeout': 30,
        'read_timeout': 30,
        'write_timeout': 30
    }
    
    # 保存数据库配置
    try:
        with open('db_config.json', 'w', encoding='utf-8') as f:
            json.dump(full_config, f, indent=2, ensure_ascii=False)
        print("✅ 数据库配置已保存到 db_config.json")
    except Exception as e:
        print(f"❌ 保存数据库配置失败: {e}")
        return False
    
    # 保存服务器配置
    server_settings = {
        'server': {
            'host': server_config['server_host'],
            'port': server_config['server_port'],
            'log_level': 'info'
        },
        'features': feature_config
    }
    
    try:
        with open('server_config.json', 'w', encoding='utf-8') as f:
            json.dump(server_settings, f, indent=2, ensure_ascii=False)
        print("✅ 服务器配置已保存到 server_config.json")
    except Exception as e:
        print(f"❌ 保存服务器配置失败: {e}")
        return False
    
    return True

def create_startup_script(server_config: Dict[str, Any]):
    """创建启动脚本"""
    print("\n📝 创建启动脚本...")
    
    # Windows启动脚本
    windows_script = f"""@echo off
echo 启动MySQL数据库分析MCP服务器...
echo 服务器地址: http://{server_config['server_host']}:{server_config['server_port']}/mcp/
echo.

if exist venv\\Scripts\\activate.bat (
    call venv\\Scripts\\activate.bat
)

python mysql_analysis_mcp.py

pause
"""
    
    try:
        with open('start_configured.bat', 'w', encoding='utf-8') as f:
            f.write(windows_script)
        print("✅ Windows启动脚本已创建: start_configured.bat")
    except Exception as e:
        print(f"⚠️  创建Windows启动脚本失败: {e}")
    
    # Linux/Mac启动脚本
    linux_script = f"""#!/bin/bash
echo "启动MySQL数据库分析MCP服务器..."
echo "服务器地址: http://{server_config['server_host']}:{server_config['server_port']}/mcp/"
echo

if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
fi

python mysql_analysis_mcp.py
"""
    
    try:
        with open('start_configured.sh', 'w', encoding='utf-8') as f:
            f.write(linux_script)
        os.chmod('start_configured.sh', 0o755)
        print("✅ Linux/Mac启动脚本已创建: start_configured.sh")
    except Exception as e:
        print(f"⚠️  创建Linux/Mac启动脚本失败: {e}")

def show_next_steps(server_config: Dict[str, Any]):
    """显示后续步骤"""
    print("\n🎉 配置完成！")
    print("=" * 60)
    print("\n📋 后续步骤:")
    print("1. 如果还未安装依赖，请运行:")
    print("   Windows: install.bat")
    print("   Linux/Mac: ./install.sh")
    print()
    print("2. (可选) 创建示例数据:")
    print("   python create_sample_data.py")
    print()
    print("3. 启动服务器:")
    print("   python mysql_analysis_mcp.py")
    print("   或使用: start_configured.bat / ./start_configured.sh")
    print()
    print("4. 测试服务器:")
    print("   python test_client.py")
    print()
    print(f"🌐 服务器地址: http://{server_config['server_host']}:{server_config['server_port']}/mcp/")
    print()
    print("📚 更多信息请查看 README.md")

def main():
    """主函数"""
    print_banner()
    
    # 检查是否已有配置文件
    if os.path.exists('db_config.json'):
        overwrite = input("发现已有配置文件，是否覆盖? [y/N]: ").strip().lower()
        if overwrite not in ['y', 'yes', '是']:
            print("配置已取消")
            return
    
    # 获取配置
    db_config = get_database_config()
    if not db_config:
        print("❌ 配置失败")
        return
    
    server_config = get_server_config()
    feature_config = get_feature_config()
    
    # 测试数据库连接
    if not test_database_connection(db_config):
        retry = input("\n数据库连接失败，是否继续保存配置? [y/N]: ").strip().lower()
        if retry not in ['y', 'yes', '是']:
            print("配置已取消")
            return
    
    # 保存配置
    if not save_config(db_config, server_config, feature_config):
        print("❌ 保存配置失败")
        return
    
    # 创建启动脚本
    create_startup_script(server_config)
    
    # 显示后续步骤
    show_next_steps(server_config)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n配置已取消")
    except Exception as e:
        print(f"\n❌ 配置过程中发生错误: {e}")
        sys.exit(1)
