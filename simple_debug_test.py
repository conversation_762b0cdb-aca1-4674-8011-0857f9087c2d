#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的调试测试脚本
逐步测试每个组件
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path

def test_fastmcp_import():
    """测试FastMCP导入"""
    print("1. 测试FastMCP导入...")
    try:
        from fastmcp import FastMCP, Client
        print("   ✓ FastMCP导入成功")
        return True
    except ImportError as e:
        print(f"   ✗ FastMCP导入失败: {e}")
        print("   请运行: pip install fastmcp")
        return False

def test_mysql_connection():
    """测试MySQL连接"""
    print("2. 测试MySQL连接...")
    try:
        import mysql.connector
        
        # 读取数据库配置
        config_file = Path("db_config.json")
        if not config_file.exists():
            print("   ✗ 数据库配置文件不存在: db_config.json")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 测试连接
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        print("   ✓ MySQL连接成功")
        return True
        
    except Exception as e:
        print(f"   ✗ MySQL连接失败: {e}")
        return False

def test_mcp_server_direct():
    """直接测试MCP服务器"""
    print("3. 测试MCP服务器直接调用...")
    
    script = '''
import asyncio
import json
import sys

async def test():
    try:
        from fastmcp import Client
        client = Client("mysql_analysis_mcp.py")
        async with client:
            result = await client.call_tool("get_database_info", {})
            print(json.dumps({"success": True, "message": "MCP服务器调用成功"}))
    except Exception as e:
        print(json.dumps({"success": False, "error": str(e)}))

asyncio.run(test())
'''
    
    try:
        process = subprocess.run(
            [sys.executable, "-c", script],
            capture_output=True,
            text=True,
            timeout=30,
            cwd=Path.cwd()
        )
        
        if process.returncode == 0:
            try:
                result = json.loads(process.stdout)
                if result.get("success"):
                    print("   ✓ MCP服务器直接调用成功")
                    return True
                else:
                    print(f"   ✗ MCP服务器调用失败: {result.get('error')}")
                    return False
            except json.JSONDecodeError:
                print(f"   ✗ 无法解析输出: {process.stdout}")
                return False
        else:
            print(f"   ✗ 进程执行失败:")
            print(f"   标准输出: {process.stdout}")
            print(f"   标准错误: {process.stderr}")
            return False
            
    except Exception as e:
        print(f"   ✗ 测试异常: {e}")
        return False

def test_fastapi_import():
    """测试FastAPI导入"""
    print("4. 测试FastAPI导入...")
    try:
        import fastapi
        import uvicorn
        print("   ✓ FastAPI和Uvicorn导入成功")
        return True
    except ImportError as e:
        print(f"   ✗ FastAPI导入失败: {e}")
        print("   请运行: pip install fastapi uvicorn")
        return False

def test_bridge_script():
    """测试桥接器脚本语法"""
    print("5. 测试桥接器脚本语法...")
    try:
        # 检查语法
        with open("mcp_http_bridge.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, "mcp_http_bridge.py", "exec")
        print("   ✓ 桥接器脚本语法正确")
        return True
    except SyntaxError as e:
        print(f"   ✗ 桥接器脚本语法错误: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 检查桥接器脚本时出错: {e}")
        return False

def run_bridge_test():
    """运行桥接器测试"""
    print("6. 启动桥接器进行测试...")
    
    try:
        # 启动桥接器
        process = subprocess.Popen(
            [sys.executable, "mcp_http_bridge.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=Path.cwd()
        )
        
        print(f"   桥接器进程ID: {process.pid}")
        
        # 等待启动
        print("   等待桥接器启动...")
        time.sleep(5)
        
        if process.poll() is None:
            print("   ✓ 桥接器进程正在运行")
            
            # 测试连接
            try:
                import requests
                response = requests.get("http://127.0.0.1:8080/", timeout=10)
                print(f"   HTTP响应状态: {response.status_code}")
                if response.status_code == 200:
                    print("   ✓ 桥接器HTTP服务正常")
                    
                    # 测试健康检查
                    health_response = requests.get("http://127.0.0.1:8080/health", timeout=10)
                    print(f"   健康检查状态: {health_response.status_code}")
                    if health_response.status_code == 200:
                        print("   ✓ 健康检查通过")
                    else:
                        print(f"   ✗ 健康检查失败: {health_response.text}")
                else:
                    print(f"   ✗ HTTP服务异常: {response.text}")
            except Exception as req_e:
                print(f"   ✗ HTTP请求失败: {req_e}")
            
            # 停止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("   桥接器进程已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("   桥接器进程已强制停止")
            
            return True
        else:
            stdout, stderr = process.communicate()
            print("   ✗ 桥接器进程启动失败:")
            if stdout:
                print(f"   标准输出: {stdout}")
            if stderr:
                print(f"   标准错误: {stderr}")
            return False
            
    except Exception as e:
        print(f"   ✗ 桥接器测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("简单调试测试")
    print("=" * 60)
    
    tests = [
        test_fastmcp_import,
        test_mysql_connection,
        test_mcp_server_direct,
        test_fastapi_import,
        test_bridge_script,
        run_bridge_test
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"   ✗ 测试异常: {e}")
            print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，系统应该可以正常运行")
    else:
        print("✗ 部分测试失败，请根据上述信息排查问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
