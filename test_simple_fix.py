#!/usr/bin/env python3
"""
简单测试修复
"""

import requests
import json
import time

def test_simple_bridge():
    """测试简化桥接器"""
    print("🧪 测试简化桥接器...")
    
    url = "http://127.0.0.1:8083/mcp/call-tool"
    payload = {
        "tool_name": "get_database_statistics",
        "arguments": {
            "table": "payment",
            "column": "amount"
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        print(f"📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 测试成功: {result}")
            return True
        else:
            print(f"❌ 测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    print("🚀 开始测试简化桥接器...")
    print("=" * 50)
    
    # 等待系统完全启动
    print("⏳ 等待系统启动...")
    time.sleep(2)
    
    # 测试简化桥接器
    success = test_simple_bridge()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过！系统正常运行！")
    else:
        print("⚠️ 测试失败，需要检查系统状态")

if __name__ == "__main__":
    main()
