#!/usr/bin/env python3
"""
简化的MCP客户端测试
"""

import asyncio
import json
import sys
from typing import Dict, Any

try:
    from fastmcp import Client
    import aiohttp
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请运行: pip install fastmcp aiohttp")
    sys.exit(1)

async def test_server_connection():
    """测试服务器连接"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("🔗 测试MCP服务器连接...")
    
    try:
        # 测试HTTP连接
        async with aiohttp.ClientSession() as session:
            async with session.get("http://127.0.0.1:9000") as response:
                print(f"HTTP状态码: {response.status}")
                if response.status == 200:
                    print("✅ HTTP服务器响应正常")
                else:
                    print(f"⚠️  HTTP服务器状态码: {response.status}")
    except Exception as e:
        print(f"❌ HTTP连接失败: {e}")
    
    try:
        # 测试MCP连接
        async with <PERSON><PERSON>(server_url) as client:
            print("✅ MCP客户端连接成功")
            
            # 测试数据库连接
            print("\n📊 测试数据库连接...")
            result = await client.call_tool("test_connection", {})
            print(f"结果: {result}")

            # 获取表列表
            print("\n📋 获取数据库表...")
            result = await client.call_tool("get_tables", {})
            print(f"结果: {result}")

            # 获取电影统计
            print("\n🎬 获取电影统计...")
            result = await client.call_tool("get_film_stats", {})
            print(f"结果: {result}")

            # 执行简单查询
            print("\n💻 执行简单查询...")
            result = await client.call_tool("simple_query", {
                "sql": "SELECT title, rating, rental_rate FROM film LIMIT 5"
            })
            print(f"结果: {result}")
            
            print("\n🎉 所有测试完成！")
            
    except Exception as e:
        print(f"❌ MCP连接失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(test_server_connection())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}")
