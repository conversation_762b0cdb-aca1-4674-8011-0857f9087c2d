#!/usr/bin/env python3
"""
测试displayChart修复
"""

import requests
import json
import time

def test_stats_analysis():
    """测试统计分析功能"""
    print("🧪 测试统计分析功能...")
    
    # 测试统计分析
    url = "http://127.0.0.1:8081/call_tool"
    payload = {
        "tool": "get_database_statistics",
        "arguments": {
            "table": "payment",
            "column": "amount"
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        print(f"📊 统计分析响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 统计分析成功: {result}")
            return True
        else:
            print(f"❌ 统计分析失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 统计分析请求失败: {e}")
        return False

def test_trend_analysis():
    """测试趋势分析功能"""
    print("🧪 测试趋势分析功能...")
    
    url = "http://127.0.0.1:8081/call_tool"
    payload = {
        "tool": "analyze_data_trend",
        "arguments": {
            "table": "payment",
            "column": "amount",
            "time_column": "payment_date",
            "period": "month"
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        print(f"📈 趋势分析响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 趋势分析成功: {result}")
            return True
        else:
            print(f"❌ 趋势分析失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 趋势分析请求失败: {e}")
        return False

def test_chart_generation():
    """测试图表生成功能"""
    print("🧪 测试图表生成功能...")
    
    url = "http://127.0.0.1:8081/call_tool"
    payload = {
        "tool": "generate_bar_chart",
        "arguments": {
            "table": "payment",
            "x_column": "customer_id",
            "y_column": "amount",
            "title": "客户支付金额分析",
            "limit": 10
        }
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        print(f"📊 图表生成响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 图表生成成功: {result}")
            return True
        else:
            print(f"❌ 图表生成失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 图表生成请求失败: {e}")
        return False

def main():
    print("🚀 开始测试displayChart修复...")
    print("=" * 50)
    
    # 等待系统完全启动
    print("⏳ 等待系统启动...")
    time.sleep(3)
    
    results = []
    
    # 测试统计分析
    results.append(("统计分析", test_stats_analysis()))
    
    # 测试趋势分析
    results.append(("趋势分析", test_trend_analysis()))
    
    # 测试图表生成
    results.append(("图表生成", test_chart_generation()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！displayChart修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
