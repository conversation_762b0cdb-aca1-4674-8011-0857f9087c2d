#!/usr/bin/env python3
"""
调试MCP服务器
添加详细的调试信息
"""

import sys
import traceback
from fastmcp import FastMCP

print("🔍 开始调试MCP服务器")
print("=" * 40)

try:
    print("1. 导入FastMCP...")
    from fastmcp import FastMCP
    print("✅ FastMCP导入成功")
    
    print("2. 创建MCP实例...")
    mcp = FastMCP("调试服务器")
    print("✅ MCP实例创建成功")
    
    print("3. 添加简单工具...")
    
    @mcp.tool()
    def hello() -> str:
        """简单的hello工具"""
        return "Hello from debug server!"
    
    @mcp.tool()
    def test_database() -> dict:
        """测试数据库连接"""
        try:
            import mysql.connector
            print("  - 尝试连接数据库...")
            connection = mysql.connector.connect(
                host='localhost',
                port=3306,
                user='root',
                password='123456',
                database='realtime_data',
                connect_timeout=5
            )
            
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM industrial_monitoring")
            count = cursor.fetchone()[0]
            cursor.close()
            connection.close()
            
            return {
                "status": "success",
                "message": "数据库连接成功",
                "record_count": count
            }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    print("✅ 工具添加成功")
    
    print("4. 启动服务器...")
    print(f"📍 地址: http://127.0.0.1:9000/mcp/")
    print(f"🔧 工具: hello, test_database")
    print("=" * 40)
    
    # 启动服务器
    mcp.run(transport="http", host="127.0.0.1", port=9000)
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    print("\n🔍 详细错误信息:")
    traceback.print_exc()
    
    print("\n💡 可能的解决方案:")
    print("1. 检查端口9000是否被占用")
    print("2. 检查FastMCP版本")
    print("3. 检查Python环境")
    
    sys.exit(1)
