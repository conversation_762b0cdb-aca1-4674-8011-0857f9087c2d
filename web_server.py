#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地Web服务器 - 为MySQL数据分析系统提供Web界面
完全本地化，无需互联网连接
"""

import os
import sys
import webbrowser
import threading
import time
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse
import json

# 设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

class CORSHTTPRequestHandler(SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 设置Web客户端目录为服务根目录
        super().__init__(*args, directory="web_client", **kwargs)
    
    def end_headers(self):
        """添加CORS头部"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"🔍 [WEB DEBUG] [{timestamp}] {format % args}")

        # 添加详细的请求信息
        if hasattr(self, 'requestline'):
            print(f"🔍 [WEB DEBUG] 请求行: {self.requestline}")
        if hasattr(self, 'headers'):
            print(f"🔍 [WEB DEBUG] 请求头:")
            for header, value in self.headers.items():
                print(f"    {header}: {value}")
        if hasattr(self, 'path'):
            print(f"🔍 [WEB DEBUG] 请求路径: {self.path}")
        print("🔍 [WEB DEBUG] " + "-" * 40)

class WebServerManager:
    """Web服务器管理器"""
    
    def __init__(self, host='127.0.0.1', port=8080):
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.is_running = False
    
    def start_server(self):
        """启动Web服务器"""
        try:
            # 检查web_client目录是否存在
            if not os.path.exists('web_client'):
                print("错误: web_client目录不存在")
                print("请确保以下文件存在:")
                print("  - web_client/index.html")
                print("  - web_client/styles.css")
                print("  - web_client/app.js")
                return False

            # 检查必要文件
            required_files = ['index.html', 'styles.css', 'app.js']
            missing_files = []
            for file in required_files:
                if not os.path.exists(f'web_client/{file}'):
                    missing_files.append(file)

            if missing_files:
                print(f"错误: 缺少必要文件: {', '.join(missing_files)}")
                return False
            
            # 创建HTTP服务器
            self.server = HTTPServer((self.host, self.port), CORSHTTPRequestHandler)
            
            # 在单独线程中运行服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()

            self.is_running = True
            return True

        except OSError as e:
            if e.errno == 10048:  # 端口被占用
                print(f"错误: 端口 {self.port} 已被占用")
                print("请尝试使用其他端口或关闭占用该端口的程序")
            else:
                print(f"启动服务器失败: {e}")
            return False
        except Exception as e:
            print(f"启动服务器失败: {e}")
            return False
    
    def stop_server(self):
        """停止Web服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.is_running = False
            print("Web服务器已停止")
    
    def get_url(self):
        """获取Web界面URL"""
        return f"http://{self.host}:{self.port}"

def check_mcp_server():
    """检查MCP服务器是否运行"""
    import urllib.request
    import urllib.error

    try:
        # 尝试连接MCP服务器
        req = urllib.request.Request('http://127.0.0.1:9002/mcp/')
        req.add_header('Content-Type', 'application/json')

        # 发送hello工具调用请求
        data = json.dumps({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": "hello",
                "arguments": {}
            }
        }).encode('utf-8')

        with urllib.request.urlopen(req, data, timeout=5) as response:
            return True
    except:
        return False

def print_banner():
    """打印启动横幅"""
    banner = """
===============================================================

        工业数据分析系统 - Web客户端

        基于MCP协议的工业数据分析解决方案
        连接到工业数据分析MCP服务器

===============================================================
"""
    print(banner)

def print_status(web_url, mcp_running):
    """打印系统状态"""
    print("\n系统状态:")
    print(f"   Web界面: {web_url}")
    print(f"   MCP服务器: {'运行中' if mcp_running else '未运行'}")

    if not mcp_running:
        print("\n注意: MCP服务器未运行")
        print("   请先启动MCP服务器:")
        print("   python standalone_server.py")

    print(f"\nWeb界面已启动: {web_url}")
    print("   在浏览器中打开上述地址即可使用")
    print("\n使用说明:")
    print("   1. 确保MySQL数据库正在运行")
    print("   2. 确保工业数据分析MCP服务器正在运行")
    print("   3. 在Web界面中点击'连接服务器'")
    print("   4. 开始使用工业数据分析功能")

def main():
    """主函数"""
    print_banner()
    
    # 检查命令行参数
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 错误: 端口号必须是数字")
            sys.exit(1)
    
    # 创建Web服务器管理器
    web_manager = WebServerManager(port=port)
    
    # 启动Web服务器
    print("启动Web服务器...")
    if not web_manager.start_server():
        print("Web服务器启动失败")
        sys.exit(1)

    # 检查MCP服务器状态
    print("检查MCP服务器状态...")
    mcp_running = check_mcp_server()

    # 打印状态信息
    web_url = web_manager.get_url()
    print_status(web_url, mcp_running)

    # 自动打开浏览器
    print("\n正在打开浏览器...")
    try:
        webbrowser.open(web_url)
        print("浏览器已打开")
    except Exception as e:
        print(f"无法自动打开浏览器: {e}")
        print(f"请手动在浏览器中打开: {web_url}")

    print("\n" + "="*60)
    print("Web服务器运行中...")
    print("按 Ctrl+C 停止服务器")
    print("="*60)
    
    try:
        # 保持服务器运行
        while web_manager.is_running:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n收到停止信号...")
        web_manager.stop_server()
        print("感谢使用工业数据分析系统！")

if __name__ == "__main__":
    main()
