#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新启动系统
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def kill_python_processes():
    """杀掉所有Python进程"""
    try:
        subprocess.run(["taskkill", "/F", "/IM", "python.exe"], 
                      capture_output=True, text=True)
        print("已停止所有Python进程")
        time.sleep(2)
    except:
        pass

def start_bridge():
    """启动HTTP桥接器"""
    print("启动HTTP桥接器...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, "mcp_http_bridge.py"],
            cwd=Path.cwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"HTTP桥接器进程ID: {process.pid}")
        
        # 等待启动
        for i in range(10):
            try:
                response = requests.get("http://127.0.0.1:8082/", timeout=2)
                if response.status_code == 200:
                    print(f"✓ HTTP桥接器启动成功 (第{i+1}秒)")
                    return process
            except:
                pass
            time.sleep(1)
            print(f"等待启动... ({i+1}/10)")
        
        print("⚠ HTTP桥接器启动超时")
        return process
        
    except Exception as e:
        print(f"启动HTTP桥接器失败: {e}")
        return None

def start_web_server():
    """启动Web服务器"""
    print("\n启动Web服务器...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, "web_server.py", "8081"],
            cwd=Path.cwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"Web服务器进程ID: {process.pid}")
        
        # 等待启动
        for i in range(5):
            try:
                response = requests.get("http://127.0.0.1:8081/", timeout=2)
                if response.status_code == 200:
                    print(f"✓ Web服务器启动成功 (第{i+1}秒)")
                    return process
            except:
                pass
            time.sleep(1)
            print(f"等待启动... ({i+1}/5)")
        
        print("⚠ Web服务器启动超时")
        return process
        
    except Exception as e:
        print(f"启动Web服务器失败: {e}")
        return None

def test_system():
    """测试系统"""
    print("\n测试系统...")
    
    # 测试HTTP桥接器
    try:
        response = requests.get("http://127.0.0.1:8082/", timeout=5)
        print(f"✓ HTTP桥接器响应正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"✗ HTTP桥接器测试失败: {e}")
    
    # 测试健康检查
    try:
        response = requests.get("http://127.0.0.1:8082/health", timeout=5)
        print(f"✓ 健康检查响应正常 (状态码: {response.status_code})")
        if response.status_code == 200:
            data = response.json()
            print(f"  状态: {data.get('status')}")
            print(f"  消息: {data.get('message')}")
    except Exception as e:
        print(f"✗ 健康检查失败: {e}")
    
    # 测试Web服务器
    try:
        response = requests.get("http://127.0.0.1:8081/", timeout=5)
        print(f"✓ Web服务器响应正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"✗ Web服务器测试失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("重新启动MySQL数据分析系统")
    print("=" * 60)
    
    # 1. 停止所有进程
    kill_python_processes()
    
    # 2. 启动HTTP桥接器
    bridge_process = start_bridge()
    if not bridge_process:
        print("HTTP桥接器启动失败，退出")
        return
    
    # 3. 启动Web服务器
    web_process = start_web_server()
    if not web_process:
        print("Web服务器启动失败，但HTTP桥接器仍在运行")
    
    # 4. 测试系统
    test_system()
    
    print("\n" + "=" * 60)
    print("系统启动完成!")
    print("HTTP桥接器: http://127.0.0.1:8082")
    print("Web界面: http://127.0.0.1:8081")
    print("按 Ctrl+C 停止所有服务")
    print("=" * 60)
    
    try:
        # 保持运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止服务...")
        kill_python_processes()
        print("所有服务已停止")

if __name__ == "__main__":
    main()
