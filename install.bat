@echo off
echo ========================================
echo MySQL数据库分析MCP服务器安装脚本
echo ========================================

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 创建虚拟环境...
python -m venv venv
if %errorlevel% neq 0 (
    echo 错误: 创建虚拟环境失败
    pause
    exit /b 1
)

echo.
echo 激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 错误: 安装依赖包失败
    pause
    exit /b 1
)

echo.
echo 检查数据库配置...
if not exist db_config.json (
    echo 警告: 未找到db_config.json，请配置数据库连接信息
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用说明:
echo 1. 编辑 db_config.json 配置数据库连接
echo 2. 运行 start.bat 启动服务器
echo 3. 或者直接运行: python mysql_analysis_mcp.py
echo.
pause
