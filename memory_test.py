#!/usr/bin/env python3
"""
内存中的MCP测试
避免网络连接问题
"""

import asyncio
import json
from fastmcp import FastMCP, Client

# 创建内存中的服务器
mcp = FastMCP("内存测试服务器")

@mcp.tool()
def hello() -> str:
    """简单的hello工具"""
    return "Hello from memory server!"

@mcp.tool()
def test_database() -> dict:
    """测试数据库连接"""
    try:
        import mysql.connector
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='realtime_data',
            connect_timeout=5
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM industrial_monitoring")
        count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        return {
            "status": "success",
            "message": "数据库连接成功",
            "record_count": count
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

@mcp.tool()
def get_system_status() -> dict:
    """获取系统状态"""
    return {
        "status": "运行正常",
        "server_type": "内存测试服务器",
        "tools": ["hello", "test_database", "get_system_status"]
    }

async def test_memory_server():
    """测试内存中的服务器"""
    print("🧪 测试内存中的MCP服务器")
    print("=" * 40)
    
    # 使用内存中的服务器
    client = Client(mcp)
    
    try:
        async with client:
            print("✅ 内存服务器连接成功")
            
            # 列出工具
            tools = await client.list_tools()
            print(f"📋 可用工具: {[tool.name for tool in tools]}")
            
            # 测试hello工具
            print("\n🔧 测试hello工具:")
            result = await client.call_tool("hello", {})
            print(f"   结果: {result.data}")
            
            # 测试系统状态
            print("\n🔧 测试系统状态:")
            result = await client.call_tool("get_system_status", {})
            print(f"   结果: {json.dumps(result.data, indent=2, ensure_ascii=False)}")
            
            # 测试数据库
            print("\n🔧 测试数据库连接:")
            result = await client.call_tool("test_database", {})
            print(f"   结果: {json.dumps(result.data, indent=2, ensure_ascii=False)}")
            
            print("\n🎉 所有测试完成！")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def interactive_memory_test():
    """交互式内存测试"""
    print("🎯 交互式内存MCP测试")
    print("=" * 40)
    
    client = Client(mcp)
    
    print("\n💡 可用命令:")
    print("  1. hello - 测试hello工具")
    print("  2. status - 系统状态")
    print("  3. db - 数据库测试")
    print("  4. tools - 列出工具")
    print("  5. quit - 退出")
    
    while True:
        try:
            command = input("\n📝 请输入命令: ").strip().lower()
            
            if command in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            async with client:
                if command == 'hello':
                    result = await client.call_tool("hello", {})
                    print(f"📊 结果: {result.data}")
                
                elif command == 'status':
                    result = await client.call_tool("get_system_status", {})
                    print(f"📊 结果: {json.dumps(result.data, indent=2, ensure_ascii=False)}")
                
                elif command == 'db':
                    result = await client.call_tool("test_database", {})
                    print(f"📊 结果: {json.dumps(result.data, indent=2, ensure_ascii=False)}")
                
                elif command == 'tools':
                    tools = await client.list_tools()
                    print("📋 可用工具:")
                    for tool in tools:
                        print(f"  🔧 {tool.name}: {tool.description}")
                
                else:
                    print("❌ 未知命令，请输入有效命令")
        
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        await interactive_memory_test()
    else:
        await test_memory_server()

if __name__ == "__main__":
    asyncio.run(main())
