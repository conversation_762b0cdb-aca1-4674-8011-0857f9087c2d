@echo off
echo ========================================
echo 启动MySQL数据库分析MCP服务器
echo ========================================

echo 激活虚拟环境...
if exist venv\Scripts\activate.bat (
    call venv\Scripts\activate.bat
) else (
    echo 警告: 未找到虚拟环境，请先运行 install.bat
)

echo.
echo 检查配置文件...
if not exist db_config.json (
    echo 错误: 未找到数据库配置文件 db_config.json
    echo 请先配置数据库连接信息
    pause
    exit /b 1
)

echo.
echo 启动MCP服务器...
echo 服务器地址: http://127.0.0.1:9000/mcp/
echo 按 Ctrl+C 停止服务器
echo.

python mysql_analysis_mcp.py

echo.
echo 服务器已停止
pause
