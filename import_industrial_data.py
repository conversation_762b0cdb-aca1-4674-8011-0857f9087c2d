#!/usr/bin/env python3
"""
工业监控数据导入工具
专门处理工业设备监控Excel数据
"""

import pandas as pd
import mysql.connector
import json
import re
from datetime import datetime

def connect_database():
    """连接数据库"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        db_config = config.get('database', {})
        connection = mysql.connector.connect(
            host=db_config.get('host', 'localhost'),
            port=db_config.get('port', 3306),
            user=db_config.get('user', 'root'),
            password=db_config.get('password', '123456'),
            database=db_config.get('database', 'realtime_data'),
            charset=db_config.get('charset', 'utf8mb4')
        )
        print("✅ 数据库连接成功")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def create_industrial_monitoring_table(connection):
    """创建工业监控数据表"""
    # 先删除现有表
    drop_sql = "DROP TABLE IF EXISTS industrial_monitoring"

    create_sql = """
CREATE TABLE industrial_monitoring (
    id INT AUTO_INCREMENT PRIMARY KEY,
    record_time DATETIME NOT NULL COMMENT '记录时间',
    pressure_1 DECIMAL(12,6) COMMENT '压力1 (MPa)',
    temperature_1 DECIMAL(12,6) COMMENT '温度1 (℃)',
    pressure_2 DECIMAL(12,6) COMMENT '压力2 (MPa)',
    temperature_2 DECIMAL(12,6) COMMENT '温度2 (℃)',
    pressure_3 DECIMAL(12,6) COMMENT '压力3 (MPa)',
    temperature_3 DECIMAL(12,6) COMMENT '温度3 (℃)',
    pressure_4 DECIMAL(12,6) COMMENT '压力4 (MPa)',
    pressure_5 DECIMAL(12,6) COMMENT '压力5 (MPa)',
    temperature_4 DECIMAL(12,6) COMMENT '温度4 (℃)',
    temperature_5 DECIMAL(12,6) COMMENT '温度5 (℃)',
    temperature_6 DECIMAL(12,6) COMMENT '温度6 (℃)',
    oxygen_content DECIMAL(12,6) COMMENT '含氧量 (%)',
    flow_rate_1 DECIMAL(12,6) COMMENT '流量1 (m³/h)',
    load_1 DECIMAL(12,6) COMMENT '负荷1 (%)',
    load_2 DECIMAL(12,6) COMMENT '负荷2 (%)',
    load_3 DECIMAL(12,6) COMMENT '负荷3 (%)',
    pressure_7 DECIMAL(12,6) COMMENT '压力7 (MPa)',
    flow_rate_2 DECIMAL(12,6) COMMENT '流量2 (m³/h)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_record_time (record_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='工业设备监控数据表';
"""
    
    try:
        cursor = connection.cursor()
        cursor.execute(drop_sql)
        cursor.execute(create_sql)
        connection.commit()
        cursor.close()
        print("✅ 工业监控数据表创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def parse_datetime(date_str):
    """解析日期时间字符串"""
    try:
        # 格式：2025年06月24日08时36分48秒
        return datetime.strptime(date_str, '%Y年%m月%d日%H时%M分%S秒')
    except:
        return None

def import_industrial_data():
    """导入工业监控数据"""
    print("🏭 工业监控数据导入工具")
    print("="*50)
    
    # 连接数据库
    connection = connect_database()
    if not connection:
        return False
    
    try:
        # 创建表
        if not create_industrial_monitoring_table(connection):
            return False
        
        # 读取Excel数据
        print("📊 读取Excel数据...")
        df = pd.read_excel(r'date\数据库.xls', sheet_name='第1页')
        
        print(f"📏 数据尺寸: {len(df)} 行 × {len(df.columns)} 列")
        
        # 清理和重命名列
        column_mapping = {
            '日期': 'record_time',
            '1压力\nMPa': 'pressure_1',
            '温度1\n℃': 'temperature_1',
            '压力2\nMPa': 'pressure_2',
            '温度2\n℃': 'temperature_2',
            '压力3\nMPa': 'pressure_3',
            '温度3\n℃': 'temperature_3',
            '压力4\nMPa': 'pressure_4',
            '压力5\nMPa': 'pressure_5',
            '温度4\n℃': 'temperature_4',
            '温度5\n℃': 'temperature_5',
            '温度6\n℃': 'temperature_6',
            '含氧\n%': 'oxygen_content',
            '流量\nm3/h': 'flow_rate_1',
            '负荷1\n%': 'load_1',
            '负荷2\n%': 'load_2',
            '负荷3\n%': 'load_3',
            '压力7\nMPa': 'pressure_7',
            '流量2\nm3/h': 'flow_rate_2'
        }
        
        # 重命名列
        df = df.rename(columns=column_mapping)
        
        # 解析时间
        print("🕐 解析时间数据...")
        df['record_time'] = df['record_time'].apply(parse_datetime)
        
        # 移除时间解析失败的行
        df = df.dropna(subset=['record_time'])
        
        print(f"✅ 成功解析 {len(df)} 行数据")
        
        # 准备插入数据
        cursor = connection.cursor()
        
        insert_sql = """
INSERT INTO industrial_monitoring (
    record_time, pressure_1, temperature_1, pressure_2, temperature_2,
    pressure_3, temperature_3, pressure_4, pressure_5, temperature_4,
    temperature_5, temperature_6, oxygen_content, flow_rate_1, load_1,
    load_2, load_3, pressure_7, flow_rate_2
) VALUES (
    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
    %s, %s, %s, %s, %s, %s, %s, %s, %s
)
"""
        
        # 批量插入数据
        print("📥 插入数据到MySQL...")
        data_to_insert = []
        
        for _, row in df.iterrows():
            data_to_insert.append((
                row['record_time'],
                row['pressure_1'], row['temperature_1'], row['pressure_2'], row['temperature_2'],
                row['pressure_3'], row['temperature_3'], row['pressure_4'], row['pressure_5'],
                row['temperature_4'], row['temperature_5'], row['temperature_6'],
                row['oxygen_content'], row['flow_rate_1'], row['load_1'],
                row['load_2'], row['load_3'], row['pressure_7'], row['flow_rate_2']
            ))
        
        cursor.executemany(insert_sql, data_to_insert)
        connection.commit()
        cursor.close()
        
        print(f"✅ 成功导入 {len(data_to_insert)} 条工业监控数据")
        
        # 显示导入统计
        show_import_statistics(connection)
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False
    finally:
        connection.close()

def show_import_statistics(connection):
    """显示导入统计信息"""
    try:
        cursor = connection.cursor()
        
        # 基本统计
        cursor.execute("SELECT COUNT(*) FROM industrial_monitoring")
        total_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(record_time), MAX(record_time) FROM industrial_monitoring")
        time_range = cursor.fetchone()
        
        print(f"\n📊 导入统计:")
        print(f"  总记录数: {total_records}")
        print(f"  时间范围: {time_range[0]} 到 {time_range[1]}")
        
        # 数据范围统计
        cursor.execute("""
SELECT 
    AVG(pressure_1) as avg_pressure_1,
    AVG(temperature_1) as avg_temp_1,
    AVG(oxygen_content) as avg_oxygen,
    AVG(flow_rate_1) as avg_flow
FROM industrial_monitoring
""")
        
        stats = cursor.fetchone()
        print(f"  平均压力1: {stats[0]:.3f} MPa")
        print(f"  平均温度1: {stats[1]:.3f} ℃")
        print(f"  平均含氧量: {stats[2]:.3f} %")
        print(f"  平均流量1: {stats[3]:.3f} m³/h")
        
        cursor.close()
        
    except Exception as e:
        print(f"⚠️ 统计信息获取失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print(f"\n🎯 数据分析示例:")
    print("="*40)
    print("现在你可以使用MCP系统分析工业监控数据：")
    print("\n📊 统计分析:")
    print("  '统计industrial_monitoring表pressure_1字段的平均值'")
    print("  '计算industrial_monitoring表temperature_1字段的最大值'")
    
    print("\n🔍 异常检测:")
    print("  '检测industrial_monitoring表pressure_1字段的异常值'")
    print("  '分析industrial_monitoring表temperature_1字段的异常'")
    
    print("\n📈 趋势分析:")
    print("  '分析industrial_monitoring表pressure_1字段的趋势'")
    print("  '预测industrial_monitoring表temperature_1字段的变化'")
    
    print("\n📊 图表生成:")
    print("  '生成industrial_monitoring表pressure_1字段的折线图'")
    print("  '创建industrial_monitoring表temperature_1字段的散点图'")
    
    print("\n🎤 语音命令:")
    print("  启动客户端后说：'分析工业监控数据的压力异常'")

def main():
    """主函数"""
    success = import_industrial_data()
    
    if success:
        print("\n🎉 工业监控数据导入成功！")
        show_usage_examples()
        
        print(f"\n📋 下一步:")
        print("1. 启动MCP服务器: python local_mcp_server.py")
        print("2. 启动MCP客户端: python local_mcp_client.py")
        print("3. 使用语音或文本分析你的工业数据")
        
    else:
        print("\n❌ 导入失败，请检查错误信息")

if __name__ == "__main__":
    main()
