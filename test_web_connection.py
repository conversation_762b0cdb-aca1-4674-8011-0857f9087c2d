#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web客户端连接
"""

import requests
import json

def test_web_client_endpoints():
    """测试Web客户端需要的所有端点"""
    print("=" * 60)
    print("测试Web客户端连接")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8080"
    
    # 测试连接检查（Web客户端的connectToServer方法）
    print("1. 测试连接检查...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {data}")
            print("   ✓ 连接检查成功")
        else:
            print(f"   ✗ 连接检查失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 连接检查异常: {e}")
    
    # 测试数据库信息
    print("\n2. 测试数据库信息...")
    try:
        response = requests.get(f"{base_url}/api/database-info", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   数据库: {data.get('database_info', {}).get('database')}")
            print(f"   表数量: {data.get('database_info', {}).get('table_count')}")
            print("   ✓ 数据库信息获取成功")
        else:
            print(f"   ✗ 数据库信息获取失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 数据库信息获取异常: {e}")
    
    # 测试统计分析
    print("\n3. 测试统计分析...")
    try:
        payload = {
            "table": "users",
            "column": "age"
        }
        response = requests.post(f"{base_url}/api/statistics", 
                               json=payload, timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   平均值: {data.get('data', {}).get('average')}")
            print("   ✓ 统计分析成功")
        else:
            print(f"   ✗ 统计分析失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 统计分析异常: {e}")
    
    # 测试异常检测
    print("\n4. 测试异常检测...")
    try:
        payload = {
            "table": "orders",
            "column": "amount",
            "method": "zscore",
            "threshold": 2.0
        }
        response = requests.post(f"{base_url}/api/anomaly-detection", 
                               json=payload, timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            anomaly_count = data.get('data', {}).get('anomaly_count', 0)
            print(f"   异常数量: {anomaly_count}")
            print("   ✓ 异常检测成功")
        else:
            print(f"   ✗ 异常检测失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 异常检测异常: {e}")
    
    # 测试图表生成
    print("\n5. 测试图表生成...")
    try:
        payload = {
            "chart_type": "bar",
            "table": "products",
            "title": "测试图表"
        }
        response = requests.post(f"{base_url}/api/generate-chart", 
                               json=payload, timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            chart_type = data.get('chart_type')
            print(f"   图表类型: {chart_type}")
            print("   ✓ 图表生成成功")
        else:
            print(f"   ✗ 图表生成失败: {response.text}")
    except Exception as e:
        print(f"   ✗ 图表生成异常: {e}")
    
    # 测试SQL执行
    print("\n6. 测试SQL执行...")
    try:
        payload = {
            "query": "SELECT * FROM users LIMIT 5"
        }
        response = requests.post(f"{base_url}/api/execute-sql", 
                               json=payload, timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            row_count = data.get('data', {}).get('row_count', 0)
            print(f"   返回行数: {row_count}")
            print("   ✓ SQL执行成功")
        else:
            print(f"   ✗ SQL执行失败: {response.text}")
    except Exception as e:
        print(f"   ✗ SQL执行异常: {e}")

def main():
    """主函数"""
    test_web_client_endpoints()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("如果所有测试都成功，Web界面应该能正常连接")
    print("=" * 60)

if __name__ == "__main__":
    main()
