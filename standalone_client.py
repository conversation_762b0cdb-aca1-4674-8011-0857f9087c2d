#!/usr/bin/env python3
"""
独立的MCP客户端
连接到独立服务器
"""

import asyncio
import json
from typing import Dict, Any
from fastmcp import Client

class StandaloneClient:
    """独立MCP客户端"""
    
    def __init__(self, server_url: str = "http://127.0.0.1:9001/mcp/"):
        self.server_url = server_url
    
    async def test_connection(self):
        """测试连接"""
        print(f"🔗 连接到服务器: {self.server_url}")
        
        try:
            async with Client(self.server_url) as client:
                print("✅ 连接成功")
                
                # 测试ping
                await client.ping()
                print("✅ Ping成功")
                
                # 列出工具
                tools = await client.list_tools()
                print(f"📋 可用工具: {[tool.name for tool in tools]}")
                
                return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            print("💡 请确保服务器正在运行")
            return False
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None):
        """调用工具"""
        if arguments is None:
            arguments = {}
        
        print(f"\n🔄 调用工具: {tool_name}")
        if arguments:
            print(f"📝 参数: {arguments}")
        
        try:
            async with Client(self.server_url) as client:
                result = await client.call_tool(tool_name, arguments)
                
                print("✅ 调用成功")
                print(f"📊 结果: {json.dumps(result.data, indent=2, ensure_ascii=False)}")
                return result.data
                
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    async def demo_analysis(self):
        """演示分析"""
        print("🎯 工业数据分析演示")
        print("=" * 40)
        
        # 测试连接
        if not await self.test_connection():
            return
        
        print("\n📊 开始演示分析...")
        
        # 1. Hello测试
        print("\n1️⃣ Hello测试")
        await self.call_tool("hello")
        
        # 2. 系统状态
        print("\n2️⃣ 系统状态")
        await self.call_tool("get_system_status")
        
        # 3. 数据摘要
        print("\n3️⃣ 数据摘要")
        await self.call_tool("get_data_summary")
        
        # 4. 计算平均值
        print("\n4️⃣ 压力1平均值")
        await self.call_tool("calculate_average", {"column": "pressure_1"})
        
        # 5. 最近数据
        print("\n5️⃣ 最近3条数据")
        await self.call_tool("get_recent_data", {"limit": 3})
        
        print("\n🎉 演示完成！")
    
    async def interactive_mode(self):
        """交互模式"""
        print("🎯 独立MCP客户端")
        print("=" * 40)
        
        # 测试连接
        if not await self.test_connection():
            return
        
        print("\n💡 可用命令:")
        print("  1. hello - 测试hello")
        print("  2. status - 系统状态")
        print("  3. summary - 数据摘要")
        print("  4. avg <column> - 计算平均值")
        print("  5. recent <count> - 最近数据")
        print("  6. tools - 列出工具")
        print("  7. quit - 退出")
        
        while True:
            try:
                command = input("\n📝 请输入命令: ").strip().lower()
                
                if command in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                elif command == 'hello':
                    await self.call_tool("hello")
                
                elif command == 'status':
                    await self.call_tool("get_system_status")
                
                elif command == 'summary':
                    await self.call_tool("get_data_summary")
                
                elif command.startswith('avg '):
                    column = command.split(' ', 1)[1]
                    await self.call_tool("calculate_average", {"column": column})
                
                elif command.startswith('recent '):
                    try:
                        count = int(command.split(' ', 1)[1])
                        await self.call_tool("get_recent_data", {"limit": count})
                    except ValueError:
                        print("❌ 请输入有效数字")
                
                elif command == 'tools':
                    await self.list_tools()
                
                else:
                    print("❌ 未知命令，请输入有效命令")
            
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    async def list_tools(self):
        """列出工具"""
        try:
            async with Client(self.server_url) as client:
                tools = await client.list_tools()
                print("\n📋 可用工具:")
                for tool in tools:
                    print(f"  🔧 {tool.name}: {tool.description}")
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")

async def main():
    """主函数"""
    import sys
    
    client = StandaloneClient()
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        await client.demo_analysis()
    else:
        await client.interactive_mode()

if __name__ == "__main__":
    print("🔌 独立MCP客户端启动")
    print("📍 连接地址: http://127.0.0.1:9001/mcp/")
    print("⚠️  请确保服务器已启动")
    print("=" * 40)
    
    asyncio.run(main())
