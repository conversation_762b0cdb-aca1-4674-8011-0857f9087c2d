#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表图像生成
"""

import requests
import json
import base64
from io import BytesIO

def test_chart_image():
    """测试图表图像生成"""
    base_url = "http://127.0.0.1:8080"
    
    print("=" * 60)
    print("测试图表图像生成")
    print("=" * 60)
    
    # 测试柱状图
    print("\n1. 测试柱状图")
    try:
        payload = {
            "chart_type": "bar",
            "table": "film",
            "x_column": "rating",
            "y_column": "rental_rate",
            "title": "电影评级与租赁费用分析"
        }
        
        response = requests.post(f"{base_url}/api/generate-chart", json=payload)
        data = response.json()
        
        print(f"   状态码: {response.status_code}")
        
        if data.get("success") and data.get("chart_data", {}).get("image_base64"):
            image_base64 = data["chart_data"]["image_base64"]
            print(f"   ✅ 柱状图生成成功")
            print(f"   图像数据长度: {len(image_base64)} 字符")
            
            # 保存图像
            try:
                image_data = base64.b64decode(image_base64)
                with open("test_bar_chart.png", "wb") as f:
                    f.write(image_data)
                print(f"   📁 图像已保存: test_bar_chart.png")
            except Exception as e:
                print(f"   ❌ 保存图像失败: {e}")
        else:
            print(f"   ❌ 柱状图生成失败")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 测试饼图
    print("\n2. 测试饼图")
    try:
        payload = {
            "chart_type": "pie",
            "table": "film",
            "label_column": "rating",
            "value_column": "rental_rate",
            "title": "电影评级分布"
        }
        
        response = requests.post(f"{base_url}/api/generate-chart", json=payload)
        data = response.json()
        
        print(f"   状态码: {response.status_code}")
        
        if data.get("success") and data.get("chart_data", {}).get("image_base64"):
            image_base64 = data["chart_data"]["image_base64"]
            print(f"   ✅ 饼图生成成功")
            print(f"   图像数据长度: {len(image_base64)} 字符")
            
            # 保存图像
            try:
                image_data = base64.b64decode(image_base64)
                with open("test_pie_chart.png", "wb") as f:
                    f.write(image_data)
                print(f"   📁 图像已保存: test_pie_chart.png")
            except Exception as e:
                print(f"   ❌ 保存图像失败: {e}")
        else:
            print(f"   ❌ 饼图生成失败")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 测试趋势图
    print("\n3. 测试趋势图")
    try:
        payload = {
            "chart_type": "line",
            "table": "payment",
            "x_column": "payment_date",
            "y_column": "amount",
            "title": "支付金额趋势分析"
        }
        
        response = requests.post(f"{base_url}/api/generate-chart", json=payload)
        data = response.json()
        
        print(f"   状态码: {response.status_code}")
        
        if data.get("success") and data.get("chart_data", {}).get("image_base64"):
            image_base64 = data["chart_data"]["image_base64"]
            print(f"   ✅ 趋势图生成成功")
            print(f"   图像数据长度: {len(image_base64)} 字符")
            
            # 保存图像
            try:
                image_data = base64.b64decode(image_base64)
                with open("test_line_chart.png", "wb") as f:
                    f.write(image_data)
                print(f"   📁 图像已保存: test_line_chart.png")
            except Exception as e:
                print(f"   ❌ 保存图像失败: {e}")
        else:
            print(f"   ❌ 趋势图生成失败")
            print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")

def main():
    """主函数"""
    test_chart_image()
    
    print("\n" + "=" * 60)
    print("图表图像测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
