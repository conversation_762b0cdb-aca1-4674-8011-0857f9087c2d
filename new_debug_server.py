#!/usr/bin/env python3
"""
新的调试MCP服务器 - 使用端口9003
"""

from fastmcp import FastMCP
import json
import datetime

# 创建MCP应用
mcp = FastMCP("新调试MCP服务器")

@mcp.tool()
def hello() -> str:
    """简单的问候工具"""
    print("🔧 [MCP DEBUG] hello工具被调用")
    return "Hello from New Debug MCP Server!"

@mcp.tool()
def get_system_status() -> dict:
    """获取系统状态"""
    print("🔧 [MCP DEBUG] get_system_status工具被调用")
    return {
        "server": "新调试MCP服务器",
        "time": datetime.datetime.now().isoformat(),
        "status": "running",
        "tools": ["hello", "get_system_status", "debug_info"]
    }

@mcp.tool()
def debug_info() -> dict:
    """返回详细调试信息"""
    print("🔧 [MCP DEBUG] debug_info工具被调用")
    import os
    import sys
    return {
        "server": "新调试MCP服务器",
        "time": datetime.datetime.now().isoformat(),
        "python_version": sys.version,
        "working_directory": os.getcwd(),
        "environment": "debug"
    }

if __name__ == "__main__":
    print("🚀 启动新调试MCP服务器")
    print("=" * 40)
    print("📍 服务器地址: http://127.0.0.1:9003/mcp")
    print("🔧 可用工具:")
    print("  - hello")
    print("  - get_system_status")
    print("  - debug_info")
    print("=" * 40)
    print("🎯 等待客户端连接...")
    
    try:
        print("🔧 [DEBUG] 启动参数:")
        print(f"  transport: http")
        print(f"  host: 127.0.0.1")
        print(f"  port: 9003")
        print(f"  path: /mcp")
        
        mcp.run(transport="http", host="127.0.0.1", port=9003, path="/mcp")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
