#!/usr/bin/env python3
"""
MySQL数据库分析MCP服务器测试客户端
"""

import asyncio
import json
from fastmcp import Client

async def test_mcp_server():
    """测试MCP服务器功能"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("=" * 50)
    print("MySQL数据库分析MCP服务器测试")
    print("=" * 50)
    
    try:
        async with Client(server_url) as client:
            print(f"✓ 成功连接到MCP服务器: {server_url}")
            
            # 测试1: 获取数据库信息
            print("\n1. 测试获取数据库信息...")
            try:
                result = await client.call_tool("get_database_info", {})
                if result.get("success"):
                    db_info = result.get("database_info", {})
                    print(f"   数据库: {db_info.get('database')}")
                    print(f"   主机: {db_info.get('host')}:{db_info.get('port')}")
                    print(f"   表数量: {db_info.get('table_count')}")
                    print(f"   表列表: {db_info.get('tables', [])[:5]}...")  # 只显示前5个
                else:
                    print(f"   ✗ 失败: {result.get('error')}")
            except Exception as e:
                print(f"   ✗ 异常: {e}")
            
            # 测试2: 语音功能
            print("\n2. 测试语音功能...")
            try:
                result = await client.call_tool("voice_command", {
                    "command": "MCP服务器测试中",
                    "listen_for_input": False
                })
                if result.get("success"):
                    print(f"   ✓ 语音播报成功: {result.get('spoken')}")
                else:
                    print(f"   ✗ 失败: {result.get('error')}")
            except Exception as e:
                print(f"   ✗ 异常: {e}")
            
            # 测试3: 创建提醒（示例）
            print("\n3. 测试创建提醒...")
            try:
                result = await client.call_tool("create_alert", {
                    "alert_type": "time_based",
                    "table": "test_table",
                    "alert_time": "2024-12-31 23:59:59",
                    "description": "测试时间提醒"
                })
                if result.get("success"):
                    print(f"   ✓ 提醒创建成功: {result.get('alert_id')}")
                else:
                    print(f"   ✗ 失败: {result.get('error')}")
            except Exception as e:
                print(f"   ✗ 异常: {e}")
            
            # 测试4: 检查提醒
            print("\n4. 测试检查提醒...")
            try:
                result = await client.call_tool("check_alerts", {})
                if result.get("success"):
                    triggered = result.get("triggered_alerts", [])
                    total = result.get("total_checked", 0)
                    print(f"   ✓ 检查完成: 总共{total}个提醒，触发{len(triggered)}个")
                else:
                    print(f"   ✗ 失败: {result.get('error')}")
            except Exception as e:
                print(f"   ✗ 异常: {e}")
            
            # 测试5: 获取资源
            print("\n5. 测试获取资源...")
            try:
                # 获取系统状态
                resources = await client.list_resources()
                print(f"   可用资源数量: {len(resources)}")
                for resource in resources:
                    print(f"   - {resource.uri}: {resource.description}")
                
                # 读取系统状态资源
                if resources:
                    status_resource = None
                    for resource in resources:
                        if "system/status" in resource.uri:
                            status_resource = resource
                            break
                    
                    if status_resource:
                        content = await client.read_resource(status_resource.uri)
                        print(f"   系统状态: {content}")
                
            except Exception as e:
                print(f"   ✗ 异常: {e}")
            
            # 测试6: 列出所有工具
            print("\n6. 可用工具列表...")
            try:
                tools = await client.list_tools()
                print(f"   总共 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"   - {tool.name}: {tool.description}")
            except Exception as e:
                print(f"   ✗ 异常: {e}")
            
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        print("\n请确保:")
        print("1. MCP服务器正在运行 (python mysql_analysis_mcp.py)")
        print("2. 服务器地址正确: http://127.0.0.1:9000/mcp/")
        print("3. 数据库配置正确")

async def interactive_test():
    """交互式测试"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("\n" + "=" * 50)
    print("交互式测试模式")
    print("=" * 50)
    print("输入 'quit' 退出")
    
    try:
        async with Client(server_url) as client:
            tools = await client.list_tools()
            tool_names = [tool.name for tool in tools]
            
            while True:
                print(f"\n可用工具: {', '.join(tool_names)}")
                tool_name = input("\n请输入工具名称: ").strip()
                
                if tool_name.lower() == 'quit':
                    break
                
                if tool_name not in tool_names:
                    print("工具不存在！")
                    continue
                
                print("请输入参数 (JSON格式，或直接回车使用空参数):")
                params_input = input().strip()
                
                try:
                    if params_input:
                        params = json.loads(params_input)
                    else:
                        params = {}
                    
                    print(f"\n调用工具: {tool_name}")
                    print(f"参数: {params}")
                    
                    result = await client.call_tool(tool_name, params)
                    print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                except json.JSONDecodeError:
                    print("参数格式错误，请使用有效的JSON格式")
                except Exception as e:
                    print(f"调用失败: {e}")
    
    except Exception as e:
        print(f"连接失败: {e}")

def main():
    """主函数"""
    print("MySQL数据库分析MCP服务器测试工具")
    print("\n选择测试模式:")
    print("1. 自动测试")
    print("2. 交互式测试")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        asyncio.run(test_mcp_server())
    elif choice == "2":
        asyncio.run(interactive_test())
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
