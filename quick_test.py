#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试HTTP桥接器
"""

import requests
import time

def test_connection():
    """测试连接"""
    print("测试HTTP桥接器连接...")
    
    for i in range(5):
        try:
            print(f"尝试 {i+1}/5...")
            response = requests.get("http://127.0.0.1:8082/", timeout=5)
            print(f"成功! 状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            return True
        except Exception as e:
            print(f"失败: {e}")
            time.sleep(2)
    
    print("连接失败")
    return False

def test_health():
    """测试健康检查"""
    print("\n测试健康检查...")
    
    try:
        print("发送健康检查请求...")
        response = requests.get("http://127.0.0.1:8082/health", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"健康状态: {data}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"健康检查失败: {e}")

if __name__ == "__main__":
    if test_connection():
        test_health()
    else:
        print("无法连接到HTTP桥接器")
