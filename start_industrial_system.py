#!/usr/bin/env python3
"""
工业数据分析系统启动脚本
同时启动MCP服务器和Web前端
"""

import subprocess
import time
import sys
import os
import signal
import threading
from pathlib import Path

class IndustrialSystemLauncher:
    """工业数据分析系统启动器"""
    
    def __init__(self):
        self.mcp_process = None
        self.web_process = None
        self.running = True
    
    def print_banner(self):
        """打印启动横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        🏭 工业数据分析系统 - 完整启动器                        ║
║                                                              ║
║        基于MCP协议的工业数据分析解决方案                      ║
║        包含后端MCP服务器和前端Web界面                         ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
        print(banner)
    
    def check_dependencies(self):
        """检查依赖文件"""
        required_files = [
            'standalone_server.py',
            'web_server.py',
            'web_client/index.html',
            'web_client/app.js',
            'web_client/styles.css'
        ]
        
        missing_files = []
        for file in required_files:
            if not Path(file).exists():
                missing_files.append(file)
        
        if missing_files:
            print("❌ 缺少必要文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        
        print("✅ 所有依赖文件检查通过")
        return True
    
    def start_mcp_server(self):
        """启动MCP服务器"""
        print("\n🚀 启动MCP服务器...")
        try:
            self.mcp_process = subprocess.Popen(
                [sys.executable, 'standalone_server.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 等待服务器启动
            time.sleep(3)
            
            if self.mcp_process.poll() is None:
                print("✅ MCP服务器启动成功 (PID: {})".format(self.mcp_process.pid))
                print("📍 地址: http://127.0.0.1:9002/mcp/")
                return True
            else:
                print("❌ MCP服务器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动MCP服务器失败: {e}")
            return False
    
    def start_web_server(self):
        """启动Web服务器"""
        print("\n🌐 启动Web服务器...")
        try:
            self.web_process = subprocess.Popen(
                [sys.executable, 'web_server.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 等待服务器启动
            time.sleep(2)
            
            if self.web_process.poll() is None:
                print("✅ Web服务器启动成功 (PID: {})".format(self.web_process.pid))
                print("📍 地址: http://127.0.0.1:8080")
                return True
            else:
                print("❌ Web服务器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动Web服务器失败: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(5)
            
            # 检查MCP服务器
            if self.mcp_process and self.mcp_process.poll() is not None:
                print("⚠️ MCP服务器进程已退出")
                self.running = False
                break
            
            # 检查Web服务器
            if self.web_process and self.web_process.poll() is not None:
                print("⚠️ Web服务器进程已退出")
                self.running = False
                break
    
    def cleanup(self):
        """清理进程"""
        print("\n🛑 正在停止服务...")
        
        if self.web_process:
            try:
                self.web_process.terminate()
                self.web_process.wait(timeout=5)
                print("✅ Web服务器已停止")
            except:
                self.web_process.kill()
                print("🔨 强制停止Web服务器")
        
        if self.mcp_process:
            try:
                self.mcp_process.terminate()
                self.mcp_process.wait(timeout=5)
                print("✅ MCP服务器已停止")
            except:
                self.mcp_process.kill()
                print("🔨 强制停止MCP服务器")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在关闭系统...")
        self.running = False
    
    def run(self):
        """运行系统"""
        self.print_banner()
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 启动MCP服务器
            if not self.start_mcp_server():
                return False
            
            # 启动Web服务器
            if not self.start_web_server():
                self.cleanup()
                return False
            
            print("\n" + "="*60)
            print("🎉 工业数据分析系统启动完成！")
            print("="*60)
            print("📊 MCP服务器: http://127.0.0.1:9002/mcp/")
            print("🌐 Web界面:   http://127.0.0.1:8080")
            print("="*60)
            print("💡 使用说明:")
            print("   1. 在浏览器中打开 http://127.0.0.1:8080")
            print("   2. 点击'连接服务器'按钮")
            print("   3. 开始使用工业数据分析功能")
            print("="*60)
            print("按 Ctrl+C 停止系统")
            print("="*60)
            
            # 启动监控线程
            monitor_thread = threading.Thread(target=self.monitor_processes)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 主循环
            while self.running:
                time.sleep(1)
            
            return True
            
        except KeyboardInterrupt:
            print("\n收到停止信号...")
            return True
        except Exception as e:
            print(f"❌ 系统运行错误: {e}")
            return False
        finally:
            self.cleanup()
            print("👋 感谢使用工业数据分析系统！")

def main():
    """主函数"""
    launcher = IndustrialSystemLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
