#!/usr/bin/env python3
"""
完整功能测试脚本
测试MySQL数据分析MCP服务器的所有功能
"""

import asyncio
import json
import sys
from typing import Dict, Any

try:
    from fastmcp import Client
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请运行: pip install fastmcp")
    sys.exit(1)

async def test_all_features():
    """测试所有功能"""
    server_url = "http://127.0.0.1:9000/mcp/"
    
    print("🚀 开始完整功能测试")
    print("=" * 60)
    
    try:
        async with Client(server_url) as client:
            print("✅ MCP客户端连接成功")
            
            # 1. 测试数据库信息
            print("\n📊 1. 测试获取数据库信息...")
            result = await client.call_tool("get_database_info", {})
            if result.data.get('success'):
                db_info = result.data.get('database_info', {})
                print(f"   数据库: {db_info.get('database')}")
                print(f"   表数量: {db_info.get('table_count')}")
                print(f"   连接状态: {db_info.get('connection_status')}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 2. 测试统计功能
            print("\n📈 2. 测试统计功能...")
            result = await client.call_tool("get_database_statistics", {
                "table": "payment",
                "column": "amount"
            })
            if result.data.get('success'):
                stats = result.data.get('data', {})
                print(f"   记录数: {stats.get('count')}")
                print(f"   总金额: ${stats.get('sum', 0):.2f}")
                print(f"   平均金额: ${stats.get('average', 0):.2f}")
                print(f"   最小金额: ${stats.get('minimum', 0):.2f}")
                print(f"   最大金额: ${stats.get('maximum', 0):.2f}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 3. 测试异常检测
            print("\n🔍 3. 测试异常检测...")
            result = await client.call_tool("detect_data_anomalies", {
                "table": "payment",
                "column": "amount",
                "method": "zscore",
                "threshold": 2.0
            })
            if result.data.get('success'):
                anomaly_data = result.data.get('data', {})
                print(f"   总记录数: {anomaly_data.get('total_count')}")
                print(f"   异常记录数: {anomaly_data.get('anomaly_count')}")
                print(f"   异常率: {anomaly_data.get('anomaly_rate', 0):.2f}%")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 4. 测试图表生成
            print("\n📊 4. 测试图表生成...")
            result = await client.call_tool("generate_bar_chart", {
                "table": "film",
                "x_column": "rating",
                "y_column": "rental_rate",
                "title": "电影评级与租赁费用分析",
                "limit": 10
            })
            if result.data.get('success'):
                print(f"   图表类型: {result.data.get('chart_type')}")
                print(f"   图表标题: {result.data.get('title')}")
                print("   ✅ 柱状图生成成功")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 5. 测试趋势分析
            print("\n📈 5. 测试趋势分析...")
            result = await client.call_tool("analyze_data_trend", {
                "table": "payment",
                "time_column": "payment_date",
                "value_column": "amount",
                "period": "day",
                "forecast_days": 3
            })
            if result.data.get('success'):
                trend = result.data.get('trend_analysis', {})
                print(f"   趋势方向: {trend.get('direction')}")
                print(f"   数据点数: {trend.get('data_points')}")
                print(f"   趋势描述: {trend.get('description')}")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 6. 测试提醒功能
            print("\n⏰ 6. 测试提醒功能...")
            result = await client.call_tool("create_alert", {
                "alert_type": "value_threshold",
                "table": "payment",
                "column": "amount",
                "threshold": 10.0,
                "operator": ">",
                "description": "支付金额超过10美元提醒"
            })
            if result.data.get('success'):
                print(f"   提醒ID: {result.data.get('alert_id')}")
                print("   ✅ 提醒创建成功")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 7. 测试自定义SQL
            print("\n💻 7. 测试自定义SQL...")
            result = await client.call_tool("execute_custom_sql", {
                "sql": "SELECT c.name as category, COUNT(*) as film_count FROM category c JOIN film_category fc ON c.category_id = fc.category_id GROUP BY c.name ORDER BY film_count DESC LIMIT 5"
            })
            if result.data.get('success'):
                data = result.data.get('data', {})
                print(f"   查询返回: {data.get('row_count')} 条记录")
                print("   电影类别统计:")
                for row in data.get('rows', [])[:3]:
                    print(f"     {row.get('category')}: {row.get('film_count')} 部电影")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            # 8. 测试语音功能
            print("\n🔊 8. 测试语音功能...")
            result = await client.call_tool("voice_command", {
                "command": "MySQL数据分析系统测试完成，所有功能正常运行",
                "listen_for_input": False
            })
            if result.data.get('success'):
                print(f"   语音播报: {result.data.get('spoken', '已播报')}")
                print("   ✅ 语音功能正常")
            else:
                print(f"   ❌ 失败: {result.data.get('error')}")
            
            print("\n" + "=" * 60)
            print("🎉 完整功能测试完成！")
            print("您的MySQL数据分析MCP服务器已经成功运行，所有功能都可以正常使用。")
            
            print("\n📋 功能总结:")
            print("✅ 数据库连接和信息获取")
            print("✅ 统计分析（求和、平均值、最值等）")
            print("✅ 异常数据检测（Z-score和IQR方法）")
            print("✅ 数据可视化（柱状图、饼状图、趋势图）")
            print("✅ 趋势分析和预测")
            print("✅ 智能提醒系统")
            print("✅ 自定义SQL查询")
            print("✅ 语音交互功能")
            
            print("\n🌐 服务器信息:")
            print(f"   地址: {server_url}")
            print("   状态: 运行中")
            print("   数据库: Sakila (MySQL示例数据库)")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(test_all_features())
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行错误: {e}")
