# FastMCP 文档 - 第 7 部分
# 主要内容: Server Authentication
# 包含段落: 110 个
# 总行数: 1037

================================================================================

## Server Authentication
类型: docs, 行数: 61

#### Server Authentication

The simplest way to add authentication to the server is to use a bearer token scheme.

For this example, we'll quickly generate our own tokens with FastMCP's `RSAKeyPair` utility, but this may not be appropriate for production use. For more details, see the complete server-side [Bearer Auth](/servers/auth/bearer) documentation.

We'll start by creating an RSA key pair to sign and verify tokens.

```python
from fastmcp.server.auth.providers.bearer import RSAKeyPair

key_pair = RSAKeyPair.generate()
access_token = key_pair.create_token(audience="dice-server")
```

<Warning>
  FastMCP's `RSAKeyPair` utility is for development and testing only.
</Warning>

Next, we'll create a `BearerAuthProvider` to authenticate the server.

```python
from fastmcp import FastMCP
from fastmcp.server.auth import BearerAuthProvider

auth = BearerAuthProvider(
    public_key=key_pair.public_key,
    audience="dice-server",
)

mcp = FastMCP(name="Dice Roller", auth=auth)
```

Here is a complete example that you can copy/paste. For simplicity and the purposes of this example only, it will print the token to the console. **Do NOT do this in production!**

```python server.py [expandable]
from fastmcp import FastMCP
from fastmcp.server.auth import BearerAuthProvider
from fastmcp.server.auth.providers.bearer import RSAKeyPair
import random

key_pair = RSAKeyPair.generate()
access_token = key_pair.create_token(audience="dice-server")

auth = BearerAuthProvider(
    public_key=key_pair.public_key,
    audience="dice-server",
)

mcp = FastMCP(name="Dice Roller", auth=auth)

@mcp.tool
def roll_dice(n_dice: int) -> list[int]:
    """Roll `n_dice` 6-sided dice and return the results."""
    return [random.randint(1, 6) for _ in range(n_dice)]

if __name__ == "__main__":
    print(f"\n---\n\n🔑 Dice Roller access token:\n\n{access_token}\n\n---\n")
    mcp.run(transport="http", port=8000)
```


------------------------------------------------------------

## Client Authentication
类型: docs, 行数: 22

#### Client Authentication

If you try to call the authenticated server with the same OpenAI code we wrote earlier, you'll get an error like this:

```python
pythonAPIStatusError: Error code: 424 - {
    "error": {
        "message": "Error retrieving tool list from MCP server: 'dice_server'. Http status code: 401 (Unauthorized)",
        "type": "external_connector_error",
        "param": "tools",
        "code": "http_error"
    }
}
```

As expected, the server is rejecting the request because it's not authenticated.

To authenticate the client, you can pass the token in the `Authorization` header with the `Bearer` scheme:

```python {4, 7, 19-21} [expandable]
from openai import OpenAI


------------------------------------------------------------

## Your server URL (replace with your actual URL)
类型: docs, 行数: 3

# Your server URL (replace with your actual URL)
url = 'https://your-server-url.com'


------------------------------------------------------------

## Your access token (replace with your actual token)
类型: docs, 行数: 27

# Your access token (replace with your actual token)
access_token = 'your-access-token'

client = OpenAI()

resp = client.responses.create(
    model="gpt-4.1",
    tools=[
        {
            "type": "mcp",
            "server_label": "dice_server",
            "server_url": f"{url}/mcp/",
            "require_approval": "never",
            "headers": {
                "Authorization": f"Bearer {access_token}"
            }
        },
    ],
    input="Roll a few dice!",
)

print(resp.output_text)
```

You should now see the dice roll results in the output.



------------------------------------------------------------

## OpenAPI 🤝 FastMCP
类型: api, 行数: 22

# OpenAPI 🤝 FastMCP
Source: https://gofastmcp.com/integrations/openapi

Generate MCP servers from any OpenAPI specification

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

FastMCP can automatically generate an MCP server from any OpenAPI specification, allowing AI models to interact with existing APIs through the MCP protocol. Instead of manually creating tools and resources, you provide an OpenAPI spec and FastMCP intelligently converts API endpoints into the appropriate MCP components.

<Tip>
  Generating MCP servers from OpenAPI is a great way to get started with FastMCP, but in practice LLMs achieve **significantly better performance** with well-designed and curated MCP servers than with auto-converted OpenAPI servers. This is especially true for complex APIs with many endpoints and parameters.
</Tip>


------------------------------------------------------------

## Create a Server
类型: docs, 行数: 8

## Create a Server

To convert an OpenAPI specification to an MCP server, use the `FastMCP.from_openapi()` class method:

```python server.py
import httpx
from fastmcp import FastMCP


------------------------------------------------------------

## Create an HTTP client for your API
类型: api, 行数: 3

# Create an HTTP client for your API
client = httpx.AsyncClient(base_url="https://api.example.com")


------------------------------------------------------------

## Load your OpenAPI spec
类型: api, 行数: 3

# Load your OpenAPI spec 
openapi_spec = httpx.get("https://api.example.com/openapi.json").json()


------------------------------------------------------------

## Create the MCP server
类型: docs, 行数: 11

# Create the MCP server
mcp = FastMCP.from_openapi(
    openapi_spec=openapi_spec,
    client=client,
    name="My API Server"
)

if __name__ == "__main__":
    mcp.run()
```


------------------------------------------------------------

## Authentication
类型: docs, 行数: 8

### Authentication

If your API requires authentication, configure it on the HTTP client:

```python
import httpx
from fastmcp import FastMCP


------------------------------------------------------------

## Bearer token authentication
类型: docs, 行数: 6

# Bearer token authentication
api_client = httpx.AsyncClient(
    base_url="https://api.example.com",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)


------------------------------------------------------------

## Create MCP server with authenticated client
类型: docs, 行数: 8

# Create MCP server with authenticated client
mcp = FastMCP.from_openapi(
    openapi_spec=spec, 
    client=api_client,
    timeout=30.0  # 30 second timeout for all requests
)
```


------------------------------------------------------------

## Route Mapping
类型: docs, 行数: 20

## Route Mapping

By default, FastMCP converts **every endpoint** in your OpenAPI specification into an MCP **Tool**. This provides a simple, predictable starting point that ensures all your API's functionality is immediately available to the vast majority of LLM clients which only support MCP tools.

While this is a pragmatic default for maximum compatibility, you can easily customize this behavior. Internally, FastMCP uses an ordered list of `RouteMap` objects to determine how to map OpenAPI routes to various MCP component types.

Each `RouteMap` specifies a combination of methods, patterns, and tags, as well as a corresponding MCP component type. Each OpenAPI route is checked against each `RouteMap` in order, and the first one that matches every criteria is used to determine its converted MCP type. A special type, `EXCLUDE`, can be used to exclude routes from the MCP server entirely.

* **Methods**: HTTP methods to match (e.g. `["GET", "POST"]` or `"*"` for all)
* **Pattern**: Regex pattern to match the route path (e.g. `r"^/users/.*"` or `r".*"` for all)
* **Tags**: A set of OpenAPI tags that must all be present. An empty set (`{}`) means no tag filtering, so the route matches regardless of its tags.
* **MCP type**: What MCP component type to create (`TOOL`, `RESOURCE`, `RESOURCE_TEMPLATE`, or `EXCLUDE`)
* **MCP tags**: A set of custom tags to add to components created from matching routes

Here is FastMCP's default rule:

```python
from fastmcp.server.openapi import RouteMap, MCPType

DEFAULT_ROUTE_MAPPINGS = [

------------------------------------------------------------

## All routes become tools
类型: docs, 行数: 5

    # All routes become tools
    RouteMap(mcp_type=MCPType.TOOL),
]
```


------------------------------------------------------------

## Custom Route Maps
类型: docs, 行数: 10

### Custom Route Maps

When creating your FastMCP server, you can customize routing behavior by providing your own list of `RouteMap` objects. Your custom maps are processed before the default route maps, and routes will be assigned to the first matching custom map.

For example, prior to FastMCP 2.8.0, GET requests were automatically mapped to `Resource` and `ResourceTemplate` components based on whether they had path parameters. (This was changed solely for client compatibility reasons.) You can restore this behavior by providing custom route maps:

```python
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType


------------------------------------------------------------

## Restore pre-2.8.0 semantic mapping
类型: docs, 行数: 2

# Restore pre-2.8.0 semantic mapping
semantic_maps = [

------------------------------------------------------------

## GET requests with path parameters become ResourceTemplates
类型: docs, 行数: 2

    # GET requests with path parameters become ResourceTemplates
    RouteMap(methods=["GET"], pattern=r".*\{.*\}.*", mcp_type=MCPType.RESOURCE_TEMPLATE),

------------------------------------------------------------

## All other GET requests become Resources
类型: docs, 行数: 23

    # All other GET requests become Resources
    RouteMap(methods=["GET"], pattern=r".*", mcp_type=MCPType.RESOURCE),
]

mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    route_maps=semantic_maps,
)
```

With these maps, `GET` requests are handled semantically, and all other methods (`POST`, `PUT`, etc.) will fall through to the default rule and become `Tool`s.

Here is a more complete example that uses custom route maps to convert all `GET` endpoints under `/analytics/` to tools while excluding all admin endpoints and all routes tagged "internal". All other routes will be handled by the default rules:

```python
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType

mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    route_maps=[

------------------------------------------------------------

## Analytics `GET` endpoints are tools
类型: docs, 行数: 7

        # Analytics `GET` endpoints are tools
        RouteMap(
            methods=["GET"], 
            pattern=r"^/analytics/.*", 
            mcp_type=MCPType.TOOL,
        ),


------------------------------------------------------------

## Exclude all admin endpoints
类型: docs, 行数: 6

        # Exclude all admin endpoints
        RouteMap(
            pattern=r"^/admin/.*", 
            mcp_type=MCPType.EXCLUDE,
        ),


------------------------------------------------------------

## Exclude all routes tagged "internal"
类型: docs, 行数: 13

        # Exclude all routes tagged "internal"
        RouteMap(
            tags={"internal"},
            mcp_type=MCPType.EXCLUDE,
        ),
    ],
)
```

<Tip>
  The default route maps are always applied after your custom maps, so you do not have to create route maps for every possible route.
</Tip>


------------------------------------------------------------

## Excluding Routes
类型: docs, 行数: 30

### Excluding Routes

To exclude routes from the MCP server, use a route map to assign them to `MCPType.EXCLUDE`.

You can use this to remove sensitive or internal routes by targeting them specifically:

```python
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType

mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    route_maps=[
        RouteMap(pattern=r"^/admin/.*", mcp_type=MCPType.EXCLUDE),
        RouteMap(tags={"internal"}, mcp_type=MCPType.EXCLUDE),
    ],
)
```

Or you can use a catch-all rule to exclude everything that your maps don't handle explicitly:

```python
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType

mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    route_maps=[

------------------------------------------------------------

## custom mapping logic goes here
类型: docs, 行数: 1

        # custom mapping logic goes here

------------------------------------------------------------

## ... your specific route maps ...
类型: docs, 行数: 1

        # ... your specific route maps ...

------------------------------------------------------------

## exclude all remaining routes
类型: docs, 行数: 10

        # exclude all remaining routes
        RouteMap(mcp_type=MCPType.EXCLUDE),
    ],
)
```

<Tip>
  Using a catch-all exclusion rule will prevent the default route mappings from being applied, since it will match every remaining route. This is useful if you want to explicitly allow-list certain routes.
</Tip>


------------------------------------------------------------

## Advanced Route Mapping
类型: docs, 行数: 18

### Advanced Route Mapping

<VersionBadge version="2.5.0" />

For advanced use cases that require more complex logic, you can provide a `route_map_fn` callable. After the route map logic is applied, this function is called on each matched route and its assigned MCP component type. It can optionally return a different component type to override the mapped assignment. If it returns `None`, the assigned type is used.

In addition to more precise targeting of methods, patterns, and tags, this function can access any additional OpenAPI metadata about the route.

<Tip>
  The `route_map_fn` **is** called on routes that matched `MCPType.EXCLUDE` in your custom maps, giving you an opportunity to override the exclusion.
</Tip>

```python
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType, HTTPRoute

def custom_route_mapper(route: HTTPRoute, mcp_type: MCPType) -> MCPType | None:
    """Advanced route type mapping."""

------------------------------------------------------------

## Convert all admin routes to tools regardless of HTTP method
类型: api, 行数: 7

    # Convert all admin routes to tools regardless of HTTP method
    if "/admin/" in route.path:
        return MCPType.TOOL

    elif "internal" in route.tags:
        return MCPType.EXCLUDE
    

------------------------------------------------------------

## Convert user detail routes to templates even if they're POST
类型: docs, 行数: 4

    # Convert user detail routes to templates even if they're POST
    elif route.path.startswith("/users/") and route.method == "POST":
        return MCPType.RESOURCE_TEMPLATE
    

------------------------------------------------------------

## Use defaults for all other routes
类型: docs, 行数: 10

    # Use defaults for all other routes
    return None

mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    route_map_fn=custom_route_mapper,
)
```


------------------------------------------------------------

## Customization
类型: docs, 行数: 2

## Customization


------------------------------------------------------------

## Component Names
类型: docs, 行数: 28

### Component Names

<VersionBadge version="2.5.0" />

FastMCP automatically generates names for MCP components based on the OpenAPI specification. By default, it uses the `operationId` from your OpenAPI spec, up to the first double underscore (`__`).

All component names are automatically:

* **Slugified**: Spaces and special characters are converted to underscores or removed
* **Truncated**: Limited to 56 characters maximum to ensure compatibility
* **Unique**: If multiple components have the same name, a number is automatically appended to make them unique

For more control over component names, you can provide an `mcp_names` dictionary that maps `operationId` values to your desired names. The `operationId` must be exactly as it appears in the OpenAPI spec. The provided name will always be slugified and truncated.

```python
mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    mcp_names={
        "list_users__with_pagination": "user_list",
        "create_user__admin_required": "create_user", 
        "get_user_details__admin_required": "user_detail",
    }
)
```

Any `operationId` not found in `mcp_names` will use the default strategy (operationId up to the first `__`).


------------------------------------------------------------

## Tags
类型: docs, 行数: 6

### Tags

<VersionBadge version="2.8.0" />

FastMCP provides several ways to add tags to your MCP components, allowing you to categorize and organize them for better discoverability and filtering. Tags are combined from multiple sources to create the final set of tags on each component.


------------------------------------------------------------

## RouteMap Tags
类型: docs, 行数: 11

#### RouteMap Tags

You can add custom tags to components created from specific routes using the `mcp_tags` parameter in `RouteMap`. These tags will be applied to all components created from routes that match that particular route map.

```python
from fastmcp.server.openapi import RouteMap, MCPType

mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    route_maps=[

------------------------------------------------------------

## Add custom tags to all POST endpoints
类型: docs, 行数: 8

        # Add custom tags to all POST endpoints
        RouteMap(
            methods=["POST"],
            pattern=r".*",
            mcp_type=MCPType.TOOL,
            mcp_tags={"write-operation", "api-mutation"}
        ),
        

------------------------------------------------------------

## Add different tags to detail view endpoints
类型: docs, 行数: 8

        # Add different tags to detail view endpoints
        RouteMap(
            methods=["GET"],
            pattern=r".*\{.*\}.*",
            mcp_type=MCPType.RESOURCE_TEMPLATE,
            mcp_tags={"detail-view", "parameterized"}
        ),
        

------------------------------------------------------------

## Add tags to list endpoints
类型: docs, 行数: 11

        # Add tags to list endpoints
        RouteMap(
            methods=["GET"],
            pattern=r".*",
            mcp_type=MCPType.RESOURCE,
            mcp_tags={"list-data", "collection"}
        ),
    ],
)
```


------------------------------------------------------------

## Global Tags
类型: docs, 行数: 12

#### Global Tags

You can add tags to **all** components by providing a `tags` parameter when creating your MCP server. These global tags will be applied to every component created from your OpenAPI specification.

```python
mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    tags={"api-v2", "production", "external"}
)
```


------------------------------------------------------------

## Advanced Customization
类型: docs, 行数: 24

### Advanced Customization

<VersionBadge version="2.5.0" />

By default, FastMCP creates MCP components using a variety of metadata from the OpenAPI spec, such as incorporating the OpenAPI description into the MCP component description.

At times you may want to modify those MCP components in a variety of ways, such as adding LLM-specific instructions or tags. For fine-grained customization, you can provide a `mcp_component_fn` when creating the MCP server. After each MCP component has been created, this function is called on it and has the opportunity to modify it in-place.

<Tip>
  Your `mcp_component_fn` is expected to modify the component in-place, not to return a new component. The result of the function is ignored.
</Tip>

```python
from fastmcp.server.openapi import (
    HTTPRoute, 
    OpenAPITool, 
    OpenAPIResource, 
    OpenAPIResourceTemplate,
)

def customize_components(
    route: HTTPRoute, 
    component: OpenAPITool | OpenAPIResource | OpenAPIResourceTemplate,
) -> None:

------------------------------------------------------------

## Add custom tags to all components
类型: docs, 行数: 3

    # Add custom tags to all components
    component.tags.add("openapi")
    

------------------------------------------------------------

## Customize based on component type
类型: docs, 行数: 15

    # Customize based on component type
    if isinstance(component, OpenAPITool):
        component.description = f"🔧 {component.description} (via API)"
    
    if isinstance(component, OpenAPIResource):
        component.description = f"📊 {component.description}"
        component.tags.add("data")

mcp = FastMCP.from_openapi(
    openapi_spec=spec,
    client=client,
    mcp_component_fn=customize_components,
)
```


------------------------------------------------------------

## Request Parameter Handling
类型: docs, 行数: 4

## Request Parameter Handling

FastMCP intelligently handles different types of parameters in OpenAPI requests:


------------------------------------------------------------

## Query Parameters
类型: docs, 行数: 5

### Query Parameters

By default, FastMCP only includes query parameters that have non-empty values. Parameters with `None` values or empty strings are automatically filtered out.

```python

------------------------------------------------------------

## When calling this tool...
类型: docs, 行数: 8

# When calling this tool...
await client.call_tool("search_products", {
    "category": "electronics",  # ✅ Included
    "min_price": 100,           # ✅ Included  
    "max_price": None,          # ❌ Excluded
    "brand": "",                # ❌ Excluded
})


------------------------------------------------------------

## The HTTP request will be: GET /products?category=electronics&min_price=100
类型: docs, 行数: 3

# The HTTP request will be: GET /products?category=electronics&min_price=100
```


------------------------------------------------------------

## Path Parameters
类型: docs, 行数: 9

### Path Parameters

Path parameters are typically required by REST APIs. FastMCP:

* Filters out `None` values
* Validates that all required path parameters are provided
* Raises clear errors for missing required parameters

```python

------------------------------------------------------------

## ✅ This works
类型: docs, 行数: 3

# ✅ This works
await client.call_tool("get_user", {"user_id": 123})


------------------------------------------------------------

## ❌ This raises: "Missing required path parameters: {'user_id'}"
类型: docs, 行数: 4

# ❌ This raises: "Missing required path parameters: {'user_id'}"
await client.call_tool("get_user", {"user_id": None})
```


------------------------------------------------------------

## Array Parameters
类型: docs, 行数: 8

### Array Parameters

FastMCP handles array parameters according to OpenAPI specifications:

* **Query arrays**: Serialized based on the `explode` parameter (default: `True`)
* **Path arrays**: Serialized as comma-separated values (OpenAPI 'simple' style)

```python

------------------------------------------------------------

## Query array with explode=true (default)
类型: docs, 行数: 1

# Query array with explode=true (default)

------------------------------------------------------------

## ?tags=red&tags=blue&tags=green
类型: docs, 行数: 2

# ?tags=red&tags=blue&tags=green


------------------------------------------------------------

## Query array with explode=false
类型: docs, 行数: 1

# Query array with explode=false  

------------------------------------------------------------

## ?tags=red,blue,green
类型: docs, 行数: 2

# ?tags=red,blue,green


------------------------------------------------------------

## Path array (always comma-separated)
类型: docs, 行数: 1

# Path array (always comma-separated)

------------------------------------------------------------

## /items/red,blue,green
类型: docs, 行数: 3

# /items/red,blue,green
```


------------------------------------------------------------

## Headers
类型: docs, 行数: 5

### Headers

Header parameters are automatically converted to strings and included in the HTTP request.



------------------------------------------------------------

## Starlette / ASGI 🤝 FastMCP
类型: docs, 行数: 23

# Starlette / ASGI 🤝 FastMCP
Source: https://gofastmcp.com/integrations/starlette

Integrate FastMCP servers into ASGI applications

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.3.1" />

FastMCP servers can be integrated into existing ASGI applications, allowing you to add MCP functionality to your web applications. This is useful for:

* Adding MCP functionality to an existing website or API
* Mounting MCP servers under specific URL paths
* Combining multiple services in a single application
* Leveraging existing authentication and middleware


------------------------------------------------------------

## Basic Usage
类型: docs, 行数: 17

## Basic Usage

To integrate a FastMCP server into an ASGI application, use the `http_app()` method to obtain a Starlette application instance:

<Tip>
  The `http_app()` method is new in FastMCP 2.3.2. In older versions, use `sse_app()` for SSE transport or `streamable_http_app()` for Streamable HTTP transport.
</Tip>

```python
from fastmcp import FastMCP

mcp = FastMCP("MyServer")

@mcp.tool
def hello(name: str) -> str:
    return f"Hello, {name}!"


------------------------------------------------------------

## Get a Starlette app instance for Streamable HTTP transport (recommended)
类型: docs, 行数: 3

# Get a Starlette app instance for Streamable HTTP transport (recommended)
http_app = mcp.http_app()


------------------------------------------------------------

## For legacy SSE transport (deprecated)
类型: docs, 行数: 6

# For legacy SSE transport (deprecated)
sse_app = mcp.http_app(transport="sse")
```

The returned Starlette application can be integrated with other ASGI-compatible web frameworks. The MCP server's endpoint is mounted at `/mcp/` for Streamable HTTP transport and `/sse/` for SSE transport.


------------------------------------------------------------

## Configuration Options
类型: setup, 行数: 5

### Configuration Options

You can customize the endpoint path and access the FastMCP server instance:

```python

------------------------------------------------------------

## Custom endpoint path
类型: docs, 行数: 3

# Custom endpoint path
http_app = mcp.http_app(path="/custom-mcp-path")


------------------------------------------------------------

## Access the FastMCP server from middleware/routes
类型: docs, 行数: 1

# Access the FastMCP server from middleware/routes

------------------------------------------------------------

## The server is available at: request.app.state.fastmcp_server
类型: docs, 行数: 3

# The server is available at: request.app.state.fastmcp_server
```


------------------------------------------------------------

## Adding Custom Routes
类型: docs, 行数: 18

### Adding Custom Routes

You can add custom web routes directly to your FastMCP server using the `@custom_route` decorator:

```python
from fastmcp import FastMCP
from starlette.requests import Request
from starlette.responses import JSONResponse

mcp = FastMCP("MyServer")

@mcp.custom_route("/api/status", methods=["GET"])
async def get_status(request: Request):
    return JSONResponse({"server": "running"})

http_app = mcp.http_app()
```


------------------------------------------------------------

## Health Check Endpoints
类型: docs, 行数: 20

#### Health Check Endpoints

Health checks are commonly needed for monitoring and load balancing:

```python
from fastmcp import FastMCP
from starlette.requests import Request
from starlette.responses import JSONResponse

mcp = FastMCP("MyServer")

@mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request):
    return JSONResponse({"status": "healthy"})

http_app = mcp.http_app()
```

The health endpoint will be available at `/health` alongside your MCP endpoint at `/mcp/`.


------------------------------------------------------------

## Starlette Integration
类型: docs, 行数: 9

## Starlette Integration

Mount your FastMCP server in another Starlette application:

```python
from fastmcp import FastMCP
from starlette.applications import Starlette
from starlette.routing import Mount


------------------------------------------------------------

## Create your FastMCP server
类型: docs, 行数: 7

# Create your FastMCP server
mcp = FastMCP("MyServer")

@mcp.tool
def analyze(data: str) -> dict:
    return {"result": f"Analyzed: {data}"}


------------------------------------------------------------

## Create the ASGI app
类型: docs, 行数: 3

# Create the ASGI app
mcp_app = mcp.http_app(path='/mcp')


------------------------------------------------------------

## Create a Starlette app and mount the MCP server
类型: docs, 行数: 4

# Create a Starlette app and mount the MCP server
app = Starlette(
    routes=[
        Mount("/mcp-server", app=mcp_app),

------------------------------------------------------------

## Add other routes as needed
类型: docs, 行数: 12

        # Add other routes as needed
    ],
    lifespan=mcp_app.lifespan,
)
```

The MCP endpoint will be available at `/mcp-server/mcp/` of the resulting Starlette app.

<Warning>
  For Streamable HTTP transport, you **must** pass the lifespan context from the FastMCP app to the resulting Starlette app, as nested lifespans are not recognized. Otherwise, the FastMCP server's session manager will not be properly initialized.
</Warning>


------------------------------------------------------------

## Nested Mounts
类型: docs, 行数: 9

### Nested Mounts

You can create complex routing structures by nesting mounts:

```python
from fastmcp import FastMCP
from starlette.applications import Starlette
from starlette.routing import Mount


------------------------------------------------------------

## Create your FastMCP server
类型: docs, 行数: 3

# Create your FastMCP server
mcp = FastMCP("MyServer")


------------------------------------------------------------

## Create the ASGI app
类型: docs, 行数: 3

# Create the ASGI app
mcp_app = mcp.http_app(path='/mcp')


------------------------------------------------------------

## Create nested application structure
类型: docs, 行数: 10

# Create nested application structure
inner_app = Starlette(routes=[Mount("/inner", app=mcp_app)])
app = Starlette(
    routes=[Mount("/outer", app=inner_app)],
    lifespan=mcp_app.lifespan,
)
```

In this setup, the MCP server is accessible at the `/outer/inner/mcp/` path.


------------------------------------------------------------

## Custom Middleware
类型: docs, 行数: 11

## Custom Middleware

<VersionBadge version="2.3.2" />

Add custom Starlette middleware to your FastMCP ASGI apps by passing a list of middleware instances:

```python
from fastmcp import FastMCP
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware


------------------------------------------------------------

## Create your FastMCP server
类型: docs, 行数: 3

# Create your FastMCP server
mcp = FastMCP("MyServer")


------------------------------------------------------------

## Define custom middleware
类型: docs, 行数: 10

# Define custom middleware
custom_middleware = [
    Middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_methods=["*"],
        allow_headers=["*"],
    )
]


------------------------------------------------------------

## Create ASGI app with middleware
类型: docs, 行数: 4

# Create ASGI app with middleware
http_app = mcp.http_app(custom_middleware=custom_middleware)
```


------------------------------------------------------------

## Running the Server
类型: docs, 行数: 17

## Running the Server

To run your ASGI application, use an ASGI server like `uvicorn`:

```python
import uvicorn

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

Or from the command line:

```bash
uvicorn path.to.your.app:app --host 0.0.0.0 --port 8000
```


------------------------------------------------------------

## Framework-Specific Integration
类型: docs, 行数: 2

## Framework-Specific Integration


------------------------------------------------------------

## FastAPI
类型: api, 行数: 4

### FastAPI

For FastAPI-specific integration patterns including both mounting MCP servers into FastAPI apps and generating MCP servers from FastAPI apps, see the [FastAPI Integration guide](/integrations/fastapi).


------------------------------------------------------------

## Other ASGI Frameworks
类型: docs, 行数: 9

### Other ASGI Frameworks

The patterns shown here work with any ASGI-compatible framework. The key requirements are:

1. Mount the FastMCP ASGI app at your desired path
2. Pass the lifespan context to your root application
3. Configure any necessary middleware or authentication



------------------------------------------------------------

## FastMCP CLI
类型: docs, 行数: 20

# FastMCP CLI
Source: https://gofastmcp.com/patterns/cli

Learn how to use the FastMCP command-line interface

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

FastMCP provides a command-line interface (CLI) that makes it easy to run, develop, and install your MCP servers. The CLI is automatically installed when you install FastMCP.

```bash
fastmcp --help
```


------------------------------------------------------------

## Commands Overview
类型: docs, 行数: 10

## Commands Overview

| Command   | Purpose                                         | Dependency Management                                                                                             |
| --------- | ----------------------------------------------- | ----------------------------------------------------------------------------------------------------------------- |
| `run`     | Run a FastMCP server directly                   | Uses your current environment; you are responsible for ensuring all dependencies are available                    |
| `dev`     | Run a server with the MCP Inspector for testing | Creates an isolated environment; dependencies must be explicitly specified with `--with` and/or `--with-editable` |
| `install` | Install a server in MCP client applications     | Creates an isolated environment; dependencies must be explicitly specified with `--with` and/or `--with-editable` |
| `inspect` | Generate a JSON report about a FastMCP server   | Uses your current environment; you are responsible for ensuring all dependencies are available                    |
| `version` | Display version information                     | N/A                                                                                                               |


------------------------------------------------------------

## Command Details
类型: docs, 行数: 2

## Command Details


------------------------------------------------------------

## `run`
类型: docs, 行数: 12

### `run`

Run a FastMCP server directly or proxy a remote server.

```bash
fastmcp run server.py
```

<Tip>
  This command runs the server directly in your current Python environment. You are responsible for ensuring all dependencies are available.
</Tip>


------------------------------------------------------------

## Options
类型: docs, 行数: 11

#### Options

| Option    | Flag                | Description                                                                     |
| --------- | ------------------- | ------------------------------------------------------------------------------- |
| Transport | `--transport`, `-t` | Transport protocol to use (`stdio`, `http`, or `sse`)                           |
| Host      | `--host`            | Host to bind to when using http transport (default: 127.0.0.1)                  |
| Port      | `--port`, `-p`      | Port to bind to when using http transport (default: 8000)                       |
| Path      | `--path`            | Path to bind to when using http transport (default: `/mcp/` or `/sse/` for SSE) |
| Log Level | `--log-level`, `-l` | Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)                               |
| No Banner | `--no-banner`       | Disable the startup banner display                                              |


------------------------------------------------------------

## Server Specification
类型: docs, 行数: 17

#### Server Specification

<VersionBadge version="2.3.5" />

The server can be specified in three ways:

1. `server.py` - imports the module and looks for a FastMCP object named `mcp`, `server`, or `app`. Errors if no such object is found.
2. `server.py:custom_name` - imports and uses the specified server object
3. `http://server-url/path` or `https://server-url/path` - connects to a remote server and creates a proxy

<Tip>
  When using `fastmcp run` with a local file, it **ignores** the `if __name__ == "__main__"` block entirely. Instead, it finds your server object and calls its `run()` method directly with the transport options you specify. This means you can use `fastmcp run` to override the transport specified in your code.
</Tip>

For example, if your code contains:

```python

------------------------------------------------------------

## server.py
类型: docs, 行数: 10

# server.py
from fastmcp import FastMCP

mcp = FastMCP("MyServer")

@mcp.tool
def hello(name: str) -> str:
    return f"Hello, {name}!"

if __name__ == "__main__":

------------------------------------------------------------

## This is ignored when using `fastmcp run`!
类型: docs, 行数: 13

    # This is ignored when using `fastmcp run`!
    mcp.run(transport="stdio")
```

You can run it with Streamable HTTP transport regardless of what's in the `__main__` block:

```bash
fastmcp run server.py --transport http --port 8000
```

**Examples**

```bash

------------------------------------------------------------

## Run a local server with Streamable HTTP transport on a custom port
类型: docs, 行数: 3

# Run a local server with Streamable HTTP transport on a custom port
fastmcp run server.py --transport http --port 8000


------------------------------------------------------------

## Connect to a remote server and proxy as a stdio server
类型: docs, 行数: 3

# Connect to a remote server and proxy as a stdio server
fastmcp run https://example.com/mcp-server


------------------------------------------------------------

## Connect to a remote server with specified log level
类型: docs, 行数: 4

# Connect to a remote server with specified log level
fastmcp run https://example.com/mcp-server --log-level DEBUG
```


------------------------------------------------------------

## `dev`
类型: docs, 行数: 31

### `dev`

Run a MCP server with the [MCP Inspector](https://github.com/modelcontextprotocol/inspector) for testing.

```bash
fastmcp dev server.py
```

<Tip>
  This command runs your server in an isolated environment. All dependencies must be explicitly specified using the `--with` and/or `--with-editable` options.
</Tip>

<Warning>
  The `dev` command is a shortcut for testing a server over STDIO only. When the Inspector launches, you may need to:

  1. Select "STDIO" from the transport dropdown
  2. Connect manually

  This command does not support HTTP testing. To test a server over Streamable HTTP or SSE:

  1. Start your server manually with the appropriate transport using either the command line:
     ```bash
     fastmcp run server.py --transport http
     ```
     or by setting the transport in your code:
     ```bash
     python server.py  # Assuming your __main__ block sets Streamable HTTP transport
     ```
  2. Open the MCP Inspector separately and connect to your running server
</Warning>


------------------------------------------------------------

## Options
类型: docs, 行数: 13

#### Options

| Option              | Flag                    | Description                                                     |
| ------------------- | ----------------------- | --------------------------------------------------------------- |
| Editable Package    | `--with-editable`, `-e` | Directory containing pyproject.toml to install in editable mode |
| Additional Packages | `--with`                | Additional packages to install (can be used multiple times)     |
| Inspector Version   | `--inspector-version`   | Version of the MCP Inspector to use                             |
| UI Port             | `--ui-port`             | Port for the MCP Inspector UI                                   |
| Server Port         | `--server-port`         | Port for the MCP Inspector Proxy server                         |

**Example**

```bash

------------------------------------------------------------

## Run dev server with editable mode and additional packages
类型: docs, 行数: 4

# Run dev server with editable mode and additional packages
fastmcp dev server.py -e . --with pandas --with matplotlib
```


------------------------------------------------------------

## `install`
类型: setup, 行数: 28

### `install`

<VersionBadge version="2.10.3" />

Install a MCP server in MCP client applications. FastMCP currently supports the following clients:

* **Claude Code** - Installs via Claude Code's built-in MCP management system
* **Claude Desktop** - Installs via direct configuration file modification
* **Cursor** - Installs via deeplink that opens Cursor for user confirmation
* **MCP JSON** - Generates standard MCP JSON configuration for manual use

```bash
fastmcp install claude-code server.py
fastmcp install claude-desktop server.py
fastmcp install cursor server.py
fastmcp install mcp-json server.py
```

Note that for security reasons, MCP clients usually run every server in a completely isolated environment. Therefore, all dependencies must be explicitly specified using the `--with` and/or `--with-editable` options (following `uv` conventions) or by attaching them to your server in code via the `dependencies` parameter. You should not assume that the MCP server will have access to your local environment.

<Warning>
  **`uv` must be installed and available in your system PATH**. Both Claude Desktop and Cursor run in isolated environments and need `uv` to manage dependencies. On macOS, install `uv` globally with Homebrew for Claude Desktop compatibility: `brew install uv`.
</Warning>

<Tip>
  **FastMCP `install` commands focus on local server files with STDIO transport.** For remote servers running with HTTP or SSE transport, use your client's native configuration - FastMCP's value is simplifying the complex local setup with dependencies and `uv` commands.
</Tip>


------------------------------------------------------------

## Server Specification
类型: docs, 行数: 7

#### Server Specification

The `install` command supports the same `file.py:object` notation as the `run` command:

1. `server.py` - imports the module and looks for a FastMCP object named `mcp`, `server`, or `app`. Errors if no such object is found.
2. `server.py:custom_name` - imports and uses the specified server object


------------------------------------------------------------

## Options
类型: docs, 行数: 13

#### Options

| Option                | Flag                    | Description                                                                   |
| --------------------- | ----------------------- | ----------------------------------------------------------------------------- |
| Server Name           | `--name`, `-n`          | Custom name for the server (defaults to server's name attribute or file name) |
| Editable Package      | `--with-editable`, `-e` | Directory containing pyproject.toml to install in editable mode               |
| Additional Packages   | `--with`                | Additional packages to install (can be used multiple times)                   |
| Environment Variables | `--env-var`, `-v`       | Environment variables in KEY=VALUE format (can be used multiple times)        |
| Environment File      | `--env-file`, `-f`      | Load environment variables from a .env file                                   |

**Examples**

```bash

------------------------------------------------------------

## Auto-detects server object (looks for 'mcp', 'server', or 'app')
类型: docs, 行数: 3

# Auto-detects server object (looks for 'mcp', 'server', or 'app')
fastmcp install claude-desktop server.py


------------------------------------------------------------

## Uses specific server object
类型: docs, 行数: 3

# Uses specific server object
fastmcp install claude-desktop server.py:my_server


------------------------------------------------------------

## With custom name and dependencies
类型: docs, 行数: 3

# With custom name and dependencies
fastmcp install claude-desktop server.py:my_server -n "My Analysis Server" --with pandas


------------------------------------------------------------

## Install in Claude Code with environment variables
类型: setup, 行数: 3

# Install in Claude Code with environment variables
fastmcp install claude-code server.py --env-var API_KEY=secret --env-var DEBUG=true


------------------------------------------------------------

## Install in Cursor with environment variables
类型: setup, 行数: 3

# Install in Cursor with environment variables
fastmcp install cursor server.py --env-var API_KEY=secret --env-var DEBUG=true


------------------------------------------------------------

## Install with environment file
类型: setup, 行数: 3

# Install with environment file
fastmcp install cursor server.py --env-file .env


------------------------------------------------------------

## Generate MCP JSON configuration
类型: setup, 行数: 3

# Generate MCP JSON configuration
fastmcp install mcp-json server.py --name "My Server" --with pandas


------------------------------------------------------------

## Copy JSON configuration to clipboard
类型: setup, 行数: 4

# Copy JSON configuration to clipboard
fastmcp install mcp-json server.py --copy
```


------------------------------------------------------------

## MCP JSON Generation
类型: docs, 行数: 38

#### MCP JSON Generation

The `mcp-json` subcommand generates standard MCP JSON configuration that can be used with any MCP-compatible client. This is useful when:

* Working with MCP clients not directly supported by FastMCP
* Creating configuration for CI/CD environments
* Sharing server configurations with others
* Integration with custom tooling

The generated JSON follows the standard `mcpServers` format used by Claude Desktop, VS Code, Cursor, and other MCP clients:

```json
{
  "mcpServers": {
    "server-name": {
      "command": "uv",
      "args": [
        "run",
        "--with",
        "fastmcp",
        "fastmcp",
        "run",
        "/path/to/server.py"
      ],
      "env": {
        "API_KEY": "value"
      }
    }
  }
}
```

**Options specific to mcp-json:**

| Option            | Flag     | Description                                                   |
| ----------------- | -------- | ------------------------------------------------------------- |
| Copy to Clipboard | `--copy` | Copy configuration to clipboard instead of printing to stdout |


------------------------------------------------------------

## `inspect`
类型: docs, 行数: 13

### `inspect`

<VersionBadge version="2.9.0" />

Generate a detailed JSON report about a FastMCP server, including information about its tools, prompts, resources, and capabilities.

```bash
fastmcp inspect server.py
```

The command supports the same server specification format as `run` and `install`:

```bash

------------------------------------------------------------

## Auto-detect server object
类型: docs, 行数: 3

# Auto-detect server object
fastmcp inspect server.py


------------------------------------------------------------

