#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接问题诊断工具
"""

import requests
import subprocess
import sys
import time
from pathlib import Path

def check_processes():
    """检查运行的进程"""
    print("=" * 60)
    print("检查运行的进程")
    print("=" * 60)
    
    try:
        # 检查Python进程
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq python.exe"],
            capture_output=True, text=True
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            python_processes = [line for line in lines if 'python.exe' in line.lower()]
            
            print(f"发现 {len(python_processes)} 个Python进程:")
            for i, process in enumerate(python_processes, 1):
                print(f"  {i}. {process.strip()}")
        else:
            print("无法获取进程列表")
            
    except Exception as e:
        print(f"检查进程失败: {e}")

def check_ports():
    """检查端口占用"""
    print("\n" + "=" * 60)
    print("检查端口占用")
    print("=" * 60)
    
    ports = [8080, 8081, 8082, 9000]
    
    for port in ports:
        try:
            result = subprocess.run(
                ["netstat", "-ano", "|", "findstr", f":{port}"],
                shell=True, capture_output=True, text=True
            )
            
            if result.stdout.strip():
                print(f"端口 {port}: 被占用")
                print(f"  详情: {result.stdout.strip()}")
            else:
                print(f"端口 {port}: 空闲")
                
        except Exception as e:
            print(f"检查端口 {port} 失败: {e}")

def test_http_services():
    """测试HTTP服务"""
    print("\n" + "=" * 60)
    print("测试HTTP服务")
    print("=" * 60)
    
    services = [
        ("HTTP桥接器", "http://127.0.0.1:8080"),
        ("Web服务器", "http://127.0.0.1:8081"),
        ("备用桥接器", "http://127.0.0.1:8082")
    ]
    
    for name, url in services:
        try:
            print(f"\n测试 {name} ({url})...")
            response = requests.get(url, timeout=5)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  响应: {data.get('message', '无消息')}")
                except:
                    print(f"  响应: {response.text[:100]}...")
                print(f"  ✓ {name} 运行正常")
            else:
                print(f"  ⚠ {name} 响应异常")
                
        except requests.exceptions.ConnectionError:
            print(f"  ✗ {name} 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"  ✗ {name} 连接超时")
        except Exception as e:
            print(f"  ✗ {name} 测试失败: {e}")

def test_api_endpoints():
    """测试API端点"""
    print("\n" + "=" * 60)
    print("测试API端点")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8080"
    
    endpoints = [
        ("健康检查", "/health", "GET"),
        ("数据库信息", "/api/database-info", "GET"),
        ("根路径", "/", "GET")
    ]
    
    for name, endpoint, method in endpoints:
        try:
            print(f"\n测试 {name} ({method} {endpoint})...")
            
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{base_url}{endpoint}", timeout=5)
            
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  响应数据: {str(data)[:100]}...")
                except:
                    print(f"  响应文本: {response.text[:100]}...")
                print(f"  ✓ {name} 正常")
            else:
                print(f"  ⚠ {name} 状态异常")
                print(f"  错误: {response.text[:100]}...")
                
        except Exception as e:
            print(f"  ✗ {name} 测试失败: {e}")

def check_files():
    """检查关键文件"""
    print("\n" + "=" * 60)
    print("检查关键文件")
    print("=" * 60)
    
    files = [
        "simple_bridge_test.py",
        "web_server.py",
        "web_client/app.js",
        "web_client/index.html"
    ]
    
    for file_path in files:
        path = Path(file_path)
        if path.exists():
            print(f"✓ {file_path} 存在 ({path.stat().st_size} 字节)")
        else:
            print(f"✗ {file_path} 不存在")

def main():
    """主函数"""
    print("MySQL数据分析系统 - 连接问题诊断工具")
    print("=" * 60)
    
    check_files()
    check_processes()
    check_ports()
    test_http_services()
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)
    
    print("\n建议:")
    print("1. 如果HTTP桥接器未运行，执行: python simple_bridge_test.py")
    print("2. 如果Web服务器未运行，执行: python web_server.py 8081")
    print("3. 如果端口被占用，使用不同端口或停止占用进程")
    print("4. 检查浏览器控制台是否有详细错误信息")

if __name__ == "__main__":
    main()
