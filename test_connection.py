#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接测试脚本 - 诊断MCP服务器连接问题
"""

import requests
import json
import time

def test_mcp_server():
    """测试MCP服务器连接"""
    print("测试MCP服务器连接...")
    
    # 测试基本HTTP连接
    try:
        response = requests.get('http://127.0.0.1:9001/', timeout=5)
        print(f"✅ HTTP连接成功: {response.status_code}")
    except Exception as e:
        print(f"❌ HTTP连接失败: {e}")
        return False

    # 测试MCP协议初始化
    try:
        mcp_url = 'http://127.0.0.1:9001/mcp/'
        init_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "Test Client",
                    "version": "1.0.0"
                }
            }
        }
        
        response = requests.post(
            mcp_url,
            json=init_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"MCP初始化响应状态: {response.status_code}")
        print(f"MCP初始化响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if 'error' not in data:
                print("✅ MCP初始化成功")
                return True
            else:
                print(f"❌ MCP初始化错误: {data['error']}")
                return False
        else:
            print(f"❌ MCP初始化失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ MCP协议测试失败: {e}")
        return False

def test_tool_call():
    """测试工具调用"""
    print("\n测试工具调用...")
    
    try:
        mcp_url = 'http://127.0.0.1:9001/mcp/'
        tool_payload = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "hello",
                "arguments": {}
            }
        }
        
        response = requests.post(
            mcp_url,
            json=tool_payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"工具调用响应状态: {response.status_code}")
        print(f"工具调用响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if 'error' not in data:
                print("✅ 工具调用成功")
                return True
            else:
                print(f"❌ 工具调用错误: {data['error']}")
                return False
        else:
            print(f"❌ 工具调用失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 工具调用测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("    MCP服务器连接诊断工具")
    print("=" * 60)
    
    # 检查MCP服务器是否运行
    print("检查MCP服务器状态...")
    import subprocess
    try:
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, shell=True)
        if ':9001' in result.stdout:
            print("✅ 端口9001正在监听")
        else:
            print("❌ 端口9001未在监听")
            print("请先启动MCP服务器: python minimal_mcp_test.py")
            return
    except Exception as e:
        print(f"无法检查端口状态: {e}")
    
    # 等待一下确保服务器完全启动
    print("等待服务器稳定...")
    time.sleep(2)
    
    # 测试连接
    if test_mcp_server():
        test_tool_call()
    
    print("\n" + "=" * 60)
    print("诊断完成")

if __name__ == "__main__":
    main()
