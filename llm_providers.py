#!/usr/bin/env python3
"""
LLM提供者抽象层
支持OpenAI、本地LLM等多种提供者，便于切换
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import aiohttp
import time

logger = logging.getLogger(__name__)

class LLMProvider(ABC):
    """LLM提供者抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = self.__class__.__name__
        
    @abstractmethod
    async def analyze(self, prompt: str, **kwargs) -> str:
        """分析文本，返回结果"""
        pass
    
    @abstractmethod
    async def is_available(self) -> bool:
        """检查提供者是否可用"""
        pass

class OpenAIProvider(LLMProvider):
    """OpenAI提供者（当前测试用）"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'gpt-4o-mini')
        self.base_url = config.get('base_url', 'https://api.openai.com/v1')
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        
        # 导入OpenAI客户端
        try:
            from openai import AsyncOpenAI
            self.client = AsyncOpenAI(api_key=self.api_key)
        except ImportError:
            logger.error("OpenAI库未安装，请运行: pip install openai")
            self.client = None
    
    async def analyze(self, prompt: str, **kwargs) -> str:
        """使用OpenAI分析"""
        if not self.client:
            raise Exception("OpenAI客户端未初始化")
        
        temperature = kwargs.get('temperature', self.config.get('temperature', 0.3))
        max_tokens = kwargs.get('max_tokens', self.config.get('max_tokens', 1000))
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的数据分析专家，擅长MySQL数据库分析、异常检测、趋势分析和业务洞察。请用简洁专业的中文回答，重点关注业务价值和可操作性。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise Exception(f"OpenAI分析失败: {str(e)}")
    
    async def is_available(self) -> bool:
        """检查OpenAI是否可用"""
        if not self.client:
            return False
        
        try:
            # 简单的测试调用
            await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            return True
        except Exception as e:
            logger.warning(f"OpenAI不可用: {e}")
            return False

class LocalLLMProvider(LLMProvider):
    """本地LLM提供者（将来使用）"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config.get('base_url', 'http://127.0.0.1:11434')
        self.model = config.get('model', 'llama3.1:8b')
        self.timeout = config.get('timeout', 60)
        self.api_type = config.get('api_type', 'ollama')
        
    async def analyze(self, prompt: str, **kwargs) -> str:
        """使用本地LLM分析"""
        temperature = kwargs.get('temperature', self.config.get('temperature', 0.3))
        max_tokens = kwargs.get('max_tokens', self.config.get('max_tokens', 1000))
        
        if self.api_type == 'ollama':
            return await self._call_ollama(prompt, temperature, max_tokens)
        else:
            return await self._call_openai_compatible(prompt, temperature, max_tokens)
    
    async def _call_ollama(self, prompt: str, temperature: float, max_tokens: int) -> str:
        """调用Ollama API"""
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": self.model,
            "prompt": f"你是一位专业的数据分析专家。{prompt}",
            "stream": False,
            "options": {
                "temperature": temperature,
                "num_predict": max_tokens
            }
        }
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('response', '').strip()
                    else:
                        raise Exception(f"Ollama API错误: {response.status}")
        except Exception as e:
            logger.error(f"Ollama调用失败: {e}")
            raise Exception(f"本地LLM分析失败: {str(e)}")
    
    async def _call_openai_compatible(self, prompt: str, temperature: float, max_tokens: int) -> str:
        """调用OpenAI兼容的API（如LM Studio）"""
        url = f"{self.base_url}/chat/completions"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.get('api_key', 'local')}"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一位专业的数据分析专家，擅长MySQL数据库分析、异常检测、趋势分析和业务洞察。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result['choices'][0]['message']['content'].strip()
                    else:
                        raise Exception(f"本地LLM API错误: {response.status}")
        except Exception as e:
            logger.error(f"本地LLM调用失败: {e}")
            raise Exception(f"本地LLM分析失败: {str(e)}")
    
    async def is_available(self) -> bool:
        """检查本地LLM是否可用"""
        try:
            if self.api_type == 'ollama':
                url = f"{self.base_url}/api/tags"
            else:
                url = f"{self.base_url}/models"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(url) as response:
                    return response.status == 200
        except Exception as e:
            logger.warning(f"本地LLM不可用: {e}")
            return False

class LLMManager:
    """LLM管理器，负责选择和切换提供者"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.providers = {}
        self.current_provider = None
        self.fallback_enabled = config.get('fallback_enabled', True)
        
        # 初始化提供者
        self._init_providers()
    
    def _init_providers(self):
        """初始化所有提供者"""
        providers_config = self.config.get('providers', {})
        
        # OpenAI提供者
        if providers_config.get('openai', {}).get('enabled', False):
            self.providers['openai'] = OpenAIProvider(providers_config['openai'])
        
        # 本地LLM提供者
        if providers_config.get('local', {}).get('enabled', False):
            self.providers['local'] = LocalLLMProvider(providers_config['local'])
        
        # LM Studio提供者
        if providers_config.get('lm_studio', {}).get('enabled', False):
            self.providers['lm_studio'] = LocalLLMProvider(providers_config['lm_studio'])
        
        # 设置当前提供者
        preferred_provider = self.config.get('provider', 'openai')
        if preferred_provider in self.providers:
            self.current_provider = preferred_provider
        else:
            # 选择第一个可用的提供者
            if self.providers:
                self.current_provider = list(self.providers.keys())[0]
    
    async def analyze(self, prompt: str, **kwargs) -> str:
        """智能分析：尝试当前提供者，失败时回退"""
        
        # 尝试当前提供者
        if self.current_provider and self.current_provider in self.providers:
            try:
                provider = self.providers[self.current_provider]
                logger.info(f"使用 {self.current_provider} 进行分析")
                return await provider.analyze(prompt, **kwargs)
            except Exception as e:
                logger.warning(f"{self.current_provider} 分析失败: {e}")
                
                # 如果启用了回退机制
                if self.fallback_enabled:
                    return await self._try_fallback(prompt, **kwargs)
                else:
                    raise e
        
        # 没有可用的提供者
        raise Exception("没有可用的LLM提供者")
    
    async def _try_fallback(self, prompt: str, **kwargs) -> str:
        """尝试回退到其他提供者"""
        for name, provider in self.providers.items():
            if name != self.current_provider:
                try:
                    logger.info(f"回退到 {name} 进行分析")
                    return await provider.analyze(prompt, **kwargs)
                except Exception as e:
                    logger.warning(f"{name} 回退失败: {e}")
                    continue
        
        raise Exception("所有LLM提供者都不可用")
    
    async def check_providers(self) -> Dict[str, bool]:
        """检查所有提供者的可用性"""
        status = {}
        for name, provider in self.providers.items():
            status[name] = await provider.is_available()
        return status
    
    def switch_provider(self, provider_name: str) -> bool:
        """切换到指定的提供者"""
        if provider_name in self.providers:
            self.current_provider = provider_name
            logger.info(f"切换到LLM提供者: {provider_name}")
            return True
        else:
            logger.error(f"未知的LLM提供者: {provider_name}")
            return False
    
    def get_current_provider(self) -> Optional[str]:
        """获取当前提供者名称"""
        return self.current_provider
    
    def list_providers(self) -> list:
        """列出所有可用的提供者"""
        return list(self.providers.keys())

# 全局LLM管理器实例
llm_manager = None

def init_llm_manager(config: Dict[str, Any]):
    """初始化全局LLM管理器"""
    global llm_manager
    llm_manager = LLMManager(config)
    return llm_manager

def get_llm_manager() -> LLMManager:
    """获取全局LLM管理器"""
    global llm_manager
    if llm_manager is None:
        raise Exception("LLM管理器未初始化，请先调用 init_llm_manager()")
    return llm_manager
