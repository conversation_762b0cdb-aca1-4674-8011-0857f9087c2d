#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的HTTP桥接器测试版本
不依赖MCP，直接提供模拟数据
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict
import base64
from io import BytesIO

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 图表生成依赖
try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("警告: PIL未安装，将使用占位符图像")

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Pydantic模型
class ToolCallRequest(BaseModel):
    tool_name: str
    arguments: Dict[str, Any]

# 创建FastAPI应用
app = FastAPI(
    title="简化HTTP桥接器测试",
    description="用于测试的简化版本",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 图表生成函数
def generate_mock_chart_image(chart_type: str) -> str:
    """生成模拟图表图像并返回base64编码"""
    if not PIL_AVAILABLE:
        # 如果PIL不可用，返回一个简单的占位符
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

    try:
        # 创建图像
        width, height = 400, 300
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)

        # 尝试加载字体
        try:
            font_title = ImageFont.truetype("arial.ttf", 16)
            font_text = ImageFont.truetype("arial.ttf", 12)
        except:
            font_title = ImageFont.load_default()
            font_text = ImageFont.load_default()

        if chart_type == "bar":
            # 柱状图
            draw.text((200, 20), "柱状图示例", fill='black', font=font_title, anchor="mt")

            # 绘制柱子
            bars = [
                (50, 200, 110, 250, '#FF6B6B', 'A', 10),
                (130, 150, 190, 250, '#4ECDC4', 'B', 20),
                (210, 175, 270, 250, '#45B7D1', 'C', 15),
                (290, 125, 350, 250, '#96CEB4', 'D', 25)
            ]

            for x1, y1, x2, y2, color, label, value in bars:
                # 转换颜色
                if color == '#FF6B6B':
                    fill_color = (255, 107, 107)
                elif color == '#4ECDC4':
                    fill_color = (78, 205, 196)
                elif color == '#45B7D1':
                    fill_color = (69, 183, 209)
                else:
                    fill_color = (150, 206, 180)

                draw.rectangle([x1, y1, x2, y2], fill=fill_color)
                draw.text(((x1+x2)//2, 265), label, fill='black', font=font_text, anchor="mt")
                draw.text(((x1+x2)//2, y1-10), str(value), fill='black', font=font_text, anchor="mb")

        elif chart_type == "pie":
            # 饼图
            draw.text((200, 20), "饼图示例", fill='black', font=font_title, anchor="mt")

            # 绘制饼图（简化版本，使用椭圆）
            center_x, center_y = 200, 150
            radius = 60

            # 绘制几个扇形（简化为圆形）
            colors = [(255, 107, 107), (78, 205, 196), (69, 183, 209), (150, 206, 180), (254, 202, 87)]
            labels = ['PG (35%)', 'PG-13 (25%)', 'R (20%)', 'G (15%)', 'NC-17 (5%)']

            for i, (color, label) in enumerate(zip(colors, labels)):
                angle = i * 72  # 简化为等分
                x = center_x + radius * 0.8
                y = center_y + i * 15 - 30
                draw.ellipse([center_x-radius, center_y-radius, center_x+radius, center_y+radius],
                           fill=color, outline='white', width=2)
                draw.text((x + 20, y), label, fill='black', font=font_text)

        elif chart_type == "line":
            # 趋势图
            draw.text((200, 20), "趋势图示例", fill='black', font=font_title, anchor="mt")

            # 绘制线图
            points = [(50, 200), (100, 180), (150, 190), (200, 160), (250, 170), (300, 140)]

            # 绘制线条
            for i in range(len(points) - 1):
                draw.line([points[i], points[i+1]], fill=(69, 183, 209), width=3)

            # 绘制点
            for i, (x, y) in enumerate(points):
                draw.ellipse([x-4, y-4, x+4, y+4], fill=(69, 183, 209))
                draw.text((x, 265), f"0{i+1}", fill='black', font=font_text, anchor="mt")

        elif chart_type == "scatter":
            # 散点图
            draw.text((200, 20), "散点图示例", fill='black', font=font_title, anchor="mt")

            # 绘制坐标轴
            draw.line([(50, 250), (350, 250)], fill='black', width=2)  # X轴
            draw.line([(50, 50), (50, 250)], fill='black', width=2)   # Y轴

            # 绘制散点
            scatter_points = [
                (80, 200), (120, 180), (160, 220), (200, 160),
                (240, 190), (280, 140), (320, 170)
            ]

            colors = [(255, 99, 132), (54, 162, 235), (255, 205, 86),
                     (75, 192, 192), (153, 102, 255), (255, 159, 64)]

            for i, (x, y) in enumerate(scatter_points):
                color = colors[i % len(colors)]
                draw.ellipse([x-6, y-6, x+6, y+6], fill=color, outline='white', width=2)

            # 坐标轴标签
            draw.text((200, 270), "X轴", fill='black', font=font_text, anchor="mt")
            draw.text((25, 150), "Y轴", fill='black', font=font_text, anchor="mm")

        elif chart_type == "heatmap":
            # 热力图
            draw.text((200, 20), "热力图示例", fill='black', font=font_title, anchor="mt")

            # 绘制热力图网格
            cell_size = 35
            start_x, start_y = 100, 80

            # 热力数据 (值越大颜色越深)
            heatmap_data = [
                [1, 3, 5, 2],
                [4, 8, 6, 3],
                [2, 6, 9, 7],
                [3, 4, 7, 5]
            ]

            max_val = max(max(row) for row in heatmap_data)

            for i, row in enumerate(heatmap_data):
                for j, val in enumerate(row):
                    x1 = start_x + j * cell_size
                    y1 = start_y + i * cell_size
                    x2 = x1 + cell_size
                    y2 = y1 + cell_size

                    # 根据数值计算颜色强度
                    intensity = int(255 * (1 - val / max_val))
                    color = (255, intensity, intensity)  # 红色渐变

                    draw.rectangle([x1, y1, x2, y2], fill=color, outline='white', width=1)
                    # 在格子中心显示数值
                    draw.text(((x1+x2)//2, (y1+y2)//2), str(val), fill='black', font=font_text, anchor="mm")

        else:
            # 默认图表
            draw.text((200, 150), f"{chart_type}图表", fill='black', font=font_title, anchor="mm")
            draw.text((200, 180), "示例图表", fill='black', font=font_text, anchor="mt")

        # 保存为base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        buffer.close()

        return image_base64

    except Exception as e:
        logger.error(f"生成图表失败: {e}")
        # 返回占位符图像
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

# 请求日志中间件
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = asyncio.get_event_loop().time()
    logger.info(f"收到请求: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        process_time = asyncio.get_event_loop().time() - start_time
        logger.info(f"请求完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.3f}s")
        return response
    except Exception as e:
        process_time = asyncio.get_event_loop().time() - start_time
        logger.error(f"请求失败: {request.method} {request.url} - 错误: {e} - 耗时: {process_time:.3f}s")
        raise

@app.get("/")
async def root():
    """根路径"""
    logger.info("访问根路径")
    return {
        "message": "简化HTTP桥接器测试",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    logger.info("健康检查")
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "message": "服务正常运行"
    }

# 显式处理OPTIONS请求
@app.options("/{full_path:path}")
async def options_handler(full_path: str):
    """处理所有OPTIONS请求"""
    return {"message": "OK"}

@app.get("/api/database-info")
async def get_database_info():
    """获取数据库信息（模拟）"""
    logger.info("🔄 [DEBUG] 收到数据库信息请求")

    # 模拟数据
    mock_data = {
        "success": True,
        "database_info": {
            "database": "test",
            "host": "localhost",
            "port": 3306,
            "tables": ["users", "orders", "products", "payments"],
            "table_count": 4,
            "connection_status": "connected"
        }
    }

    logger.info(f"🔄 [DEBUG] 返回数据库信息: {mock_data}")
    return mock_data

@app.post("/api/statistics")
async def run_statistics(request: dict):
    """运行统计分析（模拟）"""
    logger.info(f"统计分析请求: {request}")
    
    # 模拟统计数据
    mock_stats = {
        "success": True,
        "data": {
            "count": 1000,
            "sum": 50000.0,
            "average": 50.0,
            "minimum": 10.0,
            "maximum": 100.0,
            "std_dev": 15.5,
            "variance": 240.25
        },
        "table": request.get("table", "unknown"),
        "column": request.get("column", "unknown")
    }
    
    return mock_stats

@app.post("/api/anomaly-detection")
async def detect_anomalies(request: dict):
    """异常检测（模拟）"""
    logger.info(f"异常检测请求: {request}")
    
    # 模拟异常检测结果
    mock_anomalies = {
        "success": True,
        "data": {
            "anomalies": [
                {
                    "id": 123,
                    "value": 150.0,
                    "z_score": 3.2,
                    "reason": "Z-score (3.20) 超过阈值 2.0"
                },
                {
                    "id": 456,
                    "value": 5.0,
                    "z_score": -2.8,
                    "reason": "Z-score (-2.80) 超过阈值 2.0"
                }
            ],
            "total_count": 1000,
            "anomaly_count": 2,
            "anomaly_rate": 0.2
        },
        "method": request.get("method", "zscore"),
        "threshold": request.get("threshold", 2.0)
    }
    
    return mock_anomalies

@app.post("/api/generate-chart")
async def generate_chart(request: dict):
    """生成图表（模拟）"""
    logger.info(f"图表生成请求: {request}")

    chart_type = request.get("chart_type", "bar")

    # 根据图表类型生成不同的模拟数据
    if chart_type == "pie":
        mock_data = [
            {"name": "PG", "value": 35},
            {"name": "PG-13", "value": 25},
            {"name": "R", "value": 20},
            {"name": "G", "value": 15},
            {"name": "NC-17", "value": 5}
        ]
    elif chart_type == "line":
        mock_data = [
            {"name": "01", "value": 100},
            {"name": "02", "value": 120},
            {"name": "03", "value": 110},
            {"name": "04", "value": 140},
            {"name": "05", "value": 130},
            {"name": "06", "value": 160}
        ]
    elif chart_type == "scatter":
        mock_data = [
            {"x": 2.99, "y": 19.99, "value": [2.99, 19.99]},
            {"x": 4.99, "y": 29.99, "value": [4.99, 29.99]},
            {"x": 0.99, "y": 9.99, "value": [0.99, 9.99]},
            {"x": 2.99, "y": 24.99, "value": [2.99, 24.99]},
            {"x": 4.99, "y": 19.99, "value": [4.99, 19.99]},
            {"x": 0.99, "y": 14.99, "value": [0.99, 14.99]}
        ]
    elif chart_type == "heatmap":
        mock_data = [
            {"x": "0", "y": "1", "value": 5},
            {"x": "1", "y": "1", "value": 8},
            {"x": "2", "y": "1", "value": 12},
            {"x": "0", "y": "2", "value": 3},
            {"x": "1", "y": "2", "value": 15},
            {"x": "2", "y": "2", "value": 7}
        ]
    else:  # bar chart
        mock_data = [
            {"name": "G", "value": 10},
            {"name": "PG", "value": 20},
            {"name": "PG-13", "value": 15},
            {"name": "R", "value": 25}
        ]

    # 模拟图表数据
    mock_chart = {
        "success": True,
        "chart_type": chart_type,
        "chart_data": {
            "chart_type": chart_type,
            "title": request.get("title", f"{chart_type}图表"),
            "data": mock_data,
            "image_base64": generate_mock_chart_image(chart_type)
        },
        "title": request.get("title", f"{chart_type}图表"),
        "has_image": True
    }

    return mock_chart

@app.post("/api/execute-sql")
async def execute_sql(request: dict):
    """执行SQL查询（模拟）"""
    logger.info(f"SQL执行请求: {request}")
    
    sql = request.get("query", "")
    
    # 模拟SQL执行结果
    mock_result = {
        "success": True,
        "data": {
            "rows": [
                {"id": 1, "name": "张三", "age": 25},
                {"id": 2, "name": "李四", "age": 30},
                {"id": 3, "name": "王五", "age": 28}
            ],
            "columns": ["id", "name", "age"],
            "row_count": 3
        },
        "sql": sql
    }
    
    return mock_result

async def analyze_data_trend(args):
    """分析数据趋势（模拟）"""
    table = args.get("table", "payment")
    time_column = args.get("time_column", "created_at")
    value_column = args.get("value_column", "amount")
    period = args.get("period", "day")

    # 模拟趋势分析数据
    import random
    from datetime import datetime, timedelta

    # 生成模拟的趋势数据
    trend_data = []
    base_date = datetime.now() - timedelta(days=30)

    for i in range(30):
        date = base_date + timedelta(days=i)
        value = 1000 + random.randint(-200, 300) + (i * 10)  # 模拟增长趋势
        trend_data.append({
            "period": date.strftime("%Y-%m-%d"),
            "avg_value": round(value, 2),
            "count": random.randint(50, 150)
        })

    # 计算趋势指标
    values = [item["avg_value"] for item in trend_data]
    trend_direction = "上升" if values[-1] > values[0] else "下降"
    growth_rate = ((values[-1] - values[0]) / values[0]) * 100

    return {
        "success": True,
        "data": trend_data,
        "analysis": {
            "trend_direction": trend_direction,
            "growth_rate": round(growth_rate, 2),
            "avg_value": round(sum(values) / len(values), 2),
            "max_value": max(values),
            "min_value": min(values),
            "volatility": round(max(values) - min(values), 2)
        },
        "forecast": [
            {"period": "2025-01-29", "predicted_value": values[-1] + 50},
            {"period": "2025-01-30", "predicted_value": values[-1] + 75},
            {"period": "2025-01-31", "predicted_value": values[-1] + 100}
        ]
    }

@app.post("/mcp/call-tool")
async def call_tool(request: ToolCallRequest):
    """MCP工具调用（模拟）"""
    logger.info(f"MCP工具调用: {request.tool_name}, 参数: {request.arguments}")
    
    # 根据工具名称返回模拟数据
    if request.tool_name == "get_database_info":
        result = await get_database_info()
    elif request.tool_name == "analyze_statistics":
        result = await run_statistics(request.arguments)
    elif request.tool_name == "detect_anomalies":
        result = await detect_anomalies(request.arguments)
    elif request.tool_name == "generate_chart":
        result = await generate_chart(request.arguments)
    elif request.tool_name == "execute_sql":
        result = await execute_sql(request.arguments)
    elif request.tool_name == "analyze_data_trend":
        result = await analyze_data_trend(request.arguments)
    elif request.tool_name == "get_database_statistics":
        result = await run_statistics(request.arguments)
    elif request.tool_name in ["generate_bar_chart", "generate_pie_chart", "generate_trend_chart", "generate_scatter_chart", "generate_heatmap_chart"]:
        result = await generate_chart(request.arguments)
    else:
        result = {
            "success": False,
            "error": f"未知的工具: {request.tool_name}"
        }
    
    return {"result": result}

def main():
    """主函数"""
    print("=" * 60)
    print("简化HTTP桥接器测试")
    print("=" * 60)
    print("启动HTTP服务器...")
    print("监听地址: http://127.0.0.1:8083")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)

    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8083,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")

if __name__ == "__main__":
    main()
