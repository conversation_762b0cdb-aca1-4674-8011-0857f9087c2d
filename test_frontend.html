<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>前端API测试</h1>
    
    <div class="test-section">
        <h2>1. 数据库信息测试</h2>
        <button onclick="testDatabaseInfo()">测试数据库信息</button>
        <div id="dbResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 统计分析测试</h2>
        <button onclick="testStatistics()">测试统计分析</button>
        <div id="statsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 异常检测测试</h2>
        <button onclick="testAnomalyDetection()">测试异常检测</button>
        <div id="anomalyResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 图表生成测试</h2>
        <button onclick="testChartGeneration()">测试图表生成</button>
        <div id="chartResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 完整流程测试</h2>
        <button onclick="testFullWorkflow()">测试完整流程</button>
        <div id="workflowResult" class="result"></div>
    </div>

    <script>
        const serverUrl = 'http://127.0.0.1:8080';
        
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = message;
        }
        
        async function testDatabaseInfo() {
            try {
                showResult('dbResult', '正在测试数据库信息...');
                
                const response = await fetch(`${serverUrl}/api/database-info`);
                const data = await response.json();
                
                console.log('数据库信息响应:', data);
                
                if (data.success && data.database_info) {
                    const info = data.database_info;
                    showResult('dbResult', `
                        <strong>测试成功!</strong><br>
                        数据库: ${info.database}<br>
                        表数量: ${info.table_count}<br>
                        连接状态: ${info.connection_status}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, true);
                } else {
                    showResult('dbResult', `测试失败: 数据格式不正确<br><pre>${JSON.stringify(data, null, 2)}</pre>`, false);
                }
            } catch (error) {
                showResult('dbResult', `测试失败: ${error.message}`, false);
            }
        }
        
        async function testStatistics() {
            try {
                showResult('statsResult', '正在测试统计分析...');
                
                const response = await fetch(`${serverUrl}/api/statistics`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        table: 'payment',
                        column: 'amount'
                    })
                });
                
                const data = await response.json();
                console.log('统计分析响应:', data);
                
                if (data.success && data.data) {
                    const stats = data.data;
                    showResult('statsResult', `
                        <strong>测试成功!</strong><br>
                        记录数: ${stats.count}<br>
                        平均值: ${stats.average}<br>
                        最小值: ${stats.minimum}<br>
                        最大值: ${stats.maximum}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, true);
                } else {
                    showResult('statsResult', `测试失败: 数据格式不正确<br><pre>${JSON.stringify(data, null, 2)}</pre>`, false);
                }
            } catch (error) {
                showResult('statsResult', `测试失败: ${error.message}`, false);
            }
        }
        
        async function testAnomalyDetection() {
            try {
                showResult('anomalyResult', '正在测试异常检测...');
                
                const response = await fetch(`${serverUrl}/api/anomaly-detection`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        table: 'payment',
                        column: 'amount',
                        method: 'zscore',
                        threshold: 2.0
                    })
                });
                
                const data = await response.json();
                console.log('异常检测响应:', data);
                
                if (data.success && data.data) {
                    const anomalyData = data.data;
                    showResult('anomalyResult', `
                        <strong>测试成功!</strong><br>
                        总记录数: ${anomalyData.total_count}<br>
                        异常数量: ${anomalyData.anomaly_count}<br>
                        异常率: ${(anomalyData.anomaly_rate * 100).toFixed(2)}%<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, true);
                } else {
                    showResult('anomalyResult', `测试失败: 数据格式不正确<br><pre>${JSON.stringify(data, null, 2)}</pre>`, false);
                }
            } catch (error) {
                showResult('anomalyResult', `测试失败: ${error.message}`, false);
            }
        }
        
        async function testChartGeneration() {
            try {
                showResult('chartResult', '正在测试图表生成...');
                
                const response = await fetch(`${serverUrl}/api/generate-chart`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        chart_type: 'bar',
                        table: 'film',
                        x_column: 'rating',
                        y_column: 'rental_rate',
                        title: '测试图表'
                    })
                });
                
                const data = await response.json();
                console.log('图表生成响应:', data);
                
                if (data.success && data.chart_data) {
                    const chartData = data.chart_data;
                    showResult('chartResult', `
                        <strong>测试成功!</strong><br>
                        图表类型: ${chartData.chart_type}<br>
                        标题: ${chartData.title}<br>
                        数据点数量: ${chartData.data.length}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, true);
                } else {
                    showResult('chartResult', `测试失败: 数据格式不正确<br><pre>${JSON.stringify(data, null, 2)}</pre>`, false);
                }
            } catch (error) {
                showResult('chartResult', `测试失败: ${error.message}`, false);
            }
        }
        
        async function testFullWorkflow() {
            showResult('workflowResult', '正在测试完整流程...');
            
            let results = [];
            
            // 测试所有API
            const tests = [
                { name: '数据库信息', func: testDatabaseInfo },
                { name: '统计分析', func: testStatistics },
                { name: '异常检测', func: testAnomalyDetection },
                { name: '图表生成', func: testChartGeneration }
            ];
            
            for (const test of tests) {
                try {
                    await test.func();
                    results.push(`✓ ${test.name}: 成功`);
                } catch (error) {
                    results.push(`✗ ${test.name}: 失败 - ${error.message}`);
                }
            }
            
            showResult('workflowResult', `
                <strong>完整流程测试结果:</strong><br>
                ${results.join('<br>')}
            `, true);
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testDatabaseInfo();
        };
    </script>
</body>
</html>
