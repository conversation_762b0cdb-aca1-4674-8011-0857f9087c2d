// 工业数据分析系统 - Web客户端
class IndustrialAnalysisClient {
    constructor() {
        this.mcpServerUrl = 'http://127.0.0.1:9002/mcp';  // MCP服务器地址（使用9002端口）
        this.isConnected = false;
        this.sessionId = null;  // 存储MCP会话ID
        this.currentTab = 'dashboard';
        this.realTimeMonitoring = false;
        this.monitoringInterval = null;
        this.lastAnomalyResults = null;
        this.checkCount = 0;
        this.nextCheckTime = null;
        this.speechRecognition = null;
        this.isListening = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeDefaultTimes();
        this.setupTabNavigation();
        this.setupChartTypeSelector();

        // 显示初始化信息
        this.updateConnectionLog('🚀 工业数据分析系统启动');
        this.updateConnectionLog('📍 MCP服务器地址: ' + this.mcpServerUrl);
        this.updateConnectionLog('⏳ 等待用户连接...');

        this.checkConnection();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 连接按钮
        document.getElementById('connectBtn').addEventListener('click', () => {
            this.checkConnection();
        });

        // 仪表板功能
        document.getElementById('refreshDbInfo').addEventListener('click', () => {
            this.loadDatabaseInfo();
        });

        document.getElementById('generateQuickChart').addEventListener('click', () => {
            this.generateQuickChart();
        });

        // 统计分析
        document.getElementById('runStatistics').addEventListener('click', () => {
            this.runStatistics();
        });

        // 统计分析相关事件
        document.getElementById('exportStatistics').addEventListener('click', () => {
            this.exportStatistics();
        });

        document.getElementById('clearStatistics').addEventListener('click', () => {
            this.clearStatistics();
        });

        document.getElementById('statsVoiceControl').addEventListener('click', () => {
            this.handleStatsVoiceControl();
        });

        document.getElementById('statsToggleChart').addEventListener('click', () => {
            this.toggleStatsChart();
        });

        document.getElementById('statsRefreshNow').addEventListener('click', () => {
            this.refreshStatsNow();
        });

        // 实时监控切换
        document.getElementById('statsRealTime').addEventListener('change', (e) => {
            this.toggleStatsRealTime(e.target.checked);
        });

        // 异常检测
        document.getElementById('runAnomalyDetection').addEventListener('click', () => {
            this.runAnomalyDetection();
        });

        // 实时监控切换
        document.getElementById('toggleRealTimeMonitoring').addEventListener('click', () => {
            this.toggleRealTimeMonitoring();
        });

        // 导出异常结果
        document.getElementById('exportAnomalyResults').addEventListener('click', () => {
            this.exportAnomalyResults();
        });

        // 清空结果
        document.getElementById('clearResults').addEventListener('click', () => {
            this.clearResults();
        });

        // 语音控制
        document.getElementById('startVoiceControl').addEventListener('click', () => {
            this.toggleVoiceControl();
        });

        // 检测方法变化时显示/隐藏业务规则
        document.getElementById('anomalyMethod').addEventListener('change', (e) => {
            const businessRuleGroup = document.getElementById('businessRuleGroup');
            if (e.target.value === 'business_rule') {
                businessRuleGroup.style.display = 'block';
            } else {
                businessRuleGroup.style.display = 'none';
            }
        });

        // 实时监控开关变化
        document.getElementById('realTimeMonitoring').addEventListener('change', (e) => {
            const toggleBtn = document.getElementById('toggleRealTimeMonitoring');
            toggleBtn.disabled = !e.target.checked;
            if (!e.target.checked && this.realTimeMonitoring) {
                this.toggleRealTimeMonitoring(); // 关闭监控
            }
        });

        // 趋势分析
        document.getElementById('runTrendAnalysis').addEventListener('click', () => {
            this.runTrendAnalysis();
        });

        // 趋势分析相关事件
        document.getElementById('exportTrendData').addEventListener('click', () => {
            this.exportTrendData();
        });

        document.getElementById('clearTrendResult').addEventListener('click', () => {
            this.clearTrendResult();
        });

        document.getElementById('trendVoiceControl').addEventListener('click', () => {
            this.handleTrendVoiceControl();
        });

        document.getElementById('trendToggleView').addEventListener('click', () => {
            this.toggleTrendView();
        });

        document.getElementById('trendRefreshNow').addEventListener('click', () => {
            this.refreshTrendNow();
        });

        document.getElementById('trendFullscreen').addEventListener('click', () => {
            this.toggleTrendFullscreen();
        });

        // 实时趋势监控切换
        document.getElementById('trendRealTime').addEventListener('change', (e) => {
            this.toggleTrendRealTime(e.target.checked);
        });

        // 图表生成
        document.getElementById('generateBarChart').addEventListener('click', () => {
            this.generateBarChart();
        });

        document.getElementById('generatePieChart').addEventListener('click', () => {
            this.generatePieChart();
        });

        document.getElementById('generateLineChart').addEventListener('click', () => {
            this.generateLineChart();
        });

        document.getElementById('generateScatterChart').addEventListener('click', () => {
            this.generateScatterChart();
        });

        document.getElementById('generateHeatmapChart').addEventListener('click', () => {
            this.generateHeatmapChart();
        });

        // 企业级图表功能
        document.getElementById('chartTheme').addEventListener('change', (e) => {
            this.changeChartTheme(e.target.value);
        });

        document.getElementById('exportChart').addEventListener('click', () => {
            this.exportCurrentChart();
        });

        document.getElementById('fullscreenChart').addEventListener('click', () => {
            this.toggleFullscreenChart();
        });

        // SQL查询
        document.getElementById('executeSql').addEventListener('click', () => {
            this.executeSql();
        });

        document.getElementById('formatSql').addEventListener('click', () => {
            this.formatSql();
        });

        document.getElementById('clearSql').addEventListener('click', () => {
            this.clearSql();
        });

        // 语音功能
        document.getElementById('startVoice').addEventListener('click', () => {
            this.startVoiceInput();
        });

        document.getElementById('stopVoice').addEventListener('click', () => {
            this.stopVoiceInput();
        });

        // 图表库
        document.getElementById('refreshGallery').addEventListener('click', () => {
            this.loadGallery();
        });

        document.getElementById('clearGallery').addEventListener('click', () => {
            this.clearGallery();
        });

        // 提醒管理
        document.getElementById('createAlert').addEventListener('click', () => {
            this.createAlert();
        });
    }

    // 设置标签页导航
    setupTabNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = link.getAttribute('data-tab');
                this.switchTab(tabId);
            });
        });
    }

    // 设置图表类型选择器
    setupChartTypeSelector() {
        const chartTypeBtns = document.querySelectorAll('.chart-type-btn');
        chartTypeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const chartType = btn.getAttribute('data-type');
                this.switchChartType(chartType);
            });
        });
    }

    // 切换标签页
    switchTab(tabId) {
        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabId).classList.add('active');

        this.currentTab = tabId;

        // 加载对应数据
        this.loadTabData(tabId);
    }

    // 切换图表类型
    switchChartType(chartType) {
        // 更新按钮状态
        document.querySelectorAll('.chart-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-type="${chartType}"]`).classList.add('active');

        // 更新表单显示
        document.querySelectorAll('.chart-form-section').forEach(section => {
            section.classList.remove('active');
        });

        const formId = chartType === 'scatter' ? 'scatterChartForm' :
                      chartType === 'heatmap' ? 'heatmapChartForm' :
                      `${chartType}ChartForm`;

        const formElement = document.getElementById(formId);
        if (formElement) {
            formElement.classList.add('active');
        }
    }

    // 加载标签页数据
    loadTabData(tabId) {
        switch(tabId) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'statistics':
                // 统计分析页面 - 确保字体不被影响
                this.ensurePageFontIntegrity();
                break;
            case 'anomaly':
                // 异常检测页面 - 确保字体不被影响
                this.ensurePageFontIntegrity();
                break;
            case 'trends':
                // 趋势分析页面 - 确保字体不被影响
                this.ensurePageFontIntegrity();
                break;
            case 'charts':
                // 图表页面 - 确保字体不被ECharts影响
                this.ensurePageFontIntegrity();
                break;
            case 'gallery':
                this.loadGallery();
                break;
            case 'sql':
                // SQL页面不需要预加载
                break;
            case 'alerts':
                this.loadAlerts();
                break;
            case 'voice':
                // 语音页面不需要预加载
                break;
        }
    }

    // 确保页面字体完整性，防止ECharts影响
    ensurePageFontIntegrity() {
        const targetFont = 'Microsoft YaHei, Segoe UI, Tahoma, Geneva, Verdana, sans-serif';

        // 强制重新应用页面字体 - 更全面的选择器
        const selectors = [
            '.container', '.header', '.sidebar', '.main-content', '.nav-item',
            '.section-title', '.content-section', '.form-group', '.btn',
            '.stats-grid', '.stat-card', '.notification', '.tab-content',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'label',
            'input', 'select', 'textarea', 'button', '.nav-link',
            'div:not([id*="Chart"]):not([id*="chart"]):not(.echarts-container)',
            '.chart-info', '.data-insights', '.chart-controls'
        ];

        selectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.style.setProperty('font-family', targetFont, 'important');
                });
            } catch (e) {
                // 忽略无效选择器
            }
        });

        // 多次延迟执行，确保持久性
        [100, 300, 500].forEach(delay => {
            setTimeout(() => {
                selectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            element.style.setProperty('font-family', targetFont, 'important');
                        });
                    } catch (e) {
                        // 忽略无效选择器
                    }
                });
            }, delay);
        });

        console.log('🔤 页面字体保护已强化应用');
    }

    // 检查MCP服务器连接状态
    async checkConnection() {
        this.showLoading();
        console.log('🔄 开始MCP连接测试，服务器地址:', this.mcpServerUrl);

        // 在页面上显示连接状态
        this.updateConnectionLog('🔄 开始MCP连接测试...');
        this.updateConnectionLog('📍 目标服务器: ' + this.mcpServerUrl);
        this.updateConnectionLog('🌐 当前页面: ' + window.location.href);
        this.updateConnectionLog('🔧 浏览器: ' + navigator.userAgent);

        try {
            // 首先测试基本网络连接
            this.updateConnectionLog('🔄 测试基本网络连接...');
            try {
                const pingResponse = await fetch(this.mcpServerUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json, text/event-stream',
                        'Content-Type': 'application/json'
                    }
                });
                this.updateConnectionLog('📡 网络连接测试: ' + pingResponse.status + ' ' + pingResponse.statusText);
            } catch (pingError) {
                this.updateConnectionLog('❌ 网络连接测试失败: ' + pingError.message);
                throw new Error('无法连接到服务器，请检查服务器是否运行');
            }

            // 首先进行MCP初始化
            console.log('🔄 发送MCP初始化请求...');
            this.updateConnectionLog('🔄 发送MCP初始化请求...');
            await this.initializeMCPConnection();

            // 然后测试hello工具
            console.log('🔄 测试hello工具...');
            this.updateConnectionLog('🔄 测试hello工具...');
            const result = await this.callMCPTool('hello', {});

            console.log('✅ MCP hello响应:', result);
            this.updateConnectionLog('✅ MCP hello响应: ' + JSON.stringify(result, null, 2));

            if (result) {
                this.setConnectionStatus(true);
                this.showNotification('MCP服务器连接成功！', 'success');
                this.updateConnectionLog('✅ MCP服务器连接成功！');
                this.loadDashboard();
            } else {
                throw new Error('MCP服务器响应无效');
            }
        } catch (error) {
            console.error('❌ MCP连接错误详情:', error);
            this.setConnectionStatus(false);
            this.showNotification('无法连接到MCP服务器: ' + error.message, 'error');
            this.updateConnectionLog('❌ MCP连接失败: ' + error.message);
            this.updateConnectionLog('💡 请检查:');
            this.updateConnectionLog('  1. MCP服务器是否运行在 http://127.0.0.1:9002/mcp');
            this.updateConnectionLog('  2. 防火墙是否阻止连接');
            this.updateConnectionLog('  3. 浏览器控制台是否有CORS错误');
        }
        this.hideLoading();
    }

    // 更新连接日志显示
    updateConnectionLog(message) {
        console.log(message);

        // 在页面上显示连接日志
        let logContainer = document.getElementById('connectionLog');
        if (!logContainer) {
            // 如果日志容器不存在，创建一个
            const wrapper = document.createElement('div');
            wrapper.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                width: 420px;
                z-index: 10000;
            `;

            // 创建标题栏
            const header = document.createElement('div');
            header.style.cssText = `
                background: rgba(0,0,0,0.9);
                color: #00ff00;
                padding: 5px 10px;
                border-radius: 5px 5px 0 0;
                border: 1px solid #333;
                font-family: monospace;
                font-size: 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            `;
            header.innerHTML = `
                <span>🔧 MCP连接日志</span>
                <button onclick="document.getElementById('connectionLog').innerHTML=''"
                        style="background: #333; color: #00ff00; border: 1px solid #555; padding: 2px 6px; border-radius: 3px; cursor: pointer; font-size: 10px;">
                    清除
                </button>
            `;

            logContainer = document.createElement('div');
            logContainer.id = 'connectionLog';
            logContainer.style.cssText = `
                width: 400px;
                max-height: 300px;
                background: rgba(0,0,0,0.9);
                color: #00ff00;
                font-family: monospace;
                font-size: 12px;
                padding: 10px;
                border-radius: 0 0 5px 5px;
                overflow-y: auto;
                border: 1px solid #333;
                border-top: none;
            `;

            wrapper.appendChild(header);
            wrapper.appendChild(logContainer);
            document.body.appendChild(wrapper);
        }

        // 添加时间戳
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.textContent = `[${timestamp}] ${message}`;
        logContainer.appendChild(logEntry);

        // 保持最新的日志在底部
        logContainer.scrollTop = logContainer.scrollHeight;

        // 限制日志条数，避免内存泄漏
        const maxLogs = 50;
        while (logContainer.children.length > maxLogs) {
            logContainer.removeChild(logContainer.firstChild);
        }
    }

    // 初始化MCP连接
    async initializeMCPConnection() {
        const initPayload = {
            jsonrpc: '2.0',
            id: Date.now(),
            method: 'initialize',
            params: {
                protocolVersion: '2024-11-05',
                capabilities: {},
                clientInfo: {
                    name: 'industrial-analysis-client',
                    version: '1.0.0'
                }
            }
        };

        console.log('🔧 [DEBUG] MCP初始化请求:', JSON.stringify(initPayload, null, 2));
        this.updateConnectionLog('📤 发送初始化请求: ' + JSON.stringify(initPayload, null, 2));

        try {
            const response = await fetch(this.mcpServerUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json, text/event-stream'
                },
                body: JSON.stringify(initPayload)
            });

            console.log('🔧 [DEBUG] 初始化响应状态:', response.status);
            console.log('🔧 [DEBUG] 初始化响应头:', Object.fromEntries(response.headers.entries()));
            this.updateConnectionLog('📥 初始化响应状态: ' + response.status);
            this.updateConnectionLog('📥 响应头: ' + JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

            // 获取会话ID
            this.sessionId = response.headers.get('mcp-session-id');
            console.log('🔧 [DEBUG] 获取到会话ID:', this.sessionId);
            this.updateConnectionLog('🔑 获取到会话ID: ' + this.sessionId);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`初始化失败: ${response.status} - ${errorText}`);
            }

            // 对于事件流响应，我们不需要解析JSON
            if (response.headers.get('content-type')?.includes('text/event-stream')) {
                console.log('✅ MCP初始化成功，建立了事件流连接');

                // 发送initialized通知
                await this.sendInitializedNotification();
                return true;
            }

            const data = await response.json();
            console.log('🔧 [DEBUG] 初始化响应数据:', data);

            // 发送initialized通知
            await this.sendInitializedNotification();
            return true;

        } catch (error) {
            console.error('❌ MCP初始化失败:', error);
            throw error;
        }
    }

    // 发送initialized通知
    async sendInitializedNotification() {
        const notificationPayload = {
            jsonrpc: '2.0',
            method: 'notifications/initialized'
        };

        console.log('🔧 [DEBUG] 发送initialized通知...');
        this.updateConnectionLog('📤 发送initialized通知...');

        try {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            };

            // 添加会话ID
            if (this.sessionId) {
                headers['mcp-session-id'] = this.sessionId;
            }

            this.updateConnectionLog('📤 通知请求头: ' + JSON.stringify(headers, null, 2));

            const response = await fetch(this.mcpServerUrl, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(notificationPayload)
            });

            console.log('🔧 [DEBUG] Initialized通知响应状态:', response.status);
            this.updateConnectionLog('📥 Initialized通知响应状态: ' + response.status);

            if (response.ok) {
                console.log('✅ Initialized通知发送成功');
                this.updateConnectionLog('✅ Initialized通知发送成功');
            } else {
                console.warn('⚠️ Initialized通知发送失败:', response.status);
                this.updateConnectionLog('⚠️ Initialized通知发送失败: ' + response.status);
            }

        } catch (error) {
            console.error('❌ 发送initialized通知失败:', error);
        }
    }

    // 调用MCP工具的通用方法
    async callMCPTool(toolName, arguments = {}) {
        const requestPayload = {
            jsonrpc: '2.0',
            id: Date.now(),
            method: 'tools/call',
            params: {
                name: toolName,
                arguments: arguments  // 总是包含arguments字段，即使是空对象
            }
        };

        console.log('🔧 [DEBUG] MCP请求详情:');
        console.log('  URL:', this.mcpServerUrl);
        console.log('  工具名:', toolName);
        console.log('  参数:', arguments);
        console.log('  完整请求体:', JSON.stringify(requestPayload, null, 2));

        this.updateConnectionLog('📤 调用工具: ' + toolName);
        this.updateConnectionLog('📤 请求体: ' + JSON.stringify(requestPayload, null, 2));

        try {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            };

            // 如果有会话ID，添加到请求头
            if (this.sessionId) {
                headers['mcp-session-id'] = this.sessionId;
                console.log('🔧 [DEBUG] 使用会话ID:', this.sessionId);
            }

            const response = await fetch(this.mcpServerUrl, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestPayload)
            });

            console.log('🔧 [DEBUG] HTTP响应状态:', response.status);
            console.log('🔧 [DEBUG] HTTP响应头:', Object.fromEntries(response.headers.entries()));

            this.updateConnectionLog('📥 工具调用响应状态: ' + response.status);
            this.updateConnectionLog('📥 响应头: ' + JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));

            if (!response.ok) {
                const errorText = await response.text();
                console.error('🔧 [DEBUG] HTTP错误响应体:', errorText);
                this.updateConnectionLog('❌ HTTP错误响应: ' + errorText);
                throw new Error(`HTTP错误: ${response.status} - ${errorText}`);
            }

            // 检查是否是事件流响应
            if (response.headers.get('content-type')?.includes('text/event-stream')) {
                console.log('🔧 [DEBUG] 处理事件流响应...');
                this.updateConnectionLog('📥 处理事件流响应...');
                const text = await response.text();
                console.log('🔧 [DEBUG] 事件流原始数据:', text);
                this.updateConnectionLog('📥 事件流原始数据: ' + text.substring(0, 200) + (text.length > 200 ? '...' : ''));

                // 解析事件流格式
                const lines = text.split('\n');
                let jsonData = null;

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            jsonData = JSON.parse(line.substring(6));
                            break;
                        } catch (e) {
                            console.log('🔧 [DEBUG] 解析事件流数据失败:', e);
                        }
                    }
                }

                if (jsonData) {
                    console.log('🔧 [DEBUG] 解析的事件流JSON:', JSON.stringify(jsonData, null, 2));
                    this.updateConnectionLog('📥 解析的JSON: ' + JSON.stringify(jsonData, null, 2));

                    if (jsonData.error) {
                        console.error('🔧 [DEBUG] MCP错误详情:', jsonData.error);
                        this.updateConnectionLog('❌ MCP错误: ' + JSON.stringify(jsonData.error, null, 2));
                        throw new Error(jsonData.error.message || '工具调用失败');
                    }

                    // 处理MCP响应格式
                    if (jsonData.result && jsonData.result.content && jsonData.result.content[0]) {
                        const content = jsonData.result.content[0];
                        console.log('🔧 [DEBUG] 内容类型:', content.type);
                        console.log('🔧 [DEBUG] 内容数据:', content);

                        if (content.type === 'text') {
                            try {
                                const parsed = JSON.parse(content.text);
                                console.log('🔧 [DEBUG] 解析后的JSON:', parsed);
                                return parsed;
                            } catch {
                                console.log('🔧 [DEBUG] 非JSON文本响应:', content.text);
                                return content.text;
                            }
                        }
                    }

                    console.log('🔧 [DEBUG] 直接返回结果:', jsonData.result);
                    return jsonData.result;
                } else {
                    throw new Error('无法解析事件流响应');
                }
            } else {
                // 处理普通JSON响应
                const data = await response.json();
                console.log('🔧 [DEBUG] MCP响应数据:', JSON.stringify(data, null, 2));

                if (data.error) {
                    console.error('🔧 [DEBUG] MCP错误详情:', data.error);
                    throw new Error(data.error.message || '工具调用失败');
                }

                // 处理MCP响应格式
                if (data.result && data.result.content && data.result.content[0]) {
                    const content = data.result.content[0];
                    console.log('🔧 [DEBUG] 内容类型:', content.type);
                    console.log('🔧 [DEBUG] 内容数据:', content);

                    if (content.type === 'text') {
                        try {
                            const parsed = JSON.parse(content.text);
                            console.log('🔧 [DEBUG] 解析后的JSON:', parsed);
                            return parsed;
                        } catch {
                            console.log('🔧 [DEBUG] 非JSON文本响应:', content.text);
                            return content.text;
                        }
                    }
                }

                console.log('🔧 [DEBUG] 直接返回结果:', data.result);
                return data.result;
            }
        } catch (error) {
            console.error(`❌ [DEBUG] 调用MCP工具 ${toolName} 失败:`, error);
            throw error;
        }
    }

    // 初始化连接（已废弃，现在使用HTTP API）
    async initializeMCP() {
        // 不再需要MCP初始化，直接返回成功
        return { success: true };
    }

    // 设置连接状态
    setConnectionStatus(connected) {
        this.isConnected = connected;
        const statusElement = document.getElementById('connectionStatus');
        const connectBtn = document.getElementById('connectBtn');

        if (connected) {
            if (statusElement) {
                statusElement.textContent = '在线';
                statusElement.className = 'status-indicator online';
            }
            if (connectBtn) {
                connectBtn.textContent = '重新连接';
            }
        } else {
            if (statusElement) {
                statusElement.textContent = '离线';
                statusElement.className = 'status-indicator offline';
            }
            if (connectBtn) {
                connectBtn.textContent = '连接服务器';
            }
        }

        // 更新连接日志中的状态
        this.updateConnectionLog('🔗 连接状态更新: ' + (connected ? '✅ 已连接' : '❌ 未连接'));

        // 在页面主要区域显示连接状态
        if (!connected) {
            const statusMessage = document.createElement('div');
            statusMessage.id = 'connectionStatusMessage';
            statusMessage.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                z-index: 9999;
                font-size: 16px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            `;
            statusMessage.innerHTML = `
                <h3>🔌 MCP服务器未连接</h3>
                <p>请点击右上角的"连接服务器"按钮</p>
                <p>或查看右上角的连接日志了解详情</p>
            `;

            // 移除之前的状态消息
            const existingMessage = document.getElementById('connectionStatusMessage');
            if (existingMessage) {
                existingMessage.remove();
            }

            document.body.appendChild(statusMessage);
        } else {
            // 连接成功时移除状态消息
            const existingMessage = document.getElementById('connectionStatusMessage');
            if (existingMessage) {
                existingMessage.remove();
            }
        }
    }

    // 加载仪表板
    async loadDashboard() {
        if (!this.isConnected) return;

        await this.loadDatabaseInfo();
        await this.loadQuickStats();
    }

    // 加载系统状态信息
    async loadDatabaseInfo() {
        const dbInfoElement = document.getElementById('dbInfo');
        console.log('🔄 开始加载系统状态信息');
        dbInfoElement.innerHTML = '<div class="loading">加载中...</div>';

        try {
            console.log('🔄 调用get_system_status工具...');
            const response = await this.callMCPTool('get_system_status', {});
            console.log('🔄 系统状态响应:', response);

            if (response && response.status) {
                const newHTML = `
                    <div class="db-info">
                        <p><strong>系统状态:</strong> ${response.status}</p>
                        <p><strong>数据库:</strong> ${response.database || 'N/A'}</p>
                        <p><strong>记录数:</strong> ${response.records || 0}</p>
                        <p><strong>服务器:</strong> ${response.server || '工业数据分析系统'}</p>
                        <p><strong>端口:</strong> ${response.port || 9001}</p>
                    </div>
                `;

                dbInfoElement.innerHTML = newHTML;
                dbInfoElement.classList.remove('loading');
                console.log('🔄 系统状态信息加载完成');
            } else {
                console.log('🔄 系统状态响应格式错误');
                dbInfoElement.innerHTML = `<div class="error">加载失败: ${response?.error || '未知错误'}</div>`;
                dbInfoElement.classList.remove('loading');
            }
        } catch (error) {
            console.error('🔄 [DEBUG] 数据库信息加载错误:', error);
            dbInfoElement.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            dbInfoElement.classList.remove('loading');
        }

        console.log('🔄 [DEBUG] loadDatabaseInfo函数执行完成');
    }

    // 加载快速统计
    async loadQuickStats() {
        try {
            console.log('🔄 开始加载工业数据快速统计...');

            // 获取数据摘要
            const summaryResponse = await this.callMCPTool('get_data_summary', {});
            console.log('🔄 数据摘要响应:', summaryResponse);

            if (summaryResponse && summaryResponse.success && summaryResponse.summary) {
                const summary = summaryResponse.summary;
                document.getElementById('totalRecords').textContent = summary.total_records || '-';
                document.getElementById('avgAmount').textContent = summary.avg_pressure_1 ?
                    `${summary.avg_pressure_1.toFixed(3)} MPa` : '-';
            } else {
                document.getElementById('totalRecords').textContent = '-';
                document.getElementById('avgAmount').textContent = '-';
            }

            // 获取压力异常检测
            const anomalyResponse = await this.callMCPTool('detect_anomalies', {
                column: 'pressure_1',
                threshold: 2.0
            });

            console.log('🔄 异常检测响应:', anomalyResponse);

            if (anomalyResponse && anomalyResponse.success) {
                document.getElementById('anomalyCount').textContent = anomalyResponse.anomaly_count || '0';
            } else {
                document.getElementById('anomalyCount').textContent = '-';
            }
        } catch (error) {
            console.error('❌ 加载快速统计失败:', error);
            document.getElementById('totalRecords').textContent = '-';
            document.getElementById('avgAmount').textContent = '-';
            document.getElementById('anomalyCount').textContent = '-';
        }
    }

    // 生成快速图表（企业级ECharts版本）
    async generateQuickChart() {
        const chartContainer = document.getElementById('latestChart');
        chartContainer.innerHTML = '<div class="loading">生成中...</div>';

        try {
            const response = await this.callMCPTool('generate_bar_chart', {
                table: 'film',
                x_column: 'rating',
                y_column: 'rental_rate',
                title: '电影评级与租赁费用快速分析',
                limit: 5
            });

            if (response.success && response.chart_data) {
                // 创建ECharts容器
                chartContainer.innerHTML = '<div id="quickChart" style="width: 100%; height: 300px;"></div>';

                // 使用企业级图表渲染
                const chartData = response.chart_data.data || [];
                const options = {
                    title: response.chart_data.title || '快速图表',
                    seriesName: '租赁费用',
                    compact: true  // 紧凑模式
                };

                // 使用企业级图表生成器
                window.enterpriseCharts.createBarChart('quickChart', chartData, options);

                this.showNotification('快速图表生成成功！', 'success');
            } else {
                chartContainer.innerHTML = '<div class="error">图表生成失败</div>';
            }
        } catch (error) {
            chartContainer.innerHTML = `<div class="error">生成失败: ${error.message}</div>`;
        }
    }

    // 运行统计分析（增强版）
    async runStatistics() {
        // 验证表单
        if (!this.validateStatsForm()) {
            return;
        }

        const table = document.getElementById('statsTable').value;
        const column = document.getElementById('statsColumn').value;
        const startTime = document.getElementById('statsStartTime').value;
        const endTime = document.getElementById('statsEndTime').value;
        const timeColumn = document.getElementById('statsTimeColumn').value;
        const groupBy = document.getElementById('statsGroupBy').value;
        const limit = document.getElementById('statsLimit').value;
        const voiceReport = document.getElementById('statsVoiceReport').checked;

        const resultElement = document.getElementById('statisticsContent');
        resultElement.innerHTML = '<div class="loading">🔄 正在进行统计分析...</div>';

        try {
            // 构建参数
            const params = {
                table,
                column,
                time_column: timeColumn
            };

            if (startTime) params.start_time = startTime;
            if (endTime) params.end_time = endTime;
            if (groupBy) params.group_by = groupBy;
            if (limit) params.limit = parseInt(limit);

            // 调用统计分析工具
            const response = await this.callMCPTool('get_database_statistics', params);

            if (response.success) {
                const stats = response.data;

                // 显示统计结果
                this.displayStatsResults(stats, table, column);

                // 创建统计提醒（如果启用）
                if (document.getElementById('statsEnableAlert').checked) {
                    await this.createStatsAlert(stats, table, column);
                }

                // 语音播报（如果启用）
                if (voiceReport) {
                    const summary = this.generateStatsSummary(stats, table, column);
                    await this.speakText(summary);
                }

                // 启动实时监控（如果启用）
                if (document.getElementById('statsRealTime').checked) {
                    this.startStatsRealTimeMonitoring();
                }

                this.showNotification('统计分析完成！', 'success');

            } else {
                resultElement.innerHTML = `<div class="error">❌ 统计分析失败: ${response.error || '未知错误'}</div>`;
                this.showNotification('统计分析失败', 'error');
            }
        } catch (error) {
            resultElement.innerHTML = `<div class="error">❌ 分析失败: ${error.message}</div>`;
            this.showNotification('统计分析出错', 'error');
        }
    }

    // 验证统计分析表单
    validateStatsForm() {
        const table = document.getElementById('statsTable').value;
        const column = document.getElementById('statsColumn').value;

        if (!table) {
            this.showNotification('请选择数据表', 'warning');
            return false;
        }

        if (!column) {
            this.showNotification('请选择数据列', 'warning');
            return false;
        }

        // 验证时间范围
        const startTime = document.getElementById('statsStartTime').value;
        const endTime = document.getElementById('statsEndTime').value;

        if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
            this.showNotification('开始时间必须早于结束时间', 'warning');
            return false;
        }

        return true;
    }

    // 显示统计结果
    displayStatsResults(stats, table, column) {
        const resultElement = document.getElementById('statisticsContent');

        // 获取选中的统计指标
        const selectedMetrics = this.getSelectedStatsMetrics();

        let statsHtml = `
            <div class="stats-result">
                <div class="stats-header">
                    <h4>📊 ${table}表 - ${column}列统计结果</h4>
                    <div class="stats-timestamp">
                        分析时间: ${new Date().toLocaleString()}
                    </div>
                </div>
                <div class="stats-grid">
        `;

        // 根据选中的指标显示结果
        if (selectedMetrics.count && stats.count !== undefined) {
            statsHtml += `
                <div class="stat-item">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <span class="stat-label">记录数量</span>
                        <span class="stat-value">${stats.count.toLocaleString()}</span>
                    </div>
                </div>
            `;
        }

        if (selectedMetrics.sum && stats.sum !== undefined) {
            statsHtml += `
                <div class="stat-item">
                    <div class="stat-icon">➕</div>
                    <div class="stat-content">
                        <span class="stat-label">总和</span>
                        <span class="stat-value">${stats.sum.toLocaleString()}</span>
                    </div>
                </div>
            `;
        }

        if (selectedMetrics.average && stats.average !== undefined) {
            statsHtml += `
                <div class="stat-item">
                    <div class="stat-icon">📈</div>
                    <div class="stat-content">
                        <span class="stat-label">平均值</span>
                        <span class="stat-value">${stats.average.toFixed(2)}</span>
                    </div>
                </div>
            `;
        }

        if (selectedMetrics.min && stats.minimum !== undefined) {
            statsHtml += `
                <div class="stat-item">
                    <div class="stat-icon">⬇️</div>
                    <div class="stat-content">
                        <span class="stat-label">最小值</span>
                        <span class="stat-value">${stats.minimum.toFixed(2)}</span>
                    </div>
                </div>
            `;
        }

        if (selectedMetrics.max && stats.maximum !== undefined) {
            statsHtml += `
                <div class="stat-item">
                    <div class="stat-icon">⬆️</div>
                    <div class="stat-content">
                        <span class="stat-label">最大值</span>
                        <span class="stat-value">${stats.maximum.toFixed(2)}</span>
                    </div>
                </div>
            `;
        }

        if (selectedMetrics.stddev && stats.std_dev !== undefined) {
            statsHtml += `
                <div class="stat-item">
                    <div class="stat-icon">📏</div>
                    <div class="stat-content">
                        <span class="stat-label">标准差</span>
                        <span class="stat-value">${stats.std_dev.toFixed(2)}</span>
                    </div>
                </div>
            `;
        }

        if (selectedMetrics.variance && stats.variance !== undefined) {
            statsHtml += `
                <div class="stat-item">
                    <div class="stat-icon">📐</div>
                    <div class="stat-content">
                        <span class="stat-label">方差</span>
                        <span class="stat-value">${stats.variance.toFixed(2)}</span>
                    </div>
                </div>
            `;
        }

        statsHtml += `
                </div>
                <div class="stats-actions">
                    <button class="btn btn-sm" onclick="app.generateStatsChart()">📊 生成图表</button>
                    <button class="btn btn-sm" onclick="app.exportStatsData()">📁 导出数据</button>
                </div>
            </div>
        `;

        resultElement.innerHTML = statsHtml;
    }

    // 获取选中的统计指标
    getSelectedStatsMetrics() {
        return {
            count: document.getElementById('statsCount').checked,
            sum: document.getElementById('statsSum').checked,
            average: document.getElementById('statsAvg').checked,
            min: document.getElementById('statsMin').checked,
            max: document.getElementById('statsMax').checked,
            stddev: document.getElementById('statsStdDev').checked,
            variance: document.getElementById('statsVariance').checked,
            median: document.getElementById('statsMedian').checked
        };
    }

    // 生成统计摘要
    generateStatsSummary(stats, table, column) {
        const count = stats.count || 0;
        const avg = stats.average ? stats.average.toFixed(2) : '未知';
        const sum = stats.sum ? stats.sum.toFixed(2) : '未知';

        return `统计分析完成。表${table}的${column}列，共${count}条记录，平均值${avg}，总和${sum}。`;
    }

    // 创建统计提醒
    async createStatsAlert(stats, table, column) {
        const alertMetric = document.getElementById('statsAlertMetric').value;
        const alertOperator = document.getElementById('statsAlertOperator').value;
        const alertThreshold = parseFloat(document.getElementById('statsAlertThreshold').value);

        if (!alertThreshold) return;

        const metricValue = stats[alertMetric];
        if (metricValue === undefined) return;

        // 检查是否触发提醒
        let triggered = false;
        switch (alertOperator) {
            case '>': triggered = metricValue > alertThreshold; break;
            case '<': triggered = metricValue < alertThreshold; break;
            case '>=': triggered = metricValue >= alertThreshold; break;
            case '<=': triggered = metricValue <= alertThreshold; break;
            case '=': triggered = Math.abs(metricValue - alertThreshold) < 0.01; break;
        }

        if (triggered) {
            const alertMessage = `统计提醒：${table}表${column}列的${alertMetric}值${metricValue}${alertOperator}${alertThreshold}`;
            this.showNotification(alertMessage, 'warning');

            // 语音提醒
            await this.speakText(alertMessage);
        }
    }

    // 启动实时监控
    startStatsRealTimeMonitoring() {
        // 停止之前的监控
        if (this.statsMonitoringInterval) {
            clearInterval(this.statsMonitoringInterval);
        }

        const interval = parseInt(document.getElementById('statsRefreshInterval').value) * 1000;

        this.statsMonitoringInterval = setInterval(() => {
            this.runStatistics();
        }, interval);

        this.showNotification('实时监控已启动', 'info');
    }

    // 停止实时监控
    stopStatsRealTimeMonitoring() {
        if (this.statsMonitoringInterval) {
            clearInterval(this.statsMonitoringInterval);
            this.statsMonitoringInterval = null;
            this.showNotification('实时监控已停止', 'info');
        }
    }

    // 切换实时监控
    toggleStatsRealTime(enabled) {
        if (enabled) {
            this.startStatsRealTimeMonitoring();
        } else {
            this.stopStatsRealTimeMonitoring();
        }
    }

    // 立即刷新统计
    refreshStatsNow() {
        this.runStatistics();
    }

    // 导出统计结果
    exportStatistics() {
        const resultElement = document.getElementById('statisticsContent');
        const statsData = resultElement.querySelector('.stats-result');

        if (!statsData) {
            this.showNotification('没有可导出的统计数据', 'warning');
            return;
        }

        // 提取统计数据
        const statItems = statsData.querySelectorAll('.stat-item');
        const exportData = [];

        statItems.forEach(item => {
            const label = item.querySelector('.stat-label').textContent;
            const value = item.querySelector('.stat-value').textContent;
            exportData.push({ 指标: label, 数值: value });
        });

        // 转换为CSV
        const csv = this.convertToCSV(exportData);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `统计分析结果_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showNotification('统计结果已导出', 'success');
    }

    // 清空统计结果
    clearStatistics() {
        const resultElement = document.getElementById('statisticsContent');
        resultElement.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📊</div>
                <div class="empty-text">请配置参数并运行统计分析</div>
            </div>
        `;

        // 停止实时监控
        this.stopStatsRealTimeMonitoring();
        document.getElementById('statsRealTime').checked = false;

        this.showNotification('统计结果已清空', 'info');
    }

    // 生成统计图表
    async generateStatsChart() {
        const resultElement = document.getElementById('statisticsContent');
        const statsData = resultElement.querySelector('.stats-result');

        if (!statsData) {
            this.showNotification('没有可生成图表的统计数据', 'warning');
            return;
        }

        this.showLoading();

        try {
            // 提取统计数据
            const statItems = statsData.querySelectorAll('.stat-item');
            const chartData = [];
            const labels = [];
            const values = [];

            statItems.forEach(item => {
                const label = item.querySelector('.stat-label').textContent;
                const valueText = item.querySelector('.stat-value').textContent;
                const value = parseFloat(valueText.replace(/[^\d.-]/g, '')) || 0;

                labels.push(label);
                values.push(value);
                chartData.push({ label, value });
            });

            // 调用图表生成工具
            const response = await this.callMCPTool('generate_bar_chart', {
                table: 'statistics',
                x_column: 'metric',
                y_column: 'value',
                title: '统计分析图表',
                limit: 10
            });

            if (response.success && response.chart_data) {
                // 使用企业级图表渲染
                this.renderEnterpriseChart(response.chart_data, {
                    chart_type: 'bar',
                    title: '统计分析图表'
                });
                this.showNotification('统计图表生成成功', 'success');
            } else {
                throw new Error(response.error || '图表生成失败');
            }

        } catch (error) {
            console.error('生成统计图表失败:', error);
            this.showNotification('生成统计图表失败: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // 导出统计数据
    exportStatsData() {
        const resultElement = document.getElementById('statisticsContent');
        const statsData = resultElement.querySelector('.stats-result');

        if (!statsData) {
            this.showNotification('没有可导出的统计数据', 'warning');
            return;
        }

        // 提取统计数据
        const statItems = statsData.querySelectorAll('.stat-item');
        const exportData = [];

        statItems.forEach(item => {
            const label = item.querySelector('.stat-label').textContent;
            const value = item.querySelector('.stat-value').textContent;
            exportData.push({ 指标: label, 数值: value });
        });

        // 转换为CSV
        const csv = this.convertToCSV(exportData);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `统计数据_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showNotification('统计数据已导出', 'success');
    }

    // 处理统计语音控制
    async handleStatsVoiceControl() {
        const statusDiv = document.getElementById('statsVoiceStatus');

        try {
            statusDiv.textContent = '🎤 正在监听...';
            statusDiv.className = 'voice-status listening';

            // 模拟语音识别
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 模拟语音指令处理
            const commands = [
                '分析支付表金额列',
                '设置时间范围为最近一周',
                '启用实时监控',
                '导出统计结果'
            ];

            const command = commands[Math.floor(Math.random() * commands.length)];
            statusDiv.textContent = `识别到指令: ${command}`;
            statusDiv.className = 'voice-status success';

            // 执行相应操作
            await this.executeStatsVoiceCommand(command);

        } catch (error) {
            statusDiv.textContent = `语音控制失败: ${error.message}`;
            statusDiv.className = 'voice-status error';
        }

        // 3秒后重置状态
        setTimeout(() => {
            statusDiv.textContent = '点击开始语音控制';
            statusDiv.className = 'voice-status';
        }, 3000);
    }

    // 执行统计语音指令
    async executeStatsVoiceCommand(command) {
        if (command.includes('分析') && command.includes('表')) {
            // 解析表名和列名
            if (command.includes('支付表')) {
                document.getElementById('statsTable').value = 'payment';
            }
            if (command.includes('金额')) {
                document.getElementById('statsColumn').value = 'amount';
            }
            await this.runStatistics();
        } else if (command.includes('实时监控')) {
            document.getElementById('statsRealTime').checked = true;
            this.toggleStatsRealTime(true);
        } else if (command.includes('导出')) {
            this.exportStatistics();
        }
    }

    // 切换统计图表
    toggleStatsChart() {
        // 实现图表切换逻辑
        this.showNotification('图表功能开发中', 'info');
    }

    // 运行趋势分析（增强版）
    async runTrendAnalysis() {
        // 验证表单
        if (!this.validateTrendForm()) {
            return;
        }

        const table = document.getElementById('trendTable').value;
        const column = document.getElementById('trendColumn').value;
        const timeColumn = document.getElementById('trendTimeColumn').value;
        const period = document.getElementById('trendPeriod').value;
        const startTime = document.getElementById('trendStartTime').value;
        const endTime = document.getElementById('trendEndTime').value;
        const enableForecast = document.getElementById('trendEnableForecast').checked;
        const forecastPeriods = parseInt(document.getElementById('trendForecastPeriods').value);
        const compareColumn = document.getElementById('trendCompareColumn').value;
        const groupBy = document.getElementById('trendGroupBy').value;
        const voiceReport = document.getElementById('trendVoiceReport').checked;

        const resultElement = document.getElementById('trendContent');
        resultElement.innerHTML = '<div class="loading">🔄 正在进行趋势分析...</div>';

        try {
            // 构建参数
            const params = {
                table,
                time_column: timeColumn,
                value_column: column,
                period,
                forecast_days: forecastPeriods
            };

            if (startTime) params.start_time = startTime;
            if (endTime) params.end_time = endTime;
            if (compareColumn) params.compare_column = compareColumn;
            if (groupBy) params.group_by = groupBy;

            // 调用趋势分析工具
            const response = await this.callMCPTool('analyze_data_trend', params);

            if (response.success) {
                const trendData = response.data || response;

                // 显示趋势分析结果
                this.displayTrendResults(trendData, table, column, period);

                // 创建趋势告警（如果启用）
                if (document.getElementById('trendEnableAlert').checked) {
                    await this.createTrendAlert(trendData, table, column);
                }

                // 语音播报（如果启用）
                if (voiceReport) {
                    const summary = this.generateTrendSummary(trendData, table, column);
                    await this.speakText(summary);
                }

                // 启动实时监控（如果启用）
                if (document.getElementById('trendRealTime').checked) {
                    this.startTrendRealTimeMonitoring();
                }

                this.showNotification('趋势分析完成！', 'success');

            } else {
                resultElement.innerHTML = `<div class="error">❌ 趋势分析失败: ${response.error || '未知错误'}</div>`;
                this.showNotification('趋势分析失败', 'error');
            }
        } catch (error) {
            resultElement.innerHTML = `<div class="error">❌ 分析失败: ${error.message}</div>`;
            this.showNotification('趋势分析出错', 'error');
        }
    }

    // 验证趋势分析表单
    validateTrendForm() {
        const table = document.getElementById('trendTable').value;
        const column = document.getElementById('trendColumn').value;
        const timeColumn = document.getElementById('trendTimeColumn').value;

        if (!table) {
            this.showNotification('请选择数据表', 'warning');
            return false;
        }

        if (!column) {
            this.showNotification('请选择数值列', 'warning');
            return false;
        }

        if (!timeColumn) {
            this.showNotification('请选择时间列', 'warning');
            return false;
        }

        // 验证时间范围
        const startTime = document.getElementById('trendStartTime').value;
        const endTime = document.getElementById('trendEndTime').value;

        if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
            this.showNotification('开始时间必须早于结束时间', 'warning');
            return false;
        }

        return true;
    }

    // 显示趋势分析结果
    displayTrendResults(trendData, table, column, period) {
        const resultElement = document.getElementById('trendContent');

        // 获取趋势分析数据
        const trendAnalysis = trendData.trend_analysis || {};
        const dataPoints = trendData.data_points || 0;
        const direction = trendAnalysis.direction || '未知';
        const slope = trendAnalysis.slope || 0;
        const correlation = trendAnalysis.correlation || 0;
        const forecast = trendData.forecast || [];

        let resultHtml = `
            <div class="trend-result">
                <div class="trend-header">
                    <h4>📈 ${table}表 - ${column}列趋势分析</h4>
                    <div class="trend-timestamp">
                        分析时间: ${new Date().toLocaleString()}
                    </div>
                </div>

                <!-- 趋势概览 -->
                <div class="trend-overview">
                    <div class="trend-metric">
                        <div class="metric-icon">📊</div>
                        <div class="metric-content">
                            <span class="metric-label">数据点数</span>
                            <span class="metric-value">${dataPoints}</span>
                        </div>
                    </div>
                    <div class="trend-metric">
                        <div class="metric-icon">${direction === '上升' ? '📈' : direction === '下降' ? '📉' : '➡️'}</div>
                        <div class="metric-content">
                            <span class="metric-label">趋势方向</span>
                            <span class="metric-value ${direction === '上升' ? 'positive' : direction === '下降' ? 'negative' : 'neutral'}">${direction}</span>
                        </div>
                    </div>
                    <div class="trend-metric">
                        <div class="metric-icon">📐</div>
                        <div class="metric-content">
                            <span class="metric-label">趋势斜率</span>
                            <span class="metric-value">${slope.toFixed(4)}</span>
                        </div>
                    </div>
                    <div class="trend-metric">
                        <div class="metric-icon">🔗</div>
                        <div class="metric-content">
                            <span class="metric-label">相关系数</span>
                            <span class="metric-value">${correlation.toFixed(3)}</span>
                        </div>
                    </div>
                </div>

                <!-- 趋势图表 -->
                <div class="trend-chart-container">
                    <div id="trendAnalysisChart" style="width: 100%; height: 400px;"></div>
                </div>
        `;

        // 预测结果
        if (forecast && forecast.length > 0) {
            resultHtml += `
                <div class="trend-forecast">
                    <h5>🔮 趋势预测</h5>
                    <div class="forecast-grid">
            `;

            forecast.slice(0, 5).forEach((item, index) => {
                resultHtml += `
                    <div class="forecast-item">
                        <span class="forecast-period">第${index + 1}期</span>
                        <span class="forecast-value">${item.predicted_value ? item.predicted_value.toFixed(2) : 'N/A'}</span>
                    </div>
                `;
            });

            resultHtml += `
                    </div>
                </div>
            `;
        }

        // 趋势洞察
        resultHtml += `
                <div class="trend-insights">
                    <h5>💡 趋势洞察</h5>
                    <div class="insights-content">
                        ${this.generateTrendInsights(trendAnalysis, dataPoints)}
                    </div>
                </div>

                <div class="trend-actions">
                    <button class="btn btn-sm" onclick="app.generateTrendChart()">📊 生成图表</button>
                    <button class="btn btn-sm" onclick="app.exportTrendData()">📁 导出数据</button>
                    <button class="btn btn-sm" onclick="app.shareTrendAnalysis()">📤 分享分析</button>
                </div>
            </div>
        `;

        resultElement.innerHTML = resultHtml;

        // 渲染趋势图表
        this.renderTrendChart(trendData, table, column);
    }

    // 生成趋势洞察
    generateTrendInsights(trendAnalysis, dataPoints) {
        const direction = trendAnalysis.direction || '未知';
        const slope = trendAnalysis.slope || 0;
        const correlation = Math.abs(trendAnalysis.correlation || 0);

        let insights = [];

        if (dataPoints < 10) {
            insights.push('⚠️ 数据点较少，趋势分析可能不够准确，建议增加数据量。');
        }

        if (direction === '上升') {
            insights.push('📈 数据呈现上升趋势，表明指标正在增长。');
            if (slope > 0.1) {
                insights.push('🚀 增长速度较快，需要关注是否可持续。');
            }
        } else if (direction === '下降') {
            insights.push('📉 数据呈现下降趋势，需要关注潜在问题。');
            if (slope < -0.1) {
                insights.push('⚠️ 下降速度较快，建议及时采取措施。');
            }
        } else {
            insights.push('➡️ 数据趋势相对平稳，变化不明显。');
        }

        if (correlation > 0.8) {
            insights.push('🎯 趋势线拟合度很高，趋势预测较为可靠。');
        } else if (correlation > 0.5) {
            insights.push('📊 趋势线拟合度中等，预测结果仅供参考。');
        } else {
            insights.push('❓ 趋势线拟合度较低，数据可能存在较大波动。');
        }

        return insights.map(insight => `<div class="insight-item">${insight}</div>`).join('');
    }

    // 渲染趋势图表
    renderTrendChart(trendData, table, column) {
        // 这里可以使用现有的图表库来渲染趋势图
        // 暂时显示占位符
        const chartElement = document.getElementById('trendAnalysisChart');
        if (chartElement) {
            chartElement.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; border-radius: 8px;">
                    <div style="text-align: center; color: #666;">
                        <div style="font-size: 2rem; margin-bottom: 1rem;">📈</div>
                        <div>趋势图表将在此显示</div>
                        <div style="font-size: 0.8rem; margin-top: 0.5rem;">数据: ${table}.${column}</div>
                    </div>
                </div>
            `;
        }
    }

    // 生成趋势摘要
    generateTrendSummary(trendData, table, column) {
        const trendAnalysis = trendData.trend_analysis || {};
        const direction = trendAnalysis.direction || '未知';
        const dataPoints = trendData.data_points || 0;

        return `趋势分析完成。表${table}的${column}列，共${dataPoints}个数据点，趋势方向为${direction}。`;
    }

    // 创建趋势告警
    async createTrendAlert(trendData, table, column) {
        const alertType = document.getElementById('trendAlertType').value;
        const alertThreshold = parseFloat(document.getElementById('trendAlertThreshold').value);

        if (!alertThreshold) return;

        const trendAnalysis = trendData.trend_analysis || {};
        let triggered = false;
        let alertMessage = '';

        switch (alertType) {
            case 'direction_change':
                if (trendAnalysis.direction !== '平稳') {
                    triggered = true;
                    alertMessage = `趋势告警：${table}表${column}列趋势方向发生变化，当前为${trendAnalysis.direction}`;
                }
                break;
            case 'slope_threshold':
                const slope = Math.abs(trendAnalysis.slope || 0);
                if (slope > alertThreshold) {
                    triggered = true;
                    alertMessage = `趋势告警：${table}表${column}列趋势斜率${slope.toFixed(4)}超过阈值${alertThreshold}`;
                }
                break;
            case 'value_threshold':
                // 这里需要根据实际数据值来判断
                break;
        }

        if (triggered) {
            this.showNotification(alertMessage, 'warning');
            await this.speakText(alertMessage);
        }
    }

    // 启动趋势实时监控
    startTrendRealTimeMonitoring() {
        if (this.trendMonitoringInterval) {
            clearInterval(this.trendMonitoringInterval);
        }

        const interval = parseInt(document.getElementById('trendRefreshInterval').value) * 1000;

        this.trendMonitoringInterval = setInterval(() => {
            this.runTrendAnalysis();
        }, interval);

        this.showNotification('趋势实时监控已启动', 'info');
    }

    // 停止趋势实时监控
    stopTrendRealTimeMonitoring() {
        if (this.trendMonitoringInterval) {
            clearInterval(this.trendMonitoringInterval);
            this.trendMonitoringInterval = null;
            this.showNotification('趋势实时监控已停止', 'info');
        }
    }

    // 切换趋势实时监控
    toggleTrendRealTime(enabled) {
        if (enabled) {
            this.startTrendRealTimeMonitoring();
        } else {
            this.stopTrendRealTimeMonitoring();
        }
    }

    // 立即刷新趋势
    refreshTrendNow() {
        this.runTrendAnalysis();
    }

    // 导出趋势数据
    exportTrendData() {
        const resultElement = document.getElementById('trendContent');
        const trendData = resultElement.querySelector('.trend-result');

        if (!trendData) {
            this.showNotification('没有可导出的趋势数据', 'warning');
            return;
        }

        // 提取趋势数据
        const metrics = trendData.querySelectorAll('.trend-metric');
        const exportData = [];

        metrics.forEach(metric => {
            const label = metric.querySelector('.metric-label').textContent;
            const value = metric.querySelector('.metric-value').textContent;
            exportData.push({ 指标: label, 数值: value });
        });

        // 转换为CSV
        const csv = this.convertToCSV(exportData);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `趋势分析结果_${new Date().toISOString().slice(0, 10)}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showNotification('趋势数据已导出', 'success');
    }

    // 清空趋势结果
    clearTrendResult() {
        const resultElement = document.getElementById('trendContent');
        resultElement.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📈</div>
                <div class="empty-text">请配置参数并运行趋势分析</div>
            </div>
        `;

        // 停止实时监控
        this.stopTrendRealTimeMonitoring();
        document.getElementById('trendRealTime').checked = false;

        this.showNotification('趋势结果已清空', 'info');
    }

    // 处理趋势语音控制
    async handleTrendVoiceControl() {
        const statusDiv = document.getElementById('trendVoiceStatus');

        try {
            statusDiv.textContent = '🎤 正在监听...';
            statusDiv.className = 'voice-status listening';

            // 模拟语音识别
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 模拟语音指令处理
            const commands = [
                '分析支付表金额趋势',
                '设置预测周期为7天',
                '启用实时监控',
                '导出趋势数据'
            ];

            const command = commands[Math.floor(Math.random() * commands.length)];
            statusDiv.textContent = `识别到指令: ${command}`;
            statusDiv.className = 'voice-status success';

            // 执行相应操作
            await this.executeTrendVoiceCommand(command);

        } catch (error) {
            statusDiv.textContent = `语音控制失败: ${error.message}`;
            statusDiv.className = 'voice-status error';
        }

        // 3秒后重置状态
        setTimeout(() => {
            statusDiv.textContent = '点击开始语音控制';
            statusDiv.className = 'voice-status';
        }, 3000);
    }

    // 执行趋势语音指令
    async executeTrendVoiceCommand(command) {
        if (command.includes('分析') && command.includes('趋势')) {
            if (command.includes('支付表')) {
                document.getElementById('trendTable').value = 'payment';
            }
            if (command.includes('金额')) {
                document.getElementById('trendColumn').value = 'amount';
            }
            await this.runTrendAnalysis();
        } else if (command.includes('预测周期')) {
            const match = command.match(/(\d+)/);
            if (match) {
                document.getElementById('trendForecastPeriods').value = match[1];
            }
        } else if (command.includes('实时监控')) {
            document.getElementById('trendRealTime').checked = true;
            this.toggleTrendRealTime(true);
        } else if (command.includes('导出')) {
            this.exportTrendData();
        }
    }

    // 切换趋势视图
    toggleTrendView() {
        this.showNotification('视图切换功能开发中', 'info');
    }

    // 切换趋势全屏
    toggleTrendFullscreen() {
        const resultArea = document.getElementById('trendResult');
        if (resultArea.requestFullscreen) {
            resultArea.requestFullscreen();
        }
    }

    // 运行异常检测
    async runAnomalyDetection() {
        // 验证表单
        if (!this.validateAnomalyForm()) {
            return;
        }

        const table = document.getElementById('anomalyTable').value;
        const column = document.getElementById('anomalyColumn').value;
        const method = document.getElementById('anomalyMethod').value;
        const threshold = parseFloat(document.getElementById('anomalyThreshold').value);
        const startTime = document.getElementById('anomalyStartTime').value;
        const endTime = document.getElementById('anomalyEndTime').value;
        const timeColumn = document.getElementById('anomalyTimeColumn').value;
        const businessRule = document.getElementById('businessRule').value;
        const voiceAlert = document.getElementById('voiceAlert').checked;
        const soundAlert = document.getElementById('soundAlert').checked;
        const desktopNotification = document.getElementById('desktopNotification').checked;
        const alertThreshold = parseFloat(document.getElementById('alertThreshold').value);
        const batchSize = parseInt(document.getElementById('batchSize').value);
        const maxResults = parseInt(document.getElementById('maxResults').value);
        const enableSampling = document.getElementById('enableSampling').checked;
        const resultElement = document.getElementById('anomalyResult');

        resultElement.innerHTML = '<div class="loading">🔍 检测中...</div>';

        try {
            // 构建请求参数
            const params = {
                table,
                column,
                method,
                threshold
            };

            // 添加时间范围参数
            if (startTime) params.start_time = startTime;
            if (endTime) params.end_time = endTime;
            if (timeColumn) params.time_column = timeColumn;

            // 添加业务规则参数
            if (method === 'business_rule' && businessRule) {
                params.business_rule = businessRule;
            }

            // 添加性能优化参数
            if (batchSize) params.batch_size = batchSize;
            if (maxResults > 0) params.max_results = maxResults;
            if (enableSampling) params.enable_sampling = true;

            const response = await this.callMCPTool('detect_data_anomalies', params);
            
            if (response.success) {
                const data = response.data;
                this.lastAnomalyResults = data; // 保存结果用于导出

                let anomaliesHtml = '';
                let alertLevel = 'info';

                if (data.anomalies && data.anomalies.length > 0) {
                    // 根据异常数量确定告警级别
                    const anomalyRate = data.anomaly_rate || 0;
                    if (anomalyRate > 10) alertLevel = 'critical';
                    else if (anomalyRate > 5) alertLevel = 'warning';
                    else if (anomalyRate > 1) alertLevel = 'info';

                    anomaliesHtml = `
                        <h4>🚨 异常数据详情 (前10条):</h4>
                        <div class="anomaly-list">
                            ${data.anomalies.slice(0, 10).map((anomaly, index) => `
                                <div class="anomaly-item ${alertLevel}">
                                    <span class="anomaly-index">${index + 1}</span>
                                    <span class="anomaly-value">值: ${anomaly.value}</span>
                                    <span class="anomaly-reason">${anomaly.reason}</span>
                                    ${anomaly.timestamp ? `<span class="anomaly-time">时间: ${anomaly.timestamp}</span>` : ''}
                                </div>
                            `).join('')}
                        </div>
                        ${data.anomalies.length > 10 ? `<p class="more-anomalies">还有 ${data.anomalies.length - 10} 个异常数据...</p>` : ''}
                    `;
                }

                const timeRangeInfo = startTime && endTime ?
                    `<div class="time-range-info">📅 时间范围: ${startTime} 至 ${endTime}</div>` : '';

                resultElement.innerHTML = `
                    <div class="anomaly-result ${alertLevel}">
                        <h3>🔍 异常检测结果</h3>
                        ${timeRangeInfo}
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">总记录数</span>
                                <span class="stat-value">${data.total_count || 0}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">异常记录数</span>
                                <span class="stat-value ${alertLevel}">${data.anomaly_count || 0}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">异常率</span>
                                <span class="stat-value ${alertLevel}">${data.anomaly_rate ? data.anomaly_rate.toFixed(2) + '%' : '0%'}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">检测方法</span>
                                <span class="stat-value">${this.getMethodDisplayName(method)}</span>
                            </div>
                        </div>
                        ${anomaliesHtml}
                    </div>
                `;

                // 启用导出按钮
                document.getElementById('exportAnomalyResults').disabled = false;

                // 处理告警
                if (data.anomaly_count > 0 && data.anomaly_rate >= alertThreshold) {
                    this.handleAnomalyAlert(data, {
                        voice: voiceAlert,
                        sound: soundAlert,
                        desktop: desktopNotification,
                        level: alertLevel,
                        threshold: alertThreshold
                    });
                }

                // 异常检测完成后确保字体完整性
                setTimeout(() => {
                    this.ensurePageFontIntegrity();
                }, 200);
            } else {
                resultElement.innerHTML = `<div class="error">❌ 检测失败: ${response.error}</div>`;
            }
        } catch (error) {
            resultElement.innerHTML = `<div class="error">❌ 检测失败: ${error.message}</div>`;
        }
    }

    // 实时监控切换
    toggleRealTimeMonitoring() {
        const toggleBtn = document.getElementById('toggleRealTimeMonitoring');
        const intervalSelect = document.getElementById('monitoringInterval');
        const monitoringStatus = document.getElementById('monitoringStatus');

        if (this.realTimeMonitoring) {
            // 停止监控
            clearInterval(this.monitoringInterval);
            clearInterval(this.countdownInterval);
            this.realTimeMonitoring = false;
            this.checkCount = 0;

            toggleBtn.textContent = '📡 开始监控';
            toggleBtn.className = 'btn btn-secondary';
            monitoringStatus.style.display = 'none';

            this.showNotification('实时监控已停止', 'info');
        } else {
            // 开始监控
            const interval = parseInt(intervalSelect.value) * 1000;
            this.checkCount = 0;

            // 设置监控间隔
            this.monitoringInterval = setInterval(() => {
                this.checkCount++;
                document.getElementById('checkCount').textContent = this.checkCount;
                this.runAnomalyDetection();
                this.updateNextCheckTime(interval);
            }, interval);

            // 设置倒计时更新
            this.countdownInterval = setInterval(() => {
                this.updateCountdown();
            }, 1000);

            this.realTimeMonitoring = true;
            toggleBtn.textContent = '⏹️ 停止监控';
            toggleBtn.className = 'btn btn-warning';

            // 显示监控状态
            monitoringStatus.style.display = 'block';
            document.getElementById('currentInterval').textContent = `${intervalSelect.value}秒`;
            this.updateNextCheckTime(interval);

            this.showNotification(`实时监控已启动，间隔 ${intervalSelect.value} 秒`, 'success');
        }
    }

    // 更新下次检测时间
    updateNextCheckTime(interval) {
        this.nextCheckTime = new Date(Date.now() + interval);
        this.updateCountdown();
    }

    // 更新倒计时显示
    updateCountdown() {
        if (!this.nextCheckTime) return;

        const now = new Date();
        const diff = this.nextCheckTime - now;

        if (diff <= 0) {
            document.getElementById('nextCheck').textContent = '检测中...';
        } else {
            const seconds = Math.ceil(diff / 1000);
            document.getElementById('nextCheck').textContent = `${seconds}秒后`;
        }
    }

    // 导出异常结果
    exportAnomalyResults() {
        if (!this.lastAnomalyResults) {
            this.showNotification('没有可导出的结果', 'warning');
            return;
        }

        const data = this.lastAnomalyResults;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

        // 创建CSV内容
        let csvContent = '序号,异常值,异常原因,时间戳\n';
        if (data.anomalies) {
            data.anomalies.forEach((anomaly, index) => {
                csvContent += `${index + 1},"${anomaly.value}","${anomaly.reason}","${anomaly.timestamp || ''}"\n`;
            });
        }

        // 下载文件
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `anomaly_results_${timestamp}.csv`;
        link.click();

        this.showNotification('异常结果已导出', 'success');
    }

    // 处理异常告警
    handleAnomalyAlert(data, alertConfig) {
        const anomalyCount = data.anomaly_count || 0;
        const anomalyRate = data.anomaly_rate || 0;

        // 桌面通知
        if (alertConfig.desktop && Notification.permission === "granted") {
            const title = `🚨 检测到 ${anomalyCount} 个异常数据`;
            const body = `异常率: ${anomalyRate.toFixed(2)}% (阈值: ${alertConfig.threshold}%)`;
            const notification = new Notification(title, {
                body,
                icon: '/favicon.ico',
                tag: 'anomaly-alert',
                requireInteraction: alertConfig.level === 'critical'
            });

            // 5秒后自动关闭通知
            setTimeout(() => notification.close(), 5000);
        } else if (alertConfig.desktop && Notification.permission !== "denied") {
            Notification.requestPermission().then(permission => {
                if (permission === "granted") {
                    this.handleAnomalyAlert(data, alertConfig);
                }
            });
        }

        // 声音告警
        if (alertConfig.sound) {
            this.playAlertSound(alertConfig.level);
        }

        // 语音播报
        if (alertConfig.voice && 'speechSynthesis' in window) {
            const message = `检测到${anomalyCount}个异常数据，异常率为${anomalyRate.toFixed(1)}%，超过设定阈值${alertConfig.threshold}%`;
            const utterance = new SpeechSynthesisUtterance(message);
            utterance.lang = 'zh-CN';
            utterance.rate = 0.9; // 稍微慢一点
            utterance.volume = 0.8;
            speechSynthesis.speak(utterance);
        }

        // 在界面上显示告警信息
        this.showAlertBanner(anomalyCount, anomalyRate, alertConfig.level);
    }

    // 显示告警横幅
    showAlertBanner(anomalyCount, anomalyRate, level) {
        const banner = document.createElement('div');
        banner.className = `alert-banner ${level}`;
        banner.innerHTML = `
            <div class="alert-content">
                <span class="alert-icon">🚨</span>
                <span class="alert-text">检测到 ${anomalyCount} 个异常数据，异常率 ${anomalyRate.toFixed(2)}%</span>
                <button class="alert-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 插入到页面顶部
        const contentArea = document.querySelector('.content-area');
        contentArea.insertBefore(banner, contentArea.firstChild);

        // 5秒后自动移除
        setTimeout(() => {
            if (banner.parentNode) {
                banner.remove();
            }
        }, 5000);
    }

    // 播放告警声音
    playAlertSound(level) {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 根据告警级别设置不同频率
        if (level === 'critical') {
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(400, audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);
        } else {
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
        }

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    }

    // 获取检测方法显示名称
    getMethodDisplayName(method) {
        const methodNames = {
            'zscore': 'Z-Score 统计方法',
            'iqr': 'IQR 四分位数方法',
            'business_rule': '业务规则检测',
            'threshold': '阈值范围检测',
            'percentile': '百分位数检测'
        };
        return methodNames[method] || method;
    }

    // 清空结果
    clearResults() {
        const resultElement = document.getElementById('anomalyResult');
        resultElement.innerHTML = '';

        // 清空保存的结果
        this.lastAnomalyResults = null;

        // 禁用导出按钮
        document.getElementById('exportAnomalyResults').disabled = true;

        // 清除告警横幅
        const alertBanners = document.querySelectorAll('.alert-banner');
        alertBanners.forEach(banner => banner.remove());

        this.showNotification('结果已清空', 'info');
    }

    // 初始化页面时设置默认时间
    initializeDefaultTimes() {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

        // 设置默认时间范围为过去1小时
        document.getElementById('anomalyStartTime').value = oneHourAgo.toISOString().slice(0, 16);
        document.getElementById('anomalyEndTime').value = now.toISOString().slice(0, 16);
    }

    // 验证表单输入
    validateAnomalyForm() {
        const table = document.getElementById('anomalyTable').value;
        const column = document.getElementById('anomalyColumn').value;
        const method = document.getElementById('anomalyMethod').value;
        const threshold = document.getElementById('anomalyThreshold').value;

        if (!table || !column || !method || !threshold) {
            this.showNotification('请填写所有必需字段', 'warning');
            return false;
        }

        if (method === 'business_rule') {
            const businessRule = document.getElementById('businessRule').value;
            if (!businessRule.trim()) {
                this.showNotification('业务规则检测需要填写规则表达式', 'warning');
                return false;
            }
        }

        return true;
    }

    // 语音控制功能
    toggleVoiceControl() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            this.showNotification('您的浏览器不支持语音识别功能', 'warning');
            return;
        }

        if (this.isListening) {
            this.stopVoiceControl();
        } else {
            this.startVoiceControl();
        }
    }

    startVoiceControl() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.speechRecognition = new SpeechRecognition();

        const language = document.getElementById('voiceLanguage').value;
        this.speechRecognition.lang = language;
        this.speechRecognition.continuous = false;
        this.speechRecognition.interimResults = false;

        const startBtn = document.getElementById('startVoiceControl');
        const statusDiv = document.getElementById('voiceStatus');

        this.speechRecognition.onstart = () => {
            this.isListening = true;
            startBtn.textContent = '🛑 停止语音';
            startBtn.className = 'btn btn-voice listening';
            statusDiv.textContent = '正在听取语音指令...';
            statusDiv.className = 'voice-status listening';
        };

        this.speechRecognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            statusDiv.textContent = `识别到: ${transcript}`;
            statusDiv.className = 'voice-status processing';

            this.processVoiceCommand(transcript);
        };

        this.speechRecognition.onerror = (event) => {
            statusDiv.textContent = `语音识别错误: ${event.error}`;
            statusDiv.className = 'voice-status';
            this.stopVoiceControl();
        };

        this.speechRecognition.onend = () => {
            this.stopVoiceControl();
        };

        this.speechRecognition.start();
    }

    stopVoiceControl() {
        if (this.speechRecognition) {
            this.speechRecognition.stop();
        }

        this.isListening = false;
        const startBtn = document.getElementById('startVoiceControl');
        const statusDiv = document.getElementById('voiceStatus');

        startBtn.textContent = '🎤 开始语音控制';
        startBtn.className = 'btn btn-voice';
        statusDiv.textContent = '点击开始语音控制';
        statusDiv.className = 'voice-status';
    }

    processVoiceCommand(command) {
        const statusDiv = document.getElementById('voiceStatus');
        command = command.toLowerCase();

        try {
            if (command.includes('检测异常') || command.includes('开始检测') || command.includes('异常检测')) {
                this.runAnomalyDetection();
                statusDiv.textContent = '执行异常检测...';
                statusDiv.className = 'voice-status success';

            } else if (command.includes('开始监控') || command.includes('实时监控')) {
                if (!this.realTimeMonitoring) {
                    document.getElementById('realTimeMonitoring').checked = true;
                    this.toggleRealTimeMonitoring();
                }
                statusDiv.textContent = '启动实时监控...';
                statusDiv.className = 'voice-status success';

            } else if (command.includes('停止监控') || command.includes('关闭监控')) {
                if (this.realTimeMonitoring) {
                    this.toggleRealTimeMonitoring();
                }
                statusDiv.textContent = '停止实时监控...';
                statusDiv.className = 'voice-status success';

            } else if (command.includes('导出结果') || command.includes('导出数据')) {
                this.exportAnomalyResults();
                statusDiv.textContent = '导出异常结果...';
                statusDiv.className = 'voice-status success';

            } else if (command.includes('清空结果') || command.includes('清除结果')) {
                this.clearResults();
                statusDiv.textContent = '清空结果...';
                statusDiv.className = 'voice-status success';

            } else if (command.includes('设置阈值')) {
                // 尝试从语音中提取数字
                const numbers = command.match(/\d+(\.\d+)?/g);
                if (numbers && numbers.length > 0) {
                    const threshold = parseFloat(numbers[0]);
                    document.getElementById('anomalyThreshold').value = threshold;
                    statusDiv.textContent = `阈值设置为 ${threshold}`;
                    statusDiv.className = 'voice-status success';
                } else {
                    statusDiv.textContent = '未识别到有效的阈值数字';
                    statusDiv.className = 'voice-status';
                }

            } else {
                statusDiv.textContent = `未识别的指令: ${command}`;
                statusDiv.className = 'voice-status';

                // 语音提示可用指令
                if ('speechSynthesis' in window) {
                    const message = '可用指令包括：检测异常、开始监控、停止监控、导出结果、清空结果、设置阈值';
                    const utterance = new SpeechSynthesisUtterance(message);
                    utterance.lang = 'zh-CN';
                    speechSynthesis.speak(utterance);
                }
            }
        } catch (error) {
            statusDiv.textContent = `指令执行失败: ${error.message}`;
            statusDiv.className = 'voice-status';
        }

        // 3秒后重置状态
        setTimeout(() => {
            if (statusDiv.className !== 'voice-status listening') {
                statusDiv.textContent = '点击开始语音控制';
                statusDiv.className = 'voice-status';
            }
        }, 3000);
    }

    // 生成柱状图
    async generateBarChart() {
        const table = document.getElementById('barTable').value;
        const xColumn = document.getElementById('barXColumn').value;
        const yColumn = document.getElementById('barYColumn').value;
        const title = document.getElementById('barTitle').value;
        const limit = parseInt(document.getElementById('barLimit').value);
        
        await this.generateChart('generate_bar_chart', {
            table, x_column: xColumn, y_column: yColumn, title, limit
        });
    }

    // 生成饼状图
    async generatePieChart() {
        const table = document.getElementById('pieTable').value;
        const labelColumn = document.getElementById('pieLabelColumn').value;
        const valueColumn = document.getElementById('pieValueColumn').value;
        const title = document.getElementById('pieTitle').value;
        
        await this.generateChart('generate_pie_chart', {
            table, label_column: labelColumn, value_column: valueColumn, title
        });
    }

    // 生成趋势图
    async generateLineChart() {
        const table = document.getElementById('lineTable').value;
        const xColumn = document.getElementById('lineXColumn').value;
        const yColumn = document.getElementById('lineYColumn').value;
        const title = document.getElementById('lineTitle').value;
        const timeRange = document.getElementById('lineTimeRange').value;

        await this.generateChart('generate_trend_chart', {
            table, x_column: xColumn, y_column: yColumn, title, time_range: timeRange, chart_type: 'line'
        });
    }

    // 生成散点图
    async generateScatterChart() {
        const table = document.getElementById('scatterTable').value;
        const xColumn = document.getElementById('scatterXColumn').value;
        const yColumn = document.getElementById('scatterYColumn').value;
        const title = document.getElementById('scatterTitle').value;

        await this.generateChart('generate_scatter_chart', {
            table, x_column: xColumn, y_column: yColumn, title, chart_type: 'scatter'
        });
    }

    // 生成热力图
    async generateHeatmapChart() {
        const table = document.getElementById('heatmapTable').value;
        const xColumn = document.getElementById('heatmapXColumn').value;
        const yColumn = document.getElementById('heatmapYColumn').value;
        const title = document.getElementById('heatmapTitle').value;

        await this.generateChart('generate_heatmap_chart', {
            table, x_column: xColumn, y_column: yColumn, title, chart_type: 'heatmap'
        });
    }

    // 企业级图表生成方法
    async generateChart(toolName, params) {
        this.showLoading();

        try {
            const response = await this.callMCPTool(toolName, params);

            if (response.success && response.chart_data) {
                // 使用企业级图表渲染
                this.renderEnterpriseChart(response.chart_data, params);
                this.showNotification('图表生成成功！', 'success');
            } else {
                this.showChartError(response.error || '未知错误');
                this.showNotification('图表生成失败', 'error');
            }
        } catch (error) {
            this.showChartError(error.message);
            this.showNotification('图表生成失败', 'error');
        }

        this.hideLoading();
    }

    // 渲染企业级图表
    renderEnterpriseChart(chartData, params) {
        console.log('🎨 开始渲染企业级图表');
        console.log('📊 图表数据:', chartData);
        console.log('⚙️ 参数:', params);

        const chartType = params.chart_type || chartData.chart_type || 'bar';
        const data = chartData.data || [];

        console.log(`📈 图表类型: ${chartType}, 数据点数: ${data.length}`);

        // 更新图表信息
        document.getElementById('chartDataPoints').textContent = data.length;
        document.getElementById('chartType').textContent = this.getChartTypeName(chartType);
        document.getElementById('chartTime').textContent = new Date().toLocaleString();

        // 生成数据洞察
        this.generateDataInsights(data, chartType);

        // 检查ECharts和企业图表生成器
        if (typeof echarts === 'undefined') {
            console.error('❌ ECharts未加载');
            this.showChartError('ECharts图表库未加载');
            return;
        }

        if (!window.enterpriseCharts) {
            console.error('❌ 企业图表生成器未初始化');
            this.showChartError('企业图表生成器未初始化');
            return;
        }

        console.log('✅ ECharts和企业图表生成器已就绪');

        // 根据图表类型渲染
        const options = {
            title: chartData.title || params.title,
            seriesName: this.getSeriesName(params),
            onClick: (params) => {
                this.onChartClick(params);
            }
        };

        console.log(`🎯 准备渲染${chartType}图表，选项:`, options);

        try {
            switch (chartType) {
                case 'bar':
                    console.log('📊 渲染柱状图');
                    window.enterpriseCharts.createBarChart('enterpriseChart', data, options);
                    break;
                case 'pie':
                    console.log('🥧 渲染饼状图');
                    window.enterpriseCharts.createPieChart('enterpriseChart', data, options);
                    break;
                case 'line':
                    console.log('📈 渲染趋势图');
                    window.enterpriseCharts.createLineChart('enterpriseChart', data, options);
                    break;
                case 'scatter':
                    console.log('🔵 渲染散点图');
                    this.createScatterChart(data, options);
                    break;
                case 'heatmap':
                    console.log('🔥 渲染热力图');
                    this.createHeatmapChart(data, options);
                    break;
                default:
                    console.error(`❌ 未知的图表类型: ${chartType}`);
                    this.showChartError(`未知的图表类型: ${chartType}`);
            }
            console.log('✅ 图表渲染完成');

            // 图表渲染完成后，确保页面字体不被影响
            setTimeout(() => {
                this.ensurePageFontIntegrity();
                console.log('🔤 页面字体保护已应用');
            }, 200);

        } catch (error) {
            console.error('❌ 图表渲染失败:', error);
            this.showChartError(`图表渲染失败: ${error.message}`);
        }
    }

    // 获取图表类型中文名称
    getChartTypeName(type) {
        const names = {
            'bar': '柱状图',
            'pie': '饼状图',
            'line': '趋势图',
            'scatter': '散点图',
            'heatmap': '热力图'
        };
        return names[type] || type;
    }

    // 获取系列名称
    getSeriesName(params) {
        if (params.y_column) return params.y_column;
        if (params.value_column) return params.value_column;
        return '数值';
    }

    // 生成数据洞察
    generateDataInsights(data, chartType) {
        const insights = [];

        if (data.length === 0) {
            insights.push('暂无数据可分析');
        } else {
            insights.push(`共有 ${data.length} 个数据点`);

            if (chartType === 'bar' || chartType === 'line') {
                const values = data.map(item => item.value || item.y || 0);
                const max = Math.max(...values);
                const min = Math.min(...values);
                const avg = values.reduce((a, b) => a + b, 0) / values.length;

                insights.push(`最大值: ${max.toFixed(2)}`);
                insights.push(`最小值: ${min.toFixed(2)}`);
                insights.push(`平均值: ${avg.toFixed(2)}`);

                const maxItem = data.find(item => (item.value || item.y) === max);
                if (maxItem) {
                    insights.push(`最高值出现在: ${maxItem.name || maxItem.x}`);
                }
            }

            if (chartType === 'pie') {
                const total = data.reduce((sum, item) => sum + (item.value || 0), 0);
                const maxItem = data.reduce((max, item) =>
                    (item.value || 0) > (max.value || 0) ? item : max, data[0]);

                insights.push(`总计: ${total.toFixed(2)}`);
                insights.push(`最大占比: ${maxItem.name} (${((maxItem.value / total) * 100).toFixed(1)}%)`);
            }
        }

        const insightsHtml = insights.map(insight => `<p>• ${insight}</p>`).join('');
        document.getElementById('chartInsights').innerHTML = insightsHtml;
    }

    // 图表点击事件
    onChartClick(params) {
        console.log('图表点击:', params);
        this.showNotification(`点击了: ${params.name} - ${params.value}`, 'info');
    }

    // 显示图表错误
    showChartError(error) {
        document.getElementById('enterpriseChart').innerHTML = `
            <div class="error-state">
                <span class="error-icon">❌</span>
                <h3>图表生成失败</h3>
                <p>${error}</p>
            </div>
        `;
    }

    // 执行SQL查询
    async executeSql() {
        const sql = document.getElementById('sqlEditor').value.trim();
        const resultElement = document.getElementById('sqlResult');

        if (!sql) {
            this.showNotification('请输入SQL查询语句', 'warning');
            return;
        }

        resultElement.innerHTML = '<div class="loading">执行中...</div>';

        try {
            const response = await this.callMCPTool('execute_custom_sql', { sql });
            
            if (response.success) {
                const data = response.data;
                let tableHtml = '';
                
                if (data.rows && data.rows.length > 0) {
                    const columns = data.columns || Object.keys(data.rows[0]);
                    tableHtml = `
                        <table class="result-table">
                            <thead>
                                <tr>
                                    ${columns.map(col => `<th>${col}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.rows.slice(0, 100).map(row => `
                                    <tr>
                                        ${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                    
                    if (data.rows.length > 100) {
                        tableHtml += `<p class="result-note">显示前100条记录，共${data.row_count}条</p>`;
                    }
                } else {
                    tableHtml = '<p>查询执行成功，但没有返回数据</p>';
                }

                resultElement.innerHTML = `
                    <div class="sql-result">
                        <h3>查询结果</h3>
                        <p>返回记录数: ${data.row_count || 0}</p>
                        ${tableHtml}
                    </div>
                `;
                this.showNotification('SQL查询执行成功！', 'success');
            } else {
                resultElement.innerHTML = `<div class="error">查询失败: ${response.error}</div>`;
                this.showNotification('SQL查询失败', 'error');
            }
        } catch (error) {
            resultElement.innerHTML = `<div class="error">执行失败: ${error.message}</div>`;
            this.showNotification('SQL查询失败', 'error');
        }
    }

    // 格式化SQL
    formatSql() {
        const sqlEditor = document.getElementById('sqlEditor');
        let sql = sqlEditor.value;
        
        // 简单的SQL格式化
        sql = sql.replace(/\s+/g, ' ').trim();
        sql = sql.replace(/\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|HAVING|JOIN|LEFT JOIN|RIGHT JOIN|INNER JOIN|OUTER JOIN|ON|AND|OR|IN|NOT|EXISTS|UNION|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\b/gi, '\n$1');
        sql = sql.replace(/,/g, ',\n    ');
        sql = sql.replace(/\n\s*\n/g, '\n');
        
        sqlEditor.value = sql;
        this.showNotification('SQL已格式化', 'success');
    }

    // 清空SQL
    clearSql() {
        document.getElementById('sqlEditor').value = '';
        document.getElementById('sqlResult').innerHTML = '';
        this.showNotification('SQL编辑器已清空', 'success');
    }

    // 开始语音输入
    startVoiceInput() {
        // 这里可以集成Web Speech API或调用MCP的语音功能
        this.showNotification('语音功能开发中...', 'warning');
        
        // 模拟语音输入
        document.getElementById('voiceText').textContent = '正在监听语音输入...';
        document.getElementById('startVoice').disabled = true;
        document.getElementById('stopVoice').disabled = false;
    }

    // 停止语音输入
    stopVoiceInput() {
        document.getElementById('voiceText').textContent = '语音输入已停止';
        document.getElementById('startVoice').disabled = false;
        document.getElementById('stopVoice').disabled = true;
    }

    // 加载图表库
    loadGallery() {
        const galleryContent = document.getElementById('galleryContent');
        galleryContent.innerHTML = `
            <div class="gallery-item">
                <div class="chart-thumbnail">
                    <span class="chart-icon">📊</span>
                    <p>示例柱状图</p>
                </div>
            </div>
            <div class="gallery-item">
                <div class="chart-thumbnail">
                    <span class="chart-icon">🥧</span>
                    <p>示例饼状图</p>
                </div>
            </div>
            <div class="gallery-item">
                <div class="chart-thumbnail">
                    <span class="chart-icon">📈</span>
                    <p>示例趋势图</p>
                </div>
            </div>
        `;
    }

    // 清空图表库
    clearGallery() {
        const galleryContent = document.getElementById('galleryContent');
        galleryContent.innerHTML = `
            <div class="empty-state">
                <span class="empty-icon">🖼️</span>
                <p>图表库已清空</p>
            </div>
        `;
        this.showNotification('图表库已清空', 'success');
    }

    // 加载提醒列表
    loadAlerts() {
        const alertsList = document.getElementById('alertsList');
        alertsList.innerHTML = `
            <div class="alert-item">
                <div class="alert-info">
                    <h4>支付金额异常提醒</h4>
                    <p>监控表: payment | 列: amount | 条件: 大于 100</p>
                    <span class="alert-status active">激活</span>
                </div>
                <div class="alert-actions">
                    <button class="btn btn-sm">编辑</button>
                    <button class="btn btn-sm btn-danger">删除</button>
                </div>
            </div>
        `;
    }

    // 创建提醒
    createAlert() {
        const name = document.getElementById('alertName').value;
        const table = document.getElementById('alertTable').value;
        const column = document.getElementById('alertColumn').value;
        const condition = document.getElementById('alertCondition').value;
        const value = document.getElementById('alertValue').value;

        if (!name || !value) {
            this.showNotification('请填写完整的提醒信息', 'warning');
            return;
        }

        // 这里可以调用后端API创建提醒
        this.showNotification(`提醒 "${name}" 创建成功`, 'success');

        // 清空表单
        document.getElementById('alertName').value = '';
        document.getElementById('alertValue').value = '';

        // 刷新提醒列表
        this.loadAlerts();
    }

    // 创建散点图（ECharts实现）
    createScatterChart(data, options) {
        const container = document.getElementById('enterpriseChart');
        if (window.enterpriseCharts.charts.has('enterpriseChart')) {
            window.enterpriseCharts.charts.get('enterpriseChart').dispose();
        }

        const chart = echarts.init(container, window.enterpriseCharts.currentTheme);

        const chartOptions = {
            title: {
                text: options.title || '散点图',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    return `${params.name}<br/>X: ${params.value[0]}<br/>Y: ${params.value[1]}`;
                }
            },
            xAxis: {
                type: 'value',
                name: 'X轴',
                splitLine: {
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: 'Y轴',
                splitLine: {
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            },
            series: [{
                name: options.seriesName || '数据点',
                type: 'scatter',
                data: data.map(item => [item.x || item.value, item.y || item.value2]),
                symbolSize: 8,
                itemStyle: {
                    color: '#5470c6',
                    opacity: 0.8
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };

        chart.setOption(chartOptions);
        window.enterpriseCharts.charts.set('enterpriseChart', chart);

        window.addEventListener('resize', () => {
            chart.resize();
        });

        // 图表创建完成后，触发字体保护
        setTimeout(() => {
            this.ensurePageFontIntegrity();
        }, 100);
    }

    // 创建热力图（ECharts实现）
    createHeatmapChart(data, options) {
        const container = document.getElementById('enterpriseChart');
        if (window.enterpriseCharts.charts.has('enterpriseChart')) {
            window.enterpriseCharts.charts.get('enterpriseChart').dispose();
        }

        const chart = echarts.init(container, window.enterpriseCharts.currentTheme);

        // 处理热力图数据
        const heatmapData = data.map(item => [item.x || 0, item.y || 0, item.value || 0]);

        const chartOptions = {
            title: {
                text: options.title || '热力图',
                left: 'center',
                textStyle: {
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                position: 'top',
                formatter: function(params) {
                    return `X: ${params.value[0]}<br/>Y: ${params.value[1]}<br/>值: ${params.value[2]}`;
                }
            },
            grid: {
                height: '50%',
                top: '10%'
            },
            xAxis: {
                type: 'category',
                data: [...new Set(data.map(item => item.x))],
                splitArea: {
                    show: true
                }
            },
            yAxis: {
                type: 'category',
                data: [...new Set(data.map(item => item.y))],
                splitArea: {
                    show: true
                }
            },
            visualMap: {
                min: Math.min(...data.map(item => item.value || 0)),
                max: Math.max(...data.map(item => item.value || 0)),
                calculable: true,
                orient: 'horizontal',
                left: 'center',
                bottom: '15%',
                inRange: {
                    color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
                }
            },
            series: [{
                name: options.seriesName || '热力值',
                type: 'heatmap',
                data: heatmapData,
                label: {
                    show: true
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };

        chart.setOption(chartOptions);
        window.enterpriseCharts.charts.set('enterpriseChart', chart);

        window.addEventListener('resize', () => {
            chart.resize();
        });

        // 图表创建完成后，触发字体保护
        setTimeout(() => {
            this.ensurePageFontIntegrity();
        }, 100);
    }

    // 更改图表主题
    changeChartTheme(theme) {
        window.enterpriseCharts.setTheme(theme);
        this.showNotification(`已切换到${theme}主题`, 'success');

        // 主题切换后确保字体完整性
        setTimeout(() => {
            this.ensurePageFontIntegrity();
        }, 200);
    }

    // 导出当前图表
    exportCurrentChart() {
        window.enterpriseCharts.exportChart('enterpriseChart');
        this.showNotification('图表已导出', 'success');
    }

    // 切换全屏模式
    toggleFullscreenChart() {
        const chartContainer = document.getElementById('enterpriseChart');
        const isFullscreen = chartContainer.classList.contains('chart-fullscreen');

        if (isFullscreen) {
            this.exitFullscreenChart();
        } else {
            this.enterFullscreenChart();
        }
    }

    // 进入全屏模式
    enterFullscreenChart() {
        const chartContainer = document.getElementById('enterpriseChart');
        const wrapper = document.createElement('div');
        wrapper.className = 'chart-fullscreen';
        wrapper.innerHTML = `
            <div class="chart-fullscreen-header">
                <div class="chart-fullscreen-title">图表全屏显示</div>
                <button class="chart-fullscreen-close" onclick="app.exitFullscreenChart()">✕ 退出全屏</button>
            </div>
            <div id="fullscreenChart" class="enterprise-chart-container"></div>
        `;

        document.body.appendChild(wrapper);

        // 复制图表到全屏容器
        const currentChart = window.enterpriseCharts.charts.get('enterpriseChart');
        if (currentChart) {
            const fullscreenChart = echarts.init(document.getElementById('fullscreenChart'), window.enterpriseCharts.currentTheme);
            fullscreenChart.setOption(currentChart.getOption());
            window.enterpriseCharts.charts.set('fullscreenChart', fullscreenChart);
        }

        this.showNotification('已进入全屏模式，按ESC或点击退出按钮退出', 'info');

        // 全屏模式下也确保字体完整性
        setTimeout(() => {
            this.ensurePageFontIntegrity();
        }, 200);

        // 监听ESC键
        document.addEventListener('keydown', this.handleFullscreenEscape.bind(this));
    }

    // 退出全屏模式
    exitFullscreenChart() {
        const fullscreenElement = document.querySelector('.chart-fullscreen');
        if (fullscreenElement) {
            // 销毁全屏图表
            window.enterpriseCharts.destroyChart('fullscreenChart');
            fullscreenElement.remove();
        }

        document.removeEventListener('keydown', this.handleFullscreenEscape.bind(this));
        this.showNotification('已退出全屏模式', 'info');

        // 退出全屏后确保字体完整性
        setTimeout(() => {
            this.ensurePageFontIntegrity();
        }, 200);
    }

    // 处理全屏模式ESC键
    handleFullscreenEscape(event) {
        if (event.key === 'Escape') {
            this.exitFullscreenChart();
        }
    }

    // 调用简化桥接器MCP工具
    async callMCPTool(toolName, params) {
        // 使用简化桥接器的统一端点
        const bridgeUrl = 'http://127.0.0.1:8083/mcp/call-tool';

        const requestData = {
            tool_name: toolName,
            arguments: params
        };

        // 特殊处理图表生成
        if (toolName.includes('chart')) {
            if (toolName === 'generate_bar_chart') {
                requestData.arguments = { ...params, chart_type: 'bar' };
            } else if (toolName === 'generate_pie_chart') {
                requestData.arguments = { ...params, chart_type: 'pie' };
            } else if (toolName === 'generate_line_chart') {
                requestData.arguments = { ...params, chart_type: 'line' };
            } else if (toolName === 'generate_trend_chart') {
                requestData.arguments = { ...params, chart_type: 'line' };
            } else if (toolName === 'generate_scatter_chart') {
                requestData.arguments = { ...params, chart_type: 'scatter' };
            } else if (toolName === 'generate_heatmap_chart') {
                requestData.arguments = { ...params, chart_type: 'heatmap' };
            }
        }

        // 特殊处理SQL执行
        if (toolName === 'execute_custom_sql') {
            requestData.arguments = { query: params.sql };
        }

        const response = await fetch(bridgeUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`简化桥接器响应 (${toolName}):`, data);

        // 简化桥接器返回格式: { result: { ... } }
        const result = data.result || data;

        // 统一返回格式处理
        if (toolName === 'get_database_info') {
            return {
                success: result.success || true,
                database_info: result.database_info || result,
                data: result
            };
        } else if (toolName === 'get_database_statistics') {
            return {
                success: result.success || true,
                data: result.data || result,
                raw: result
            };
        } else if (toolName === 'detect_data_anomalies') {
            return {
                success: result.success || true,
                data: result.data || result,
                raw: result
            };
        } else if (toolName.includes('chart')) {
            return {
                success: result.success || true,
                chart_data: result.chart_data || result,
                data: result
            };
        } else if (toolName === 'analyze_data_trend') {
            return {
                success: result.success || true,
                data: result.data || result,
                analysis: result.analysis,
                forecast: result.forecast,
                raw: result
            };
        } else if (toolName === 'execute_custom_sql') {
            return {
                success: result.success || true,
                data: result.data || result,
                raw: result
            };
        } else {
            // 默认处理
            return {
                success: result.success || true,
                data: result.data || result,
                raw: result
            };
        }
    }

    // 显示加载状态
    showLoading() {
        console.log('🔄 [DEBUG] showLoading() 被调用');
        const overlay = document.getElementById('loadingOverlay');
        console.log('🔄 [DEBUG] loadingOverlay元素:', overlay);
        overlay.classList.add('show');
        console.log('🔄 [DEBUG] 添加show类后的classList:', overlay.classList.toString());
    }

    // 隐藏加载状态
    hideLoading() {
        console.log('🔄 [DEBUG] hideLoading() 被调用');
        const overlay = document.getElementById('loadingOverlay');
        console.log('🔄 [DEBUG] loadingOverlay元素:', overlay);
        overlay.classList.remove('show');
        console.log('🔄 [DEBUG] 移除show类后的classList:', overlay.classList.toString());
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.getElementById('notifications').appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// 应用将在index.html中初始化
