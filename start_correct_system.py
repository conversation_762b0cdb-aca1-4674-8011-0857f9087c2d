#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的启动脚本 - MySQL数据分析系统
基于FastMCP的正确架构实现
"""

import os
import sys
import time
import subprocess
import threading
import signal
import webbrowser
from pathlib import Path

class CorrectSystemManager:
    """正确的系统管理器"""
    
    def __init__(self):
        self.mcp_process = None
        self.bridge_process = None
        self.web_process = None
        self.is_running = False
        self.base_dir = Path(__file__).parent
    
    def print_banner(self):
        """打印启动横幅"""
        print("=" * 70)
        print("    MySQL数据分析系统 - 基于FastMCP的正确架构")
        print("    MCP服务器 -> HTTP桥接器 -> Web客户端")
        print("=" * 70)
    
    def check_requirements(self):
        """检查系统要求"""
        print("检查系统要求...")
        
        # 检查必要文件
        required_files = [
            'mysql_analysis_mcp.py',
            'mcp_http_bridge.py',
            'web_server.py',
            'db_config.json',
            'web_client/index.html',
            'web_client/styles.css',
            'web_client/app.js'
        ]
        
        missing_files = []
        for file in required_files:
            if not (self.base_dir / file).exists():
                missing_files.append(file)
        
        if missing_files:
            print("错误: 缺少必要文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False
        
        print("系统要求检查通过")
        return True
    
    def start_mcp_server(self):
        """启动MCP服务器"""
        print("1. 启动MCP服务器...")

        try:
            # 启动MCP服务器进程
            print(f"   执行命令: {sys.executable} mysql_analysis_mcp.py")
            print(f"   工作目录: {self.base_dir}")

            self.mcp_process = subprocess.Popen(
                [sys.executable, 'mysql_analysis_mcp.py'],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            print(f"   进程ID: {self.mcp_process.pid}")

            # 等待服务器启动
            print("   等待MCP服务器启动...")
            for i in range(8):
                time.sleep(1)
                if self.mcp_process.poll() is not None:
                    print(f"   进程在第{i+1}秒时退出")
                    break
                print(f"   等待中... ({i+1}/8)")

            # 检查进程是否还在运行
            if self.mcp_process.poll() is None:
                print("   ✓ MCP服务器启动成功 (http://127.0.0.1:9000/mcp/)")
                return True
            else:
                print("   ✗ MCP服务器启动失败:")
                try:
                    stdout, stderr = self.mcp_process.communicate(timeout=5)
                    if stdout:
                        print(f"   标准输出: {stdout}")
                    if stderr:
                        print(f"   标准错误: {stderr}")
                except subprocess.TimeoutExpired:
                    print("   无法获取进程输出（超时）")
                return False

        except Exception as e:
            print(f"   ✗ 启动MCP服务器失败: {e}")
            return False
    
    def start_http_bridge(self):
        """启动HTTP桥接器"""
        print("2. 启动HTTP桥接器...")

        try:
            # 启动HTTP桥接器进程
            print(f"   执行命令: {sys.executable} mcp_http_bridge.py")

            self.bridge_process = subprocess.Popen(
                [sys.executable, 'mcp_http_bridge.py'],
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )

            print(f"   进程ID: {self.bridge_process.pid}")

            # 等待桥接器启动
            print("   等待HTTP桥接器启动...")
            for i in range(5):
                time.sleep(1)
                if self.bridge_process.poll() is not None:
                    print(f"   进程在第{i+1}秒时退出")
                    break
                print(f"   等待中... ({i+1}/5)")

            # 检查进程是否还在运行
            if self.bridge_process.poll() is None:
                print("   ✓ HTTP桥接器启动成功 (http://127.0.0.1:8080)")

                # 测试HTTP桥接器连接
                print("   测试HTTP桥接器连接...")
                try:
                    import requests
                    response = requests.get("http://127.0.0.1:8080/", timeout=5)
                    if response.status_code == 200:
                        print("   ✓ HTTP桥接器连接测试成功")
                    else:
                        print(f"   ⚠ HTTP桥接器响应异常: {response.status_code}")
                except Exception as test_e:
                    print(f"   ⚠ HTTP桥接器连接测试失败: {test_e}")

                return True
            else:
                print("   ✗ HTTP桥接器启动失败:")
                try:
                    stdout, stderr = self.bridge_process.communicate(timeout=5)
                    if stdout:
                        print(f"   标准输出: {stdout}")
                    if stderr:
                        print(f"   标准错误: {stderr}")
                except subprocess.TimeoutExpired:
                    print("   无法获取进程输出（超时）")
                return False

        except Exception as e:
            print(f"   ✗ 启动HTTP桥接器失败: {e}")
            return False
    
    def start_web_server(self):
        """启动Web服务器"""
        print("3. 启动Web服务器...")
        
        try:
            # 启动Web服务器进程
            self.web_process = subprocess.Popen(
                [sys.executable, 'web_server.py', '8081'],  # 使用不同端口避免冲突
                cwd=self.base_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # 等待Web服务器启动
            print("   等待Web服务器启动...")
            time.sleep(3)
            
            # 检查进程是否还在运行
            if self.web_process.poll() is None:
                print("   ✓ Web服务器启动成功 (http://127.0.0.1:8081)")
                return True
            else:
                stdout, stderr = self.web_process.communicate()
                print("   ✗ Web服务器启动失败:")
                if stderr:
                    print(f"   错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"   ✗ 启动Web服务器失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        print("4. 打开Web界面...")
        
        try:
            web_url = "http://127.0.0.1:8081"
            webbrowser.open(web_url)
            print(f"   ✓ 浏览器已打开: {web_url}")
        except Exception as e:
            print(f"   ⚠ 无法自动打开浏览器: {e}")
            print("   请手动在浏览器中打开: http://127.0.0.1:8081")
    
    def print_status(self):
        """打印系统状态"""
        print("\n" + "=" * 70)
        print("🎉 MySQL数据分析系统启动完成！")
        print("=" * 70)
        print("\n📋 服务架构:")
        print("   📊 MCP服务器:    http://127.0.0.1:9000/mcp/ (FastMCP)")
        print("   🔗 HTTP桥接器:   http://127.0.0.1:8080 (API转换)")
        print("   🌐 Web界面:      http://127.0.0.1:8081 (用户界面)")
        
        print("\n💡 使用说明:")
        print("   1. Web界面已在浏览器中打开")
        print("   2. 点击'连接服务器'连接到HTTP桥接器")
        print("   3. 开始使用数据分析功能")
        
        print("\n🏗️ 架构说明:")
        print("   • FastMCP服务器提供数据分析功能")
        print("   • HTTP桥接器将MCP协议转换为HTTP API")
        print("   • Web客户端通过HTTP API调用功能")
        print("   • 完全本地部署，数据安全可靠")
        
        print("\n⏹️ 停止系统: 按 Ctrl+C")
        print("=" * 70)
    
    def stop_system(self):
        """停止整个系统"""
        print("\n正在停止系统...")
        
        # 停止Web服务器
        if self.web_process and self.web_process.poll() is None:
            print("   停止Web服务器...")
            self.web_process.terminate()
            try:
                self.web_process.wait(timeout=5)
                print("   ✓ Web服务器已停止")
            except subprocess.TimeoutExpired:
                self.web_process.kill()
                print("   ⚠ 强制停止Web服务器")
        
        # 停止HTTP桥接器
        if self.bridge_process and self.bridge_process.poll() is None:
            print("   停止HTTP桥接器...")
            self.bridge_process.terminate()
            try:
                self.bridge_process.wait(timeout=5)
                print("   ✓ HTTP桥接器已停止")
            except subprocess.TimeoutExpired:
                self.bridge_process.kill()
                print("   ⚠ 强制停止HTTP桥接器")
        
        # 停止MCP服务器
        if self.mcp_process and self.mcp_process.poll() is None:
            print("   停止MCP服务器...")
            self.mcp_process.terminate()
            try:
                self.mcp_process.wait(timeout=5)
                print("   ✓ MCP服务器已停止")
            except subprocess.TimeoutExpired:
                self.mcp_process.kill()
                print("   ⚠ 强制停止MCP服务器")
        
        self.is_running = False
        print("✓ MySQL数据分析系统已完全停止")
    
    def run(self):
        """运行整个系统"""
        self.print_banner()
        
        # 检查系统要求
        if not self.check_requirements():
            print("\n系统要求检查失败，无法启动")
            return False
        
        print("\n启动系统组件...")
        
        # 启动MCP服务器
        if not self.start_mcp_server():
            print("\nMCP服务器启动失败，无法继续")
            return False
        
        # 启动HTTP桥接器
        if not self.start_http_bridge():
            print("\nHTTP桥接器启动失败，停止MCP服务器")
            self.stop_system()
            return False
        
        # 启动Web服务器
        if not self.start_web_server():
            print("\nWeb服务器启动失败，停止所有服务")
            self.stop_system()
            return False
        
        # 打开浏览器
        self.open_browser()
        
        # 打印状态
        self.print_status()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, lambda sig, frame: self.stop_system())
        
        self.is_running = True
        
        try:
            # 保持系统运行
            while self.is_running:
                time.sleep(1)
                
                # 检查进程状态
                if self.mcp_process and self.mcp_process.poll() is not None:
                    print("⚠ MCP服务器意外停止")
                    break
                
                if self.bridge_process and self.bridge_process.poll() is not None:
                    print("⚠ HTTP桥接器意外停止")
                    break
                
                if self.web_process and self.web_process.poll() is not None:
                    print("⚠ Web服务器意外停止")
                    break
                    
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_system()
        
        return True

def main():
    """主函数"""
    try:
        system_manager = CorrectSystemManager()
        success = system_manager.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"系统启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
