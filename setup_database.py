#!/usr/bin/env python3
"""
数据库设置脚本
帮助用户安装和配置MySQL数据库
"""

import os
import sys
import platform
import subprocess
import json

def detect_system():
    """检测操作系统"""
    system = platform.system().lower()
    print(f"🖥️ 检测到操作系统: {system}")
    return system

def install_mysql_windows():
    """Windows系统MySQL安装指南"""
    print("\n🪟 Windows MySQL安装指南")
    print("=" * 40)
    print("1. 下载MySQL安装包:")
    print("   https://dev.mysql.com/downloads/mysql/")
    print("\n2. 选择 'MySQL Installer for Windows'")
    print("\n3. 安装步骤:")
    print("   - 选择 'Developer Default' 安装类型")
    print("   - 设置root密码（建议留空用于测试）")
    print("   - 完成安装")
    print("\n4. 验证安装:")
    print("   打开命令提示符，输入: mysql -u root -p")
    print("\n5. 启动服务:")
    print("   net start mysql")

def install_mysql_linux():
    """Linux系统MySQL安装指南"""
    print("\n🐧 Linux MySQL安装指南")
    print("=" * 40)
    
    # 检测Linux发行版
    try:
        with open('/etc/os-release', 'r') as f:
            os_info = f.read().lower()
        
        if 'ubuntu' in os_info or 'debian' in os_info:
            print("📦 Ubuntu/Debian系统:")
            print("sudo apt update")
            print("sudo apt install mysql-server")
            print("sudo mysql_secure_installation")
            print("sudo systemctl start mysql")
            print("sudo systemctl enable mysql")
            
        elif 'centos' in os_info or 'rhel' in os_info or 'fedora' in os_info:
            print("📦 CentOS/RHEL/Fedora系统:")
            print("sudo yum install mysql-server")
            print("# 或者")
            print("sudo dnf install mysql-server")
            print("sudo systemctl start mysqld")
            print("sudo systemctl enable mysqld")
            
        else:
            print("📦 通用Linux安装:")
            print("请根据您的发行版使用相应的包管理器安装MySQL")
            
    except:
        print("📦 通用Linux安装:")
        print("sudo apt install mysql-server  # Ubuntu/Debian")
        print("sudo yum install mysql-server  # CentOS/RHEL")
    
    print("\n🔧 配置MySQL:")
    print("sudo mysql")
    print("ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '';")
    print("FLUSH PRIVILEGES;")
    print("EXIT;")

def install_mysql_macos():
    """macOS系统MySQL安装指南"""
    print("\n🍎 macOS MySQL安装指南")
    print("=" * 40)
    print("方法1 - 使用Homebrew (推荐):")
    print("brew install mysql")
    print("brew services start mysql")
    print("mysql_secure_installation")
    print("\n方法2 - 官方安装包:")
    print("1. 下载: https://dev.mysql.com/downloads/mysql/")
    print("2. 选择 macOS 版本")
    print("3. 安装 .dmg 文件")
    print("4. 在系统偏好设置中启动MySQL")

def create_sample_database():
    """创建示例数据库和表"""
    print("\n📊 创建示例数据库")
    print("=" * 30)
    
    sql_script = """
-- 创建数据库
CREATE DATABASE IF NOT EXISTS realtime_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE realtime_data;

-- 创建支付表
CREATE TABLE IF NOT EXISTS payment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_payment_date (payment_date),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- 插入示例数据
INSERT INTO payment (user_id, amount, payment_date) VALUES
(1, 100.50, '2024-01-01 10:00:00'),
(2, 250.00, '2024-01-01 11:30:00'),
(3, 75.25, '2024-01-01 14:15:00'),
(1, 500.00, '2024-01-02 09:20:00'),
(4, 125.75, '2024-01-02 16:45:00'),
(2, 300.00, '2024-01-03 12:10:00'),
(5, 50.00, '2024-01-03 18:30:00'),
(3, 1000.00, '2024-01-04 08:00:00'),
(6, 175.50, '2024-01-04 13:25:00'),
(4, 225.25, '2024-01-05 15:40:00');

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- 插入用户数据
INSERT INTO users (username, email) VALUES
('user1', '<EMAIL>'),
('user2', '<EMAIL>'),
('user3', '<EMAIL>'),
('user4', '<EMAIL>'),
('user5', '<EMAIL>'),
('user6', '<EMAIL>');

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    order_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_order_date (order_date),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- 插入订单数据
INSERT INTO orders (user_id, product_name, quantity, unit_price, total_amount, order_date) VALUES
(1, '笔记本电脑', 1, 5999.00, 5999.00, '2024-01-01 10:00:00'),
(2, '手机', 2, 3999.00, 7998.00, '2024-01-01 11:30:00'),
(3, '耳机', 1, 299.00, 299.00, '2024-01-01 14:15:00'),
(1, '键盘', 1, 199.00, 199.00, '2024-01-02 09:20:00'),
(4, '鼠标', 3, 89.00, 267.00, '2024-01-02 16:45:00'),
(2, '显示器', 1, 1999.00, 1999.00, '2024-01-03 12:10:00'),
(5, '音箱', 2, 399.00, 798.00, '2024-01-03 18:30:00'),
(3, '平板电脑', 1, 2999.00, 2999.00, '2024-01-04 08:00:00'),
(6, '充电器', 5, 49.00, 245.00, '2024-01-04 13:25:00'),
(4, '数据线', 10, 19.00, 190.00, '2024-01-05 15:40:00');

SELECT '示例数据库创建完成！' as message;
"""
    
    # 保存SQL脚本
    with open('sample_database.sql', 'w', encoding='utf-8') as f:
        f.write(sql_script)
    
    print("✅ SQL脚本已保存为: sample_database.sql")
    print("\n🔧 执行脚本:")
    print("mysql -u root -p < sample_database.sql")
    print("\n📊 包含的表:")
    print("  - payment: 支付记录表")
    print("  - users: 用户表")
    print("  - orders: 订单表")
    print("\n💡 这些表包含示例数据，可用于测试系统功能")

def update_config():
    """更新配置文件"""
    print("\n⚙️ 更新配置文件")
    print("=" * 25)
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("当前数据库配置:")
        db_config = config.get('database', {})
        print(f"  主机: {db_config.get('host', 'localhost')}")
        print(f"  端口: {db_config.get('port', 3306)}")
        print(f"  用户: {db_config.get('user', 'root')}")
        print(f"  数据库: {db_config.get('database', 'realtime_data')}")
        
        print("\n是否要修改配置？(y/n): ", end="")
        if input().lower() == 'y':
            host = input(f"主机 [{db_config.get('host', 'localhost')}]: ").strip() or db_config.get('host', 'localhost')
            port = input(f"端口 [{db_config.get('port', 3306)}]: ").strip()
            port = int(port) if port else db_config.get('port', 3306)
            user = input(f"用户名 [{db_config.get('user', 'root')}]: ").strip() or db_config.get('user', 'root')
            password = input("密码 (留空表示无密码): ").strip()
            database = input(f"数据库名 [{db_config.get('database', 'realtime_data')}]: ").strip() or db_config.get('database', 'realtime_data')
            
            config['database'].update({
                'host': host,
                'port': port,
                'user': user,
                'password': password,
                'database': database
            })
            
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print("✅ 配置已更新")
        
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")

def main():
    """主函数"""
    print("🗄️ MySQL数据库设置助手")
    print("=" * 40)
    
    system = detect_system()
    
    print("\n📋 选择操作:")
    print("1. 查看MySQL安装指南")
    print("2. 创建示例数据库")
    print("3. 更新配置文件")
    print("4. 全部执行")
    print("0. 退出")
    
    choice = input("\n请选择 (0-4): ").strip()
    
    if choice == "1" or choice == "4":
        if system == "windows":
            install_mysql_windows()
        elif system == "linux":
            install_mysql_linux()
        elif system == "darwin":
            install_mysql_macos()
        else:
            print("❌ 不支持的操作系统")
    
    if choice == "2" or choice == "4":
        create_sample_database()
    
    if choice == "3" or choice == "4":
        update_config()
    
    if choice == "4":
        print("\n🎉 数据库设置完成！")
        print("\n📋 下一步:")
        print("1. 确保MySQL服务正在运行")
        print("2. 执行SQL脚本创建示例数据")
        print("3. 运行: python quick_start.py")
    
    elif choice == "0":
        print("👋 再见！")
    
    else:
        if choice not in ["1", "2", "3", "4"]:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
